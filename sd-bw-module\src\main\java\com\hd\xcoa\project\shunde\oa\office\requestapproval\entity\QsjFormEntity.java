package com.hd.xcoa.project.shunde.oa.office.requestapproval.entity;

import com.hd.wep.extend.common.entity.WeBaseEntity;
import com.hd.wep.extend.model.annotation.FormBean;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.Parameter;

import javax.persistence.*;
import java.io.Serializable;

/**
 * XM_ONL_QSJ_112185953 实体类
 *
 * 版权所有 2013-2020 Smartdot Technologies Co., Ltd. 保留所有权利。
 * SMARTDOT 专有/机密。使用受许可条款约束。
 *
 * @version 1.0, 2025年1月8日
 */
@FormBean(formName = "XM_ONL_QSJ_112185953实体类")
@Entity
@Table(name = "XM_ONL_QSJ_112185953")
public class QsjFormEntity implements Serializable {
    
    
    private static final long serialVersionUID = 1667601797981642220L;
    private Long id;
    private Long instid;
    private String title;
    private String swbh;
    private String attachmentex16;
    private String swdw;
    private String mainsend;
    private String isdispatch;
    private String proposedoptioncreatoraccount;
    private String proposedoptioncreatedeptcode;
    private String hgyjOpinionTextarea;
    private String biaoti;
    private String docnumberjson;
    private String lwdw;
    private String hjcd;
    private String gksx;
    private Long qfrq;
    private Long yfrq;
    private String lwwh;
    private Long swsj;
    private String wjlx;
    private String attachment;
    private String ksnbyj;
    private String ldpsyjOpinionTextarea;
    private String psnbyjOpinionTextarea;
    private String gzbz;
    private Long creatorId;
    private String creatorAccount;
    private String creatorName;
    private Long createDeptId;
    private String createDeptCode;
    private String createDeptName;
    private Long modifyEmpId;
    private String modifyEmpAccount;
    private String modifyEmpName;
    private Long creationTime;
    private Long lastModifiedTime;
    private Integer deleted;
    private Long belongedorgid;
    private Integer flowStatus;
    private String flowStepName;
    private String businesstypeid;
    private String businesstypename;
    private String nbyjOpinionTextarea;
    private String customopiniontextarea24;
    private String clyjnew;
    private String gzbznew;
    
    @Id
    @GeneratedValue(generator = "qsjFormEntityIdGenerator")
    @GenericGenerator(
        name = "qsjFormEntityIdGenerator",
        strategy = "com.hd.rcugrc.data.ids.TableBasedLongIdGenerator",
        parameters = {
            @Parameter(name = "table_name", value = "HD_ID_GEN"),
            @Parameter(name = "segment_column_name", value = "ID_NAME"),
            @Parameter(name = "value_column_name", value = "ID_VAL"),
            @Parameter(name = "segment_value", value = "XM_ONL_QSJ_112185953"),
            @Parameter(name = "increment_size", value = "1")
        }
    )
    @Column(name = "ID", nullable = false)
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @Column(name = "INSTID")
    public Long getInstid() {
        return instid;
    }

    public void setInstid(Long instid) {
        this.instid = instid;
    }

    @Column(name = "TITLE", length = 6000)
    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    @Column(name = "SWBH", length = 600)
    public String getSwbh() {
        return swbh;
    }

    public void setSwbh(String swbh) {
        this.swbh = swbh;
    }

    @Column(name = "ATTACHMENTEX16", length = 50)
    public String getAttachmentex16() {
        return attachmentex16;
    }

    public void setAttachmentex16(String attachmentex16) {
        this.attachmentex16 = attachmentex16;
    }

    @Column(name = "SWDW", length = 600)
    public String getSwdw() {
        return swdw;
    }

    public void setSwdw(String swdw) {
        this.swdw = swdw;
    }

    @Column(name = "MAINSEND", length = 600)
    public String getMainsend() {
        return mainsend;
    }

    public void setMainsend(String mainsend) {
        this.mainsend = mainsend;
    }

    @Column(name = "ISDISPATCH", length = 20)
    public String getIsdispatch() {
        return isdispatch;
    }

    public void setIsdispatch(String isdispatch) {
        this.isdispatch = isdispatch;
    }

    @Column(name = "PROPOSEDOPTIONCREATORACCOUNT", length = 600)
    public String getProposedoptioncreatoraccount() {
        return proposedoptioncreatoraccount;
    }

    public void setProposedoptioncreatoraccount(String proposedoptioncreatoraccount) {
        this.proposedoptioncreatoraccount = proposedoptioncreatoraccount;
    }

    @Column(name = "PROPOSEDOPTIONCREATEDEPTCODE", length = 600)
    public String getProposedoptioncreatedeptcode() {
        return proposedoptioncreatedeptcode;
    }

    public void setProposedoptioncreatedeptcode(String proposedoptioncreatedeptcode) {
        this.proposedoptioncreatedeptcode = proposedoptioncreatedeptcode;
    }

    @Column(name = "CLJG", length = 600)
    public String getCljg() {
        return cljg;
    }

    public void setCljg(String cljg) {
        this.cljg = cljg;
    }

    @Column(name = "HGYJ_OPINION_TEXTAREA", length = 600)
    public String getHgyjOpinionTextarea() {
        return hgyjOpinionTextarea;
    }

    public void setHgyjOpinionTextarea(String hgyjOpinionTextarea) {
        this.hgyjOpinionTextarea = hgyjOpinionTextarea;
    }

    @Column(name = "BIAOTI", length = 6000)
    public String getBiaoti() {
        return biaoti;
    }

    public void setBiaoti(String biaoti) {
        this.biaoti = biaoti;
    }

    @Column(name = "DOCNUMBERJSON", length = 600)
    public String getDocnumberjson() {
        return docnumberjson;
    }

    public void setDocnumberjson(String docnumberjson) {
        this.docnumberjson = docnumberjson;
    }

    @Column(name = "LWDW", length = 600)
    public String getLwdw() {
        return lwdw;
    }

    public void setLwdw(String lwdw) {
        this.lwdw = lwdw;
    }

    @Column(name = "HJCD", length = 600)
    public String getHjcd() {
        return hjcd;
    }

    public void setHjcd(String hjcd) {
        this.hjcd = hjcd;
    }

    @Column(name = "GKSX", length = 20)
    public String getGksx() {
        return gksx;
    }

    public void setGksx(String gksx) {
        this.gksx = gksx;
    }

    @Column(name = "QFRQ")
    public Long getQfrq() {
        return qfrq;
    }

    public void setQfrq(Long qfrq) {
        this.qfrq = qfrq;
    }

    @Column(name = "YFRQ")
    public Long getYfrq() {
        return yfrq;
    }

    public void setYfrq(Long yfrq) {
        this.yfrq = yfrq;
    }

    @Column(name = "LWWH", length = 600)
    public String getLwwh() {
        return lwwh;
    }

    public void setLwwh(String lwwh) {
        this.lwwh = lwwh;
    }

    @Column(name = "SWSJ")
    public Long getSwsj() {
        return swsj;
    }

    public void setSwsj(Long swsj) {
        this.swsj = swsj;
    }

    @Column(name = "WJLX", length = 20)
    public String getWjlx() {
        return wjlx;
    }

    public void setWjlx(String wjlx) {
        this.wjlx = wjlx;
    }

    @Column(name = "ATTACHMENT", length = 50)
    public String getAttachment() {
        return attachment;
    }

    public void setAttachment(String attachment) {
        this.attachment = attachment;
    }

    @Column(name = "KSNBYJ", length = 12000)
    public String getKsnbyj() {
        return ksnbyj;
    }

    public void setKsnbyj(String ksnbyj) {
        this.ksnbyj = ksnbyj;
    }

    @Column(name = "LDPSYJ_OPINION_TEXTAREA", length = 600)
    public String getLdpsyjOpinionTextarea() {
        return ldpsyjOpinionTextarea;
    }

    public void setLdpsyjOpinionTextarea(String ldpsyjOpinionTextarea) {
        this.ldpsyjOpinionTextarea = ldpsyjOpinionTextarea;
    }

    @Column(name = "PSNBYJ_OPINION_TEXTAREA", length = 600)
    public String getPsnbyjOpinionTextarea() {
        return psnbyjOpinionTextarea;
    }

    public void setPsnbyjOpinionTextarea(String psnbyjOpinionTextarea) {
        this.psnbyjOpinionTextarea = psnbyjOpinionTextarea;
    }


    @Column(name = "GZBZ", length = 600)
    public String getGzbz() {
        return gzbz;
    }

    public void setGzbz(String gzbz) {
        this.gzbz = gzbz;
    }

    @Column(name = "CREATOR_ID")
    public Long getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    @Column(name = "CREATOR_ACCOUNT", length = 32)
    public String getCreatorAccount() {
        return creatorAccount;
    }

    public void setCreatorAccount(String creatorAccount) {
        this.creatorAccount = creatorAccount;
    }

    @Column(name = "CREATOR_NAME", length = 50)
    public String getCreatorName() {
        return creatorName;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }

    @Column(name = "CREATE_DEPT_ID")
    public Long getCreateDeptId() {
        return createDeptId;
    }

    public void setCreateDeptId(Long createDeptId) {
        this.createDeptId = createDeptId;
    }

    @Column(name = "CREATE_DEPT_CODE", length = 255)
    public String getCreateDeptCode() {
        return createDeptCode;
    }

    public void setCreateDeptCode(String createDeptCode) {
        this.createDeptCode = createDeptCode;
    }

    @Column(name = "CREATE_DEPT_NAME", length = 255)
    public String getCreateDeptName() {
        return createDeptName;
    }

    public void setCreateDeptName(String createDeptName) {
        this.createDeptName = createDeptName;
    }

    @Column(name = "MODIFY_EMP_ID")
    public Long getModifyEmpId() {
        return modifyEmpId;
    }

    public void setModifyEmpId(Long modifyEmpId) {
        this.modifyEmpId = modifyEmpId;
    }

    @Column(name = "MODIFY_EMP_ACCOUNT", length = 32)
    public String getModifyEmpAccount() {
        return modifyEmpAccount;
    }

    public void setModifyEmpAccount(String modifyEmpAccount) {
        this.modifyEmpAccount = modifyEmpAccount;
    }

    @Column(name = "MODIFY_EMP_NAME", length = 50)
    public String getModifyEmpName() {
        return modifyEmpName;
    }

    public void setModifyEmpName(String modifyEmpName) {
        this.modifyEmpName = modifyEmpName;
    }

    @Column(name = "CREATION_TIME")
    public Long getCreationTime() {
        return creationTime;
    }

    public void setCreationTime(Long creationTime) {
        this.creationTime = creationTime;
    }

    @Column(name = "LAST_MODIFIED_TIME")
    public Long getLastModifiedTime() {
        return lastModifiedTime;
    }

    public void setLastModifiedTime(Long lastModifiedTime) {
        this.lastModifiedTime = lastModifiedTime;
    }

    @Column(name = "DELETED")
    public Integer getDeleted() {
        return deleted;
    }

    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }

    @Column(name = "BELONGEDORGID")
    public Long getBelongedorgid() {
        return belongedorgid;
    }

    public void setBelongedorgid(Long belongedorgid) {
        this.belongedorgid = belongedorgid;
    }

    @Column(name = "FLOW_STATUS")
    public Integer getFlowStatus() {
        return flowStatus;
    }

    public void setFlowStatus(Integer flowStatus) {
        this.flowStatus = flowStatus;
    }

    @Column(name = "FLOW_STEP_NAME", length = 50)
    public String getFlowStepName() {
        return flowStepName;
    }

    public void setFlowStepName(String flowStepName) {
        this.flowStepName = flowStepName;
    }

    @Column(name = "BUSINESSTYPEID", length = 30)
    public String getBusinesstypeid() {
        return businesstypeid;
    }

    public void setBusinesstypeid(String businesstypeid) {
        this.businesstypeid = businesstypeid;
    }

    @Column(name = "BUSINESSTYPENAME", length = 100)
    public String getBusinesstypename() {
        return businesstypename;
    }

    public void setBusinesstypename(String businesstypename) {
        this.businesstypename = businesstypename;
    }

    @Column(name = "NBYJ_OPINION_TEXTAREA", length = 600)
    public String getNbyjOpinionTextarea() {
        return nbyjOpinionTextarea;
    }

    public void setNbyjOpinionTextarea(String nbyjOpinionTextarea) {
        this.nbyjOpinionTextarea = nbyjOpinionTextarea;
    }

    @Column(name = "CUSTOMOPINIONTEXTAREA24", length = 600)
    public String getCustomopiniontextarea24() {
        return customopiniontextarea24;
    }

    public void setCustomopiniontextarea24(String customopiniontextarea24) {
        this.customopiniontextarea24 = customopiniontextarea24;
    }

    @Column(name = "CLYJNEW", length = 600)
    public String getClyjnew() {
        return clyjnew;
    }

    public void setClyjnew(String clyjnew) {
        this.clyjnew = clyjnew;
    }

    @Column(name = "GZBZNEW", length = 600)
    public String getGzbznew() {
        return gzbznew;
    }

    public void setGzbznew(String gzbznew) {
        this.gzbznew = gzbznew;
    }
}
