/** 
*北京慧点科技有限公司XCOA产品。
*注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的。 
*@Date: 2022年9月7日
*@Copyright: Copyright 2022 © Smartdot Technologies Co., Ltd.
*/
package com.hd.rcugrc.bpm.facade.service;

import java.util.Map;

import com.hd.rcugrc.bpm.User;

/**
 * 通知处理人公共服务接口
 * <AUTHOR>
 * @since  1.0, 2022年9月7日
 */
public interface WeNotifyHandlerService {
    
    /**
     * 通知处理人服务
     * @param receiveUsers 处理人
     * @param caller      调用者
     * @param context     上下文 
     */
    public void send(User[] receiveUsers, User caller, long stepId, Map<String, Object> context);

}

