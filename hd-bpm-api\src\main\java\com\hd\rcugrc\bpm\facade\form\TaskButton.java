/*
 * Copyright 2013-2019 Smartdot Technologies Co., Ltd. All rights reserved.
 * SMARTDOT PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 */
package com.hd.rcugrc.bpm.facade.form;

import java.io.Serializable;
import java.util.Map;

import com.hd.rcugrc.bpm.facade.RollbackStep;

import io.swagger.v3.oas.annotations.media.Schema;


/**
 * <p>
 * 任务操作按钮相关信息，主要用于Restful
 * 
 * <AUTHOR> href="mailto:<EMAIL>">songjj</a>
 * @version 5.3, 2019年4月10日
 */
@Schema(title = "任务按钮", description = "包含转办、沟通、加减签、知会、退回、关注")
public class TaskButton implements Serializable {

    private static final long serialVersionUID = -6179183253636550277L;

    private String buttonId;

    private String buttonName;

    private Map<String, String> attrs;

    private RollbackStep[] rollbackSteps;

    /**
     * 返回可驳回环节列表， 配合驳回按钮使用
     * 
     * @return 可驳回环节列表
     */
    @Schema(title = "可驳回环节列表， 配合驳回按钮使用", description = "可驳回环节列表， 配合驳回按钮使用")
    public RollbackStep[] getRollbackSteps() {
        return rollbackSteps;
    }

    /**
     * 设置可驳回环节列表
     * 
     * @param rollbackSteps 可驳回环节列表
     */
    public void setRollbackSteps(RollbackStep[] rollbackSteps) {
        this.rollbackSteps = rollbackSteps;
    }

    /**
     * 获取任务ID，主要用于处理流程任务使用
     * 
     * @return 操作ID
     */
    @Schema(title = "操作ID，主要用于处理流程任务使用", description = "操作ID，主要用于处理流程任务使用", example = "save_tjFlow.1")
    public String getButtonId() {
        return buttonId;
    }

    /**
     * 设置任务按钮ID
     * 
     * @param buttonId 操作ID
     */
    public void setButtonId(String buttonId) {
        this.buttonId = buttonId;
    }

    /**
     * 获取按钮名称，主要用于在操作页面显示并区分操作按钮含义
     * 
     * @return 按钮名称
     */
    @Schema(title = "按钮名称，主要用于在操作页面显示并区分操作按钮含义", description = "按钮名称，主要用于在操作页面显示并区分操作按钮含义", example = "保存")
    public String getButtonName() {
        return buttonName;
    }

    /**
     * 设置 按钮名称
     * 
     * @param buttonName 按钮名称
     */
    public void setButtonName(String buttonName) {
        this.buttonName = buttonName;
    }

    /**
     * 任务按钮的扩展属性
     * 
     * @return
     */
    @Schema(title = "任务按钮的扩展属性", description = "任务按钮的扩展属性")
    public Map<String, String> getAttrs() {
        return attrs;
    }

    public void setAttrs(Map<String, String> attrs) {
        this.attrs = attrs;
    }
}
