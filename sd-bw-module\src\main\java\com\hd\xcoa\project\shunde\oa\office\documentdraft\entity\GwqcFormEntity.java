package com.hd.xcoa.project.shunde.oa.office.documentdraft.entity;

import com.hd.wep.extend.common.entity.WeBaseEntity;
import com.hd.wep.extend.model.annotation.FormBean;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.Parameter;

import javax.persistence.*;
import java.io.Serializable;

/**
 * XM_ONL_GWQC_110362632 实体类
 *
 * 版权所有 2013-2020 Smartdot Technologies Co., Ltd. 保留所有权利。
 * SMARTDOT 专有/机密。使用受许可条款约束。
 *
 * @version 1.0, 2025年1月8日
 */
@FormBean(formName = "XM_ONL_GWQC_110362632实体类")
@Entity
@Table(name = "XM_ONL_GWQC_110362632")
public class GwqcFormEntity implements Serializable {
    
    
    private static final long serialVersionUID = 8106171817407997045L;
    private Long id;
    private Long instid;
    private String yzjg;
    private String wjbh;
    private String zsdw;
    private String title;
    private Integer number30;
    private String text34;
    private Long fwsj;
    private Integer qsn;
    private String qpr;
    private Long qcrq;
    private Long fwrq;
    private String gwqcfwlx;
    private String ksyjuser;
    private String ksyjdept;
    private Long ksyjdate;
    private String sflhfw;
    private String xlk1;
    private String csdw;
    private String ywlxid;
    private String proposedoptioncreatoraccount;
    private String proposedoptioncreatedeptcode;
    private String ksnbyj;
    private String isdispatch;
    private String biaoti;
    private String jjcd;
    private String gksx;
    private String docnumberjson;
    private String qsnxlk;
    private Integer qsq;
    private Integer qszq;
    private Integer qsn2;
    private Integer qsq2;
    private Integer qszq2;
    private Integer qsn3;
    private Integer qsq3;
    private String wjt;
    private Long qpsj;
    private String zsdwzdy;
    private String csdwzdy;
    private String xlk;
    private String xlk2;
    private Long yfrq;
    private Integer copyNumber;
    private String sendingRange;
    private String lxdh;
    private String attachment;
    private String nbblyj;
    private String snbyj;
    private String bgslbyjOpinionTextarea;
    private String ldqfyjOpinionTextarea;
    private String gzbz;
    private Long creatorId;
    private String creatorAccount;
    private String creatorName;
    private Long createDeptId;
    private String createDeptCode;
    private String createDeptName;
    private Long modifyEmpId;
    private String modifyEmpAccount;
    private String modifyEmpName;
    private Long creationTime;
    private Long lastModifiedTime;
    private Integer deleted;
    private Long belongedorgid;
    private Integer flowStatus;
    private String flowStepName;
    private String businesstypeid;
    private String businesstypename;
    private String psnbyjOpinionTextarea;
    private String nbyjOpinionTextarea;
    
    @Id
    @GeneratedValue(generator = "gwqcFormEntityIdGenerator")
    @GenericGenerator(
        name = "gwqcFormEntityIdGenerator",
        strategy = "com.hd.rcugrc.data.ids.TableBasedLongIdGenerator",
        parameters = {
            @Parameter(name = "table_name", value = "HD_ID_GEN"),
            @Parameter(name = "segment_column_name", value = "ID_NAME"),
            @Parameter(name = "value_column_name", value = "ID_VAL"),
            @Parameter(name = "segment_value", value = "XM_ONL_GWQC_110362632"),
            @Parameter(name = "increment_size", value = "1")
        }
    )
    @Column(name = "ID", nullable = false)
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @Column(name = "INSTID")
    public Long getInstid() {
        return instid;
    }

    public void setInstid(Long instid) {
        this.instid = instid;
    }

    @Column(name = "YZJG", length = 600)
    public String getYzjg() {
        return yzjg;
    }

    public void setYzjg(String yzjg) {
        this.yzjg = yzjg;
    }

    @Column(name = "WJBH", length = 600)
    public String getWjbh() {
        return wjbh;
    }

    public void setWjbh(String wjbh) {
        this.wjbh = wjbh;
    }

    @Column(name = "ZSDW", length = 600)
    public String getZsdw() {
        return zsdw;
    }

    public void setZsdw(String zsdw) {
        this.zsdw = zsdw;
    }

    @Column(name = "TITLE", length = 6000)
    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    @Column(name = "NUMBER30")
    public Integer getNumber30() {
        return number30;
    }

    public void setNumber30(Integer number30) {
        this.number30 = number30;
    }

    @Column(name = "TEXT34", length = 600)
    public String getText34() {
        return text34;
    }

    public void setText34(String text34) {
        this.text34 = text34;
    }

    @Column(name = "FWSJ")
    public Long getFwsj() {
        return fwsj;
    }

    public void setFwsj(Long fwsj) {
        this.fwsj = fwsj;
    }

    @Column(name = "QSN")
    public Integer getQsn() {
        return qsn;
    }

    public void setQsn(Integer qsn) {
        this.qsn = qsn;
    }

    @Column(name = "QPR")
    public String getQpr() {
        return qpr;
    }

    public void setQpr(String qpr) {
        this.qpr = qpr;
    }

    @Column(name = "QCRQ")
    public Long getQcrq() {
        return qcrq;
    }

    public void setQcrq(Long qcrq) {
        this.qcrq = qcrq;
    }

    @Column(name = "FWRQ")
    public Long getFwrq() {
        return fwrq;
    }

    public void setFwrq(Long fwrq) {
        this.fwrq = fwrq;
    }

    @Column(name = "GWQCFWLX", length = 20)
    public String getGwqcfwlx() {
        return gwqcfwlx;
    }

    public void setGwqcfwlx(String gwqcfwlx) {
        this.gwqcfwlx = gwqcfwlx;
    }

    @Column(name = "KSYJUSER", length = 600)
    public String getKsyjuser() {
        return ksyjuser;
    }

    public void setKsyjuser(String ksyjuser) {
        this.ksyjuser = ksyjuser;
    }

    @Column(name = "KSYJDEPT", length = 600)
    public String getKsyjdept() {
        return ksyjdept;
    }

    public void setKsyjdept(String ksyjdept) {
        this.ksyjdept = ksyjdept;
    }

    @Column(name = "KSYJDATE")
    public Long getKsyjdate() {
        return ksyjdate;
    }

    public void setKsyjdate(Long ksyjdate) {
        this.ksyjdate = ksyjdate;
    }

    @Column(name = "SFLHFW", length = 20)
    public String getSflhfw() {
        return sflhfw;
    }

    public void setSflhfw(String sflhfw) {
        this.sflhfw = sflhfw;
    }

    @Column(name = "XLK1", length = 600)
    public String getXlk1() {
        return xlk1;
    }

    public void setXlk1(String xlk1) {
        this.xlk1 = xlk1;
    }

    @Column(name = "CSDW", length = 600)
    public String getCsdw() {
        return csdw;
    }

    public void setCsdw(String csdw) {
        this.csdw = csdw;
    }

    @Column(name = "YWLXID", length = 600)
    public String getYwlxid() {
        return ywlxid;
    }

    public void setYwlxid(String ywlxid) {
        this.ywlxid = ywlxid;
    }

    @Column(name = "PROPOSEDOPTIONCREATORACCOUNT", length = 600)
    public String getProposedoptioncreatoraccount() {
        return proposedoptioncreatoraccount;
    }

    public void setProposedoptioncreatoraccount(String proposedoptioncreatoraccount) {
        this.proposedoptioncreatoraccount = proposedoptioncreatoraccount;
    }

    @Column(name = "PROPOSEDOPTIONCREATEDEPTCODE", length = 600)
    public String getProposedoptioncreatedeptcode() {
        return proposedoptioncreatedeptcode;
    }

    public void setProposedoptioncreatedeptcode(String proposedoptioncreatedeptcode) {
        this.proposedoptioncreatedeptcode = proposedoptioncreatedeptcode;
    }

    @Column(name = "KSNBYJ", length = 12000)
    public String getKsnbyj() {
        return ksnbyj;
    }

    public void setKsnbyj(String ksnbyj) {
        this.ksnbyj = ksnbyj;
    }

    @Column(name = "ISDISPATCH", length = 20)
    public String getIsdispatch() {
        return isdispatch;
    }

    public void setIsdispatch(String isdispatch) {
        this.isdispatch = isdispatch;
    }

    @Column(name = "BIAOTI", length = 6000)
    public String getBiaoti() {
        return biaoti;
    }

    public void setBiaoti(String biaoti) {
        this.biaoti = biaoti;
    }

    @Column(name = "JJCD", length = 600)
    public String getJjcd() {
        return jjcd;
    }

    public void setJjcd(String jjcd) {
        this.jjcd = jjcd;
    }

    @Column(name = "GKSX", length = 20)
    public String getGksx() {
        return gksx;
    }

    public void setGksx(String gksx) {
        this.gksx = gksx;
    }

    @Column(name = "DOCNUMBERJSON", length = 600)
    public String getDocnumberjson() {
        return docnumberjson;
    }

    public void setDocnumberjson(String docnumberjson) {
        this.docnumberjson = docnumberjson;
    }

    @Column(name = "QSNXLK", length = 60)
    public String getQsnxlk() {
        return qsnxlk;
    }

    public void setQsnxlk(String qsnxlk) {
        this.qsnxlk = qsnxlk;
    }

    @Column(name = "QSQ")
    public Integer getQsq() {
        return qsq;
    }

    public void setQsq(Integer qsq) {
        this.qsq = qsq;
    }

    @Column(name = "QSZQ")
    public Integer getQszq() {
        return qszq;
    }

    public void setQszq(Integer qszq) {
        this.qszq = qszq;
    }

    @Column(name = "QSN2")
    public Integer getQsn2() {
        return qsn2;
    }

    public void setQsn2(Integer qsn2) {
        this.qsn2 = qsn2;
    }

    @Column(name = "QSQ2")
    public Integer getQsq2() {
        return qsq2;
    }

    public void setQsq2(Integer qsq2) {
        this.qsq2 = qsq2;
    }

    @Column(name = "QSZQ2")
    public Integer getQszq2() {
        return qszq2;
    }

    public void setQszq2(Integer qszq2) {
        this.qszq2 = qszq2;
    }

    @Column(name = "QSN3")
    public Integer getQsn3() {
        return qsn3;
    }

    public void setQsn3(Integer qsn3) {
        this.qsn3 = qsn3;
    }

    @Column(name = "QSQ3")
    public Integer getQsq3() {
        return qsq3;
    }

    public void setQsq3(Integer qsq3) {
        this.qsq3 = qsq3;
    }

    @Column(name = "WJT", length = 600)
    public String getWjt() {
        return wjt;
    }

    public void setWjt(String wjt) {
        this.wjt = wjt;
    }

    @Column(name = "QPSJ")
    public Long getQpsj() {
        return qpsj;
    }

    public void setQpsj(Long qpsj) {
        this.qpsj = qpsj;
    }

    @Column(name = "ZSDWZDY", length = 600)
    public String getZsdwzdy() {
        return zsdwzdy;
    }

    public void setZsdwzdy(String zsdwzdy) {
        this.zsdwzdy = zsdwzdy;
    }

    @Column(name = "CSDWZDY", length = 600)
    public String getCsdwzdy() {
        return csdwzdy;
    }

    public void setCsdwzdy(String csdwzdy) {
        this.csdwzdy = csdwzdy;
    }

    @Column(name = "XLK", length = 20)
    public String getXlk() {
        return xlk;
    }

    public void setXlk(String xlk) {
        this.xlk = xlk;
    }

    @Column(name = "XLK2", length = 600)
    public String getXlk2() {
        return xlk2;
    }

    public void setXlk2(String xlk2) {
        this.xlk2 = xlk2;
    }

    @Column(name = "YFRQ")
    public Long getYfrq() {
        return yfrq;
    }

    public void setYfrq(Long yfrq) {
        this.yfrq = yfrq;
    }

    @Column(name = "COPY_NUMBER")
    public Integer getCopyNumber() {
        return copyNumber;
    }

    public void setCopyNumber(Integer copyNumber) {
        this.copyNumber = copyNumber;
    }

    @Column(name = "SENDING_RANGE")
    public String getSendingRange() {
        return sendingRange;
    }

    public void setSendingRange(String sendingRange) {
        this.sendingRange = sendingRange;
    }

    @Column(name = "LXDH")
    public String getLxdh() {
        return lxdh;
    }

    public void setLxdh(String lxdh) {
        this.lxdh = lxdh;
    }

    @Column(name = "ATTACHMENT")
    public String getAttachment() {
        return attachment;
    }

    public void setAttachment(String attachment) {
        this.attachment = attachment;
    }

    @Column(name = "NBBLYJ")
    public String getNbblyj() {
        return nbblyj;
    }

    public void setNbblyj(String nbblyj) {
        this.nbblyj = nbblyj;
    }

    @Column(name = "SNBYJ")
    public String getSnbyj() {
        return snbyj;
    }

    public void setSnbyj(String snbyj) {
        this.snbyj = snbyj;
    }

    @Column(name = "BGSLDYJ_OPINION_TEXTAREA")
    public String getBgslbyjOpinionTextarea() {
        return bgslbyjOpinionTextarea;
    }

    public void setBgslbyjOpinionTextarea(String bgslbyjOpinionTextarea) {
        this.bgslbyjOpinionTextarea = bgslbyjOpinionTextarea;
    }

    @Column(name = "LDQFYJ_OPINION_TEXTAREA")
    public String getLdqfyjOpinionTextarea() {
        return ldqfyjOpinionTextarea;
    }

    public void setLdqfyjOpinionTextarea(String ldqfyjOpinionTextarea) {
        this.ldqfyjOpinionTextarea = ldqfyjOpinionTextarea;
    }

    @Column(name = "GZBZ")
    public String getGzbz() {
        return gzbz;
    }

    public void setGzbz(String gzbz) {
        this.gzbz = gzbz;
    }

    @Column(name = "CREATOR_ID")
    public Long getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    @Column(name = "CREATOR_ACCOUNT")
    public String getCreatorAccount() {
        return creatorAccount;
    }

    public void setCreatorAccount(String creatorAccount) {
        this.creatorAccount = creatorAccount;
    }

    @Column(name = "CREATOR_NAME")
    public String getCreatorName() {
        return creatorName;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }

    @Column(name = "CREATE_DEPT_ID")
    public Long getCreateDeptId() {
        return createDeptId;
    }

    public void setCreateDeptId(Long createDeptId) {
        this.createDeptId = createDeptId;
    }

    @Column(name = "CREATE_DEPT_CODE")
    public String getCreateDeptCode() {
        return createDeptCode;
    }

    public void setCreateDeptCode(String createDeptCode) {
        this.createDeptCode = createDeptCode;
    }

    @Column(name = "CREATE_DEPT_NAME")
    public String getCreateDeptName() {
        return createDeptName;
    }

    public void setCreateDeptName(String createDeptName) {
        this.createDeptName = createDeptName;
    }

    @Column(name = "MODIFY_EMP_ID")
    public Long getModifyEmpId() {
        return modifyEmpId;
    }

    public void setModifyEmpId(Long modifyEmpId) {
        this.modifyEmpId = modifyEmpId;
    }

    @Column(name = "MODIFY_EMP_ACCOUNT")
    public String getModifyEmpAccount() {
        return modifyEmpAccount;
    }

    public void setModifyEmpAccount(String modifyEmpAccount) {
        this.modifyEmpAccount = modifyEmpAccount;
    }

    @Column(name = "MODIFY_EMP_NAME")
    public String getModifyEmpName() {
        return modifyEmpName;
    }

    public void setModifyEmpName(String modifyEmpName) {
        this.modifyEmpName = modifyEmpName;
    }

    @Column(name = "CREATION_TIME")
    public Long getCreationTime() {
        return creationTime;
    }

    public void setCreationTime(Long creationTime) {
        this.creationTime = creationTime;
    }

    @Column(name = "LAST_MODIFIED_TIME")
    public Long getLastModifiedTime() {
        return lastModifiedTime;
    }

    public void setLastModifiedTime(Long lastModifiedTime) {
        this.lastModifiedTime = lastModifiedTime;
    }

    @Column(name = "DELETED")
    public Integer getDeleted() {
        return deleted;
    }

    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }

    @Column(name = "BELONGEDORGID")
    public Long getBelongedorgid() {
        return belongedorgid;
    }

    public void setBelongedorgid(Long belongedorgid) {
        this.belongedorgid = belongedorgid;
    }

    @Column(name = "FLOW_STATUS")
    public Integer getFlowStatus() {
        return flowStatus;
    }

    public void setFlowStatus(Integer flowStatus) {
        this.flowStatus = flowStatus;
    }

    @Column(name = "FLOW_STEP_NAME")
    public String getFlowStepName() {
        return flowStepName;
    }

    public void setFlowStepName(String flowStepName) {
        this.flowStepName = flowStepName;
    }

    @Column(name = "BUSINESSTYPEID")
    public String getBusinesstypeid() {
        return businesstypeid;
    }

    public void setBusinesstypeid(String businesstypeid) {
        this.businesstypeid = businesstypeid;
    }

    @Column(name = "BUSINESSTYPENAME")
    public String getBusinesstypename() {
        return businesstypename;
    }

    public void setBusinesstypename(String businesstypename) {
        this.businesstypename = businesstypename;
    }

    @Column(name = "PSNBYJ_OPINION_TEXTAREA")
    public String getPsnbyjOpinionTextarea() {
        return psnbyjOpinionTextarea;
    }

    public void setPsnbyjOpinionTextarea(String psnbyjOpinionTextarea) {
        this.psnbyjOpinionTextarea = psnbyjOpinionTextarea;
    }

    @Column(name = "NBYJ_OPINION_TEXTAREA")
    public String getNbyjOpinionTextarea() {
        return nbyjOpinionTextarea;
    }

    public void setNbyjOpinionTextarea(String nbyjOpinionTextarea) {
        this.nbyjOpinionTextarea = nbyjOpinionTextarea;
    }

}
