/*
 * Copyright 2013-2019 Smartdot Technologies Co., Ltd. All rights reserved.
 * SMARTDOT PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 */
package com.hd.rcugrc.bpm.facade.exception;

import com.hd.rcugrc.bpm.web.exception.rest.CustomServerErrorException;

/**
 * <p>
 * 流程实例管理服务，发生异常时抛出
 * <AUTHOR> href="mailto:<EMAIL>">yanglx</a>
 * @version 1.0, 2021年6月9日
 */
public class ProcessManagerException extends CustomServerErrorException {

    private static final long serialVersionUID = 4983661518839848309L;

    public ProcessManagerException(String msg) {
        super(msg);
    }
    
    public ProcessManagerException(String msg, Throwable cause) {
        super(msg, cause);
    }
    
    public ProcessManagerException(String code, String msg) {
        super(code, msg);
    }
    
    public ProcessManagerException(String code, String msg, Throwable cause) {
        super(code, msg, cause);
    }
}

