/*
 * Copyright 2013-2019 Smartdot Technologies Co., Ltd. All rights reserved.
 * SMARTDOT PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 */
package com.hd.rcugrc.bpm.facade.exception;

/**
 * <p>
 * 流程任务类异常，当流程实例转办、沟通、知会、关注等，发生异常时抛出
 * <AUTHOR> href="mailto:<EMAIL>">pandy</a>
 * @version 1.0, 2019年3月14日
 */
public class TaskProcessException extends FacadeException {
    
    private String errors;
    private String context;

    private static final long serialVersionUID = 8846428625904017475L;

    public TaskProcessException() {
        super();
    }

    public TaskProcessException(int code, String message) {
        super(message);
        setCode(code);
    }
    
    public TaskProcessException(int code, String message, Throwable cause) {
        super(message, cause);
        setCode(code);
    }
    
    public TaskProcessException(String message, Throwable cause) {
        super(message, cause);
    }

    public TaskProcessException(String message) {
        super(message);
    }

    public TaskProcessException(Throwable cause) {
        super(cause);
    }
    
    public TaskProcessException(int code, String msg, String context) {
        this(code, msg);
        this.setContext(context);
    }
    
    public TaskProcessException(int code, String msg, String context, Throwable cause) {
        this(code, msg, cause);
        this.setContext(context);
    }
    
    public String getErrors() {
        return errors;
    }
    
    public void setErrors(String errors) {
        this.errors = errors;
    }
    
    public String getContext() {
        return context;
    }
    
    public void setContext(String context) {
        this.context = context;
    }
}

