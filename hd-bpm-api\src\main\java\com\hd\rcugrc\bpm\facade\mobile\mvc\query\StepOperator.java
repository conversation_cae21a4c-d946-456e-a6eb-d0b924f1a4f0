/*
 * Copyright 2013-2020 Smartdot Technologies Co., Ltd. All rights reserved.
 * SMARTDOT PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 */
package com.hd.rcugrc.bpm.facade.mobile.mvc.query;

import java.io.Serializable;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <p> 环节参与者集合
 *
 * <AUTHOR> href="mailto:<EMAIL>">yanglx</a>
 * @version 1.0, 2020年10月15日
 */
@Schema(title = "stepOperators", description = "环节参与者集合")
public class StepOperator implements Serializable {
    private static final long serialVersionUID = -1938423775354354L;
    private long stepOperatorId;
    private OperatorBean[] operators;
    
    /**
     * 获取环节参与者ID
     *
     * @return 环节参与者ID
     */
    @Schema(title = "环节参与者ID", description = "环节参与者ID", required = true, nullable = false, example = "25")
    public long getStepOperatorId() {
        return stepOperatorId;
    }
    
    /**
     * 设置环节参与者ID
     *
     * @param stepOperatorId 环节参与者ID
     */
    public void setStepOperatorId(long stepOperatorId) {
        this.stepOperatorId = stepOperatorId;
    }
    
    /**
     * 返回待减签人员列表,目前仅支持操作者类型为用户
     *
     * @return 流程实例创建者
     */
    @Schema(title = "流程实例待减签人员列表,如果是第三方系统建议和GRCv5进行同步系统用户,目前仅支持操作人员类型为用户",
            description = "流程实例待减签人员列表,如果是第三方系统建议和GRCv5进行同步系统用户,目前仅支持操作人员类型为用户", required = true)
    public OperatorBean[] getOperators() {
        return operators;
    }
    
    /**
     * 设置待减签人员列表 ,目前仅支持操作者类型为用户
     *
     * @param operators 流程实例待处理人员列表（减签人员）
     */
    public void setOperators(OperatorBean[] operators) {
        this.operators = operators;
    }
}


