/*
 * Copyright 2013-2019 Smartdot Technologies Co., Ltd. All rights reserved.
 * SMARTDOT PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 */
package com.hd.rcugrc.bpm.facade.exception;

/**
 * <p>
 * 驱动流程类异常，当流程实例保存，提交，驳回，转办等，发生异常时抛出
 * <AUTHOR> href="mailto:<EMAIL>">pandy</a>
 * @version 1.0, 2019年3月14日
 */
public class ProcessDrivenException extends FacadeException {
    
    private String errors;
    private String context;

    /**
     * 
     */
    private static final long serialVersionUID = 8976252712154995922L;

    public ProcessDrivenException() {
        super();
    }

    public ProcessDrivenException(int code, String message) {
        super(message);
        setCode(code);
    }
    
    public ProcessDrivenException(int code, String message, Throwable cause) {
        super(message, cause);
        setCode(code);
    }
    
    public ProcessDrivenException(String message, Throwable cause) {
        super(message, cause);
    }

    public ProcessDrivenException(String message) {
        super(message);
    }

    public ProcessDrivenException(Throwable cause) {
        super(cause);
    }
    
    public ProcessDrivenException(int code, String msg, String context) {
        this(code, msg);
        this.setContext(context);
    }
    
    public ProcessDrivenException(int code, String msg, String context, Throwable cause) {
        this(code, msg, cause);
        this.setContext(context);
    }
    
    public ProcessDrivenException(int code, String msg, String context, String errors) {
        this(code, msg);
        this.setContext(context);
        this.setErrors(errors);
    }
    
    public ProcessDrivenException(int code, String msg, String context, String errors, Throwable cause) {
        this(code, msg, cause);
        this.setContext(context);
        this.setErrors(errors);
    }
    
    public String getErrors() {
        return errors;
    }
    
    public void setErrors(String errors) {
        this.errors = errors;
    }
    
    public String getContext() {
        return context;
    }
    
    public void setContext(String context) {
        this.context = context;
    }
}

