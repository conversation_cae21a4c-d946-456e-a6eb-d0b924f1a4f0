/*
 * Copyright 2013-2020 Smartdot Technologies Co., Ltd. All rights reserved.
 * SMARTDOT PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 */
package com.hd.rcugrc.bpm.facade;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <p>返回环节中尚未参加处理人员信息 主要用于并行环节多人处理可减签人员列表展示使用
 * 
 * <AUTHOR> href="mailto:<EMAIL>">yanglx</a>
 * @version 1.0, 2020年11月26日
 */
@Schema(description = "主要用于并行环节多人处理可减签人员列表展示")
public class TaskOperator extends StepOperator {

    private static final long serialVersionUID = -10909802838313L;
    
    private int index;
    
    private String status;
    
    /**
     * 返回处理人排序号
     * 
     * @return 处理人排序号
     */
    @Schema(description = "处理人排序号", example = "1")
    public int getIndex() {
        return index;
    }

    /**
     * 设置处理人排序号
     * 
     * @param index 处理人排序号
     */
    public void setIndex(int index) {
        this.index = index;
    }
    
    /**
     * 返回活动环节状态
     * 
     * @return 活动环节状态
     */
    @Schema(description = "活动环节状态", example = "处理中")
    public String getStatus() {
        return status;
    }

    /**
     * 设置活动环节状态
     * 
     * @param status 活动环节状态
     */
    public void setStatus(String status) {
        this.status = status;
    }

}

