/*
 * Copyright 2013-2019 Smartdot Technologies Co., Ltd. All rights reserved.
 * SMARTDOT PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 */
package com.hd.rcugrc.bpm.facade.service;

import java.io.IOException;
import java.util.Map;

import com.hd.rcugrc.bpm.ActionDefinition;
import com.hd.rcugrc.bpm.FlowCallback;
import com.hd.rcugrc.bpm.Operator;
import com.hd.rcugrc.bpm.TargetFlowStep;
import com.hd.rcugrc.bpm.User;
import com.hd.rcugrc.bpm.facade.AdminProcessInstanceInfo;
import com.hd.rcugrc.bpm.facade.FormDataInfo;
import com.hd.rcugrc.bpm.facade.FormParamInfo;
import com.hd.rcugrc.bpm.facade.TargetFlowStepInfo;
import com.hd.rcugrc.bpm.facade.mvc.query.FormFieldValue;
import com.hd.rcugrc.data.crud.criteria.Criterion;
import com.hd.rcugrc.mvc.bpm.ValidateFailedException;

/**
 * <p>
 * 流程实例管理服务
 * 
 * <AUTHOR> href="mailto:<EMAIL>">shanglh</a>
 * @version 1.0, 2019年3月7日
 */
public interface ProcessManagerService {
    /**
     * 返回满足约束条件的可管理流程实例数（用于流程干预）
     * 
     * @param criterions 查询约束条件集合
     * @return 返回可管理流程实例数
     */
    public int getAdministrableInstanceCount(Criterion[] criterions);

    /**
     * 返回满足约束条件的可管理流程实例信息数组（用于流程干预）
     * 
     * @param criterions    前端查询条件
     * @param startPosition 返回记录前跳过的记录条数
     * @param maxResults    返回的结果记录条数，如果传入-1则返回所有的查询结果
     * @param orderBy       查询结果的排序设置，格式为\"字段
     *                      排序设置\"，其中排序设置的值为ASC或DESC，如果涉及多个字段，则多段定义间以，分隔
     * @return 返回可管理流程实例信息数组
     */
    public AdminProcessInstanceInfo[] getAdministrableInstances(Criterion[] criterions, int startPosition,
            int maxResults, String orderBy);
    
    /**
     * 返回满足约束条件的可管理流程实例数（用于流程干预），单表查询，可提升性能，但查询条件和排序条件需是HD_WF_LIST表存在的字段
     * 
     * @param criterions 查询约束条件集合
     * @return 返回可管理流程实例数
     */
    public int findAdministrableInstanceCount(Criterion[] criterions);
    
    /**
     * 返回满足约束条件的可管理流程实例信息数组（用于流程干预），单表查询，可提升性能，但查询条件和排序条件需是HD_WF_LIST表存在的字段<br/>
     * 列表查询时，先调用{@link #findAdministrableInstanceCount(Criterion[])}方法查询数量，该方法不做数量验证
     * 
     * @param criterions    前端查询条件
     * @param startPosition 返回记录前跳过的记录条数
     * @param maxResults    返回的结果记录条数，如果传入-1则返回所有的查询结果
     * @param orderBy       查询结果的排序设置，格式为\"字段
     *                      排序设置\"，其中排序设置的值为ASC或DESC，如果涉及多个字段，则多段定义间以，分隔
     * @return 返回可管理流程实例信息数组
     */
    public AdminProcessInstanceInfo[] findAdministrableInstances(Criterion[] criterions, int startPosition,
            int maxResults, String orderBy);

    /**
     * 返回满足约束条件的可管理流程实例数（用于流程干预），支持根据流程实例当前处理人作为查询条件单表查询<br/>
     * 可提升性能，但查询条件和排序条件需是HD_WF_LIST表存在的字段
     *
     * @param currentUsers 流程实例当前处理人信息
     * @param criterions 查询约束条件集合
     * @return 返回可管理流程实例数
     */
    public int findAdministrableInstCount(User[] currentUsers, Criterion[] criterions);

    /**
     * 返回满足约束条件的可管理流程实例信息数组（用于流程干预），支持根据流程实例当前处理人作为查询条件单表查询<br/>
     * 列表查询时，先调用{@link #findAdministrableInstCount(User[], Criterion[])}方法查询数量，该方法不做数量验证
     *
     * @param currentUsers 流程实例当前处理人信息
     * @param criterions    前端查询条件
     * @param startPosition 返回记录前跳过的记录条数
     * @param maxResults    返回的结果记录条数，如果传入-1则返回所有的查询结果
     * @param orderBy       查询结果的排序设置，格式为\"字段
     *                      排序设置\"，其中排序设置的值为ASC或DESC，如果涉及多个字段，则多段定义间以，分隔
     * @return 返回可管理流程实例信息数组
     */
    public AdminProcessInstanceInfo[] findAdministrableInst(User[] currentUsers,
        Criterion[] criterions, int startPosition, int maxResults, String orderBy);

    /**
     * 获取流程实例可被干预到的目标步骤集合
     * 
     * @param instId 待干预的流程实例id
     * @return
     */

    public TargetFlowStep[] getEntryStepDescriptorList(String instId);
    
    /**
     * 获取流程实例可被干预到的目标步骤集合
     * 
     * @param instId 待干预的流程实例id
     * @return
     */

    public TargetFlowStepInfo[] getEntryStepDescriptorInfoList(long instId);
    
    /**
     * 获取流程实例可被干预到的目标步骤集合
     * 
     * @param instId 待干预的流程实例id
     * @return
     */

    public TargetFlowStepInfo[] getEntryStepDescriptorInfoList(long instId, long activeStepId);

    /**
     * 获取流程实例可被干预到的目标步骤的后继操作集合
     * 
     * @param processId 流程定义id
     * 
     * @param stepId    环节id
     * @return
     */
    public ActionDefinition[] getEntryStepActionList(String processId, String stepId);

    /**
     * 获取流程实例可被干预到的目标步骤的后继操作集合
     * 
     * @param processId 流程定义id
     * 
     * @param instId    流程实例id
     * @param activeStepId 活动环节id
     * 
     * @param stepId    环节id
     * @return
     */
    public ActionDefinition[] getEntryStepActionList(String processId, long instId, long activeStepId, String stepId);

    /**
     * 暂停流程实例
     * 
     * @param callerAccount      调用者id; 执行流程干预操作的账户id; 注意: 操作账户需要拥有管理员角色
     * @param processInstanceIds 流程实例id数组
     * 
     * @param handler            流程操作回调类
     * 
     */

    public void suspendProcess(String callerAccount, String[] processInstanceIds, FlowCallback handler);
    
    /**
     * 暂停流程实例
     * 
     * @param callerAccount      调用者id; 执行流程干预操作的账户id; 注意: 操作账户需要拥有管理员角色
     * @param processInstanceIds 流程实例id数组
     * 
     * @param handler            流程操作回调类
     * @param context            上下文
     * 
     */
    
    public void suspendProcess(String callerAccount, String[] processInstanceIds, FlowCallback handler, 
            Map<String, Object> context);

    /**
     * 恢复流程实例
     * 
     * @param callerAccount      调用者id; 执行流程干预操作的账户id; 注意: 操作账户需要拥有管理员角色
     * @param processInstanceIds 流程实例id数组
     * 
     * @param handler            流程操作回调类
     * 
     */
    public void resumeProcess(String callerAccount, String[] processInstanceIds, FlowCallback handler);

    /**
     * 恢复流程实例
     * 
     * @param callerAccount      调用者id; 执行流程干预操作的账户id; 注意: 操作账户需要拥有管理员角色
     * @param processInstanceIds 流程实例id数组
     * 
     * @param handler            流程操作回调类
     * @param context            上下文
     * 
     */
    public void resumeProcess(String callerAccount, String[] processInstanceIds, FlowCallback handler, 
            Map<String, Object> context);
    /**
     * 终止流程实例
     * 
     * @param callerAccount      调用者id; 执行流程干预操作的账户id; 注意: 操作账户需要拥有管理员角色
     * @param processInstanceIds
     * 
     * @param handler            流程操作回调类
     * 
     *                           流程实例id数组
     */
    public void terminateProcess(String callerAccount, String[] processInstanceIds, FlowCallback handler);

    /**
     * 删除流程实例
     * 
     * @param callerAccount      调用者id; 执行流程干预操作的账户id; 注意: 操作账户需要拥有管理员角色
     * 
     * @param processInstanceIds 流程实例id数组
     * @param formParams         表单参数 平台表单时可以为空，第三方表单时不能为空
     * 
     * @param handler            流程操作回调类
     * 
     */
    public void removeProcess(String callerAccount, String[] processInstanceIds, FormParamInfo formParams,
            FlowCallback handler);

    /**
     * 将流转事项干预到指定的流转环节
     * 
     * @param callerAccount          调用者id; 执行流程干预操作的账户id; 注意: 操作账户需要拥有管理员角色
     * @param instId                 待干预的流转事项id
     * @param activeStepId           待干预的流转事项的当前处理步骤id
     * @param stepId                 干预到的环节的id
     * @param operators              干预后的参与者
     * 
     * @param handler                流程操作回调类
     * 
     * @param thirdBean              外部bean json字符串
     * 
     * @param thirdBeanIdProperty    外部bean id属性
     * 
     * @param thirdBeanIdType        外部bean id属性类型
     * 
     * @param thirdBeanTitleProperty 外部bean 流程标题属性
     * 
     * @return 成功返回true, 失败返回false
     */
    public boolean interveneToStep(String callerAccount, String instId, String activeStepId, String stepId,
            Operator[] operators, FlowCallback handler, Object thirdBean, String thirdBeanIdProperty,
            String thirdBeanIdType, String thirdBeanTitleProperty);
    
    /**
     * 将流转事项干预到指定的流转环节
     * 同 interveneToStep 只是返回参数不一样
     * 
     * @param callerAccount          调用者id; 执行流程干预操作的账户id; 注意: 操作账户需要拥有管理员角色
     * @param instId                 待干预的流转事项id
     * @param activeStepId           待干预的流转事项的当前处理步骤id
     * @param stepId                 干预到的环节的id
     * @param operators              干预后的参与者
     * 
     * @param handler                流程操作回调类
     * 
     * @param thirdBean              外部bean json字符串
     * 
     * @param thirdBeanIdProperty    外部bean id属性
     * 
     * @param thirdBeanIdType        外部bean id属性类型
     * 
     * @param thirdBeanTitleProperty 外部bean 流程标题属性
     * 
     * @param withResult 是否返回结果，没有实际作用
     * 
     * @return 流转后的当前处理步骤id的集合，以二维数组方式进行返回，第一维的每一元素为一个当前处理步骤的定义信息， 第二维为定义处理步骤信息的数组，元素0新当前步骤所在流转实例的entryId,元素1为新当前步骤的id。
     *         普通流转后续处理步骤仅有一个；如果流程流转后结束则没有当前处理步骤； 如果流程是多人处理、汇聚、子流程等需要进行等待的场景，流转后当前处理步骤保持不变；
     *         如果流程流转后推动了主流程的流转，则返回后的当前处理步骤是主流程的处理
     */
    @Deprecated
    public long[][] interveneToStep(String callerAccount, String instId, String activeStepId, String stepId,
            Operator[] operators, FlowCallback handler, Object thirdBean, String thirdBeanIdProperty,
            String thirdBeanIdType, String thirdBeanTitleProperty, boolean withResult);
    
    
    /**
     * 将流转事项干预到指定的流转环节
     * 同 interveneToStep 只是返回参数不一样
     * 
     * @param callerAccount          调用者id; 执行流程干预操作的账户id; 注意: 操作账户需要拥有管理员角色
     * @param instId                 待干预的流转事项id
     * @param activeStepId           待干预的流转事项的当前处理步骤id
     * @param stepId                 干预到的环节的id
     * @param operators              干预后的参与者
     * 
     * @param handler                流程操作回调类
     * 
     * @param thirdBean              外部bean json字符串
     * 
     * @param thirdBeanIdProperty    外部bean id属性
     * 
     * @param thirdBeanIdType        外部bean id属性类型
     * 
     * @param thirdBeanTitleProperty 外部bean 流程标题属性
     * 
     * @param withResult             是否返回结果，没有实际作用
     * @param props                  扩展属性
     * 
     * @return 流转后的当前处理步骤id的集合，以二维数组方式进行返回，第一维的每一元素为一个当前处理步骤的定义信息， 第二维为定义处理步骤信息的数组，元素0新当前步骤所在流转实例的entryId,元素1为新当前步骤的id。
     *         普通流转后续处理步骤仅有一个；如果流程流转后结束则没有当前处理步骤； 如果流程是多人处理、汇聚、子流程等需要进行等待的场景，流转后当前处理步骤保持不变；
     *         如果流程流转后推动了主流程的流转，则返回后的当前处理步骤是主流程的处理
     */
    public long[][] interveneToStep(String callerAccount, String instId, String activeStepId, String stepId,
            Operator[] operators, FlowCallback handler, Object thirdBean, String thirdBeanIdProperty,
            String thirdBeanIdType, String thirdBeanTitleProperty, boolean withResult, Map<String, String> props);

    /**
     * 将流程干预到指定的操作
     * 
     * @param callerAccount          调用者id; 执行流程干预操作的账户id; 注意: 操作账户需要拥有管理员角色
     * @param instId                 待干预的流转事项id
     * @param activeStepId           待干预的流转事项的当前处理步骤id
     * @param actionId               干预到的操作的id
     * @param operators              干预后的参与者
     * 
     * @param handler                流程操作回调类
     * 
     * @param thirdBean              外部bean json字符串
     * 
     * @param thirdBeanIdProperty    外部bean id属性
     * 
     * @param thirdBeanIdType        外部bean id属性类型
     * 
     * @param thirdBeanTitleProperty 外部bean 流程标题属性
     * 
     * @return 成功返回true, 失败返回false
     */
    @Deprecated
    public boolean interveneToAction(String callerAccount, String instId, String activeStepId, String actionId,
            Operator[] operators, FlowCallback handler, Object thirdBean, String thirdBeanIdProperty,
            String thirdBeanIdType, String thirdBeanTitleProperty) throws Exception;
    
    /**
     * 将流程干预到指定的操作
     * 
     * @param callerAccount          调用者id; 执行流程干预操作的账户id; 注意: 操作账户需要拥有管理员角色
     * @param instId                 待干预的流转事项id
     * @param activeStepId           待干预的流转事项的当前处理步骤id
     * @param actionId               干预到的操作的id
     * @param operators              干预后的参与者
     * 
     * @param handler                流程操作回调类
     * 
     * @param thirdBean              外部bean json字符串
     * 
     * @param thirdBeanIdProperty    外部bean id属性
     * 
     * @param thirdBeanIdType        外部bean id属性类型
     * 
     * @param thirdBeanTitleProperty 外部bean 流程标题属性
     * 
     * @param withResult 是否返回结果，没有实际作用
     * 
     * @return 流转后的当前处理步骤id的集合，以二维数组方式进行返回，第一维的每一元素为一个当前处理步骤的定义信息， 第二维为定义处理步骤信息的数组，元素0新当前步骤所在流转实例的entryId,元素1为新当前步骤的id。
     *         普通流转后续处理步骤仅有一个；如果流程流转后结束则没有当前处理步骤； 如果流程是多人处理、汇聚、子流程等需要进行等待的场景，流转后当前处理步骤保持不变；
     *         如果流程流转后推动了主流程的流转，则返回后的当前处理步骤是主流程的处理
     */
    public long[][] interveneToAction(String callerAccount, String instId, String activeStepId, String actionId,
            Operator[] operators, FlowCallback handler, Object thirdBean, String thirdBeanIdProperty,
            String thirdBeanIdType, String thirdBeanTitleProperty, boolean withResult) throws Exception;
    
    /**
     * 将流程干预到指定的操作
     * 
     * @param callerAccount          调用者id; 执行流程干预操作的账户id; 注意: 操作账户需要拥有管理员角色
     * @param instId                 待干预的流转事项id
     * @param activeStepId           待干预的流转事项的当前处理步骤id
     * @param actionId               干预到的操作的id
     * @param operators              干预后的参与者
     * 
     * @param handler                流程操作回调类
     * 
     * @param thirdBean              外部bean json字符串
     * 
     * @param thirdBeanIdProperty    外部bean id属性
     * 
     * @param thirdBeanIdType        外部bean id属性类型
     * 
     * @param thirdBeanTitleProperty 外部bean 流程标题属性
     * 
     * @param withResult             是否返回结果，没有实际作用
     * @param props                  扩展属性
     * 
     * @return 流转后的当前处理步骤id的集合，以二维数组方式进行返回，第一维的每一元素为一个当前处理步骤的定义信息， 第二维为定义处理步骤信息的数组，元素0新当前步骤所在流转实例的entryId,元素1为新当前步骤的id。
     *         普通流转后续处理步骤仅有一个；如果流程流转后结束则没有当前处理步骤； 如果流程是多人处理、汇聚、子流程等需要进行等待的场景，流转后当前处理步骤保持不变；
     *         如果流程流转后推动了主流程的流转，则返回后的当前处理步骤是主流程的处理
     */
    public long[][] interveneToAction(String callerAccount, String instId, String activeStepId, String actionId,
            Operator[] operators, FlowCallback handler, Object thirdBean, String thirdBeanIdProperty,
            String thirdBeanIdType, String thirdBeanTitleProperty, boolean withResult, 
            Map<String, String> props) throws Exception;

    /**
     * 返回指定流转事项所处理的表单的数据信息
     * 
     * @param instId             流转事项id
     * @param includeFields      返回结果中所包含的表单字段名字，多个字段以，分隔
     * @param excludeFields      返回结果中所不包含的表单字段名字，多个字段以，分隔
     * 
     * @param handler            流程操作回调类
     * 
     * @param thirdIdProperty    外部bean id属性
     * 
     * @param thirdIdProperyType 外部bean id属性类型
     * 
     * @param thirdTitleProperty 外部bean 流程标题属性
     * 
     * @return FormDataInfo 表单数据集合
     * @throws IOException
     */
    public FormDataInfo getFormData(String instId, String includeFields, String excludeFields, FlowCallback handler,
            String thirdIdProperty, String thirdIdProperyType, String thirdTitleProperty) throws IOException;

    /**
     * 保存对表单字段数据的修改
     * 
     * @param instId             流转事项id
     * @param modelName          数据所隶属的表单的id
     * @param propValues         待修改的表单字段集合，每个字段以一个二维字符数组指定，元素0为字段名字，元素1为字符形式的表单字段值
     * 
     * @param handler            流程操作回调类
     * 
     * @param thirdIdProperty    外部bean id属性
     * 
     * @param thirdIdProperyType 外部bean id属性类型
     * 
     * @param thirdTitleProperty 外部bean 流程标题属性
     * 
     * 
     * @return 更新后的属性数据，字段以一个二维字符数组指定，元素0为字段名字，元素1为字符形式的表单字段值
     * @throws IOException
     */
    public String[][] updateFormData(String instId, String modelName, String[][] propValues, FlowCallback handler,
            String thirdIdProperty, String thirdIdProperyType, String thirdTitleProperty) throws IOException;

    /**
     * 对流程实例强制解锁
     * 
     * @param instId 流程实例ID
     * @return true 解锁成功 ，false 解锁失败
     */
    public boolean forceUnlock(long instId);
    
    void adminUpdateFormData(long instId, FormFieldValue[] inputs, String formId) throws ValidateFailedException;
}
