/*
 * Copyright 2013-2019 Smartdot Technologies Co., Ltd. All rights reserved.
 * SMARTDOT PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 */
package com.hd.rcugrc.bpm.facade.service;

import com.hd.rcugrc.bpm.facade.CommunicationLogInfo;
import com.hd.rcugrc.bpm.facade.HdProcessLogInfo;
import com.hd.rcugrc.bpm.facade.InformLogInfo;
import com.hd.rcugrc.bpm.facade.ProcessActiveInfo;
import com.hd.rcugrc.bpm.facade.ProcessActiveLogInfo;
import com.hd.rcugrc.bpm.facade.ProcessLogInfo;
import com.hd.rcugrc.bpm.facade.ProcessRecordInfo;
import com.hd.rcugrc.bpm.facade.ReminderLogInfo;
import com.hd.rcugrc.bpm.facade.mobile.mvc.query.FreeSignNode;


/**
 * <p>
 * 流程监控服务 主要用于展示流程实时监控日志以及实时监控图
 * 
 * <AUTHOR> href="mailto:<EMAIL>">pandy</a>
 * @version 1.0, 2019年3月7日
 */
public interface ProcessTracingService {
    /**
     * 返回当前流程实时图字节数组
     * 
     * @param instId 流程实例Id
     * @return byte[] 流程实时图字节数组
     */
    public byte[] getFlowDiagramPng(long instId);

    /**
     * 返回当前流程定义图字节数组
     * 
     * @param flowId 获取流程定义图
     * @return byte[] 流程实时图字节数组
     */
    public byte[] getFlowDiagramPng(String flowId);

    /**
     * 获取流转记录列表
     * 
     * @param instId 流程实例Id
     * @return HdProcessLogInfo [] 流程记录列表
     */
    public HdProcessLogInfo[] getProcessLogs(long instId);

    /**
     * 获取流转记录列表，过滤子流程
     *
     * @param instId 流程实例Id
     * @return HdProcessLogInfo [] 流程记录列表
     */
    public default HdProcessLogInfo[] getProcessLogs(long instId, long subInstId) {
        return new HdProcessLogInfo[0];
    }

    /**
     * 获取当前流程实例正在活动的环节信息
     * @param instId 流程实例Id
     * @return ProcessActiveLogInfo [] 活动信息列表
     */
    public ProcessActiveLogInfo[] getProcessActiveLogs(long instId);

    /**
     * 获取当前流程实例正在活动的环节信息，过滤子流程
     *
     * @param instId 流程实例Id
     * @param subInstId  子流程实例
     * @return ProcessActiveLogInfo [] 活动信息列表
     */
    public default ProcessActiveLogInfo[] getProcessActiveLogs(long instId, long subInstId) {
        return new ProcessActiveLogInfo[0];
    }

    /**
     * 获取当前流程实例的流程监控信息
     * 
     * @param instId
     * @return
     */
    public ProcessRecordInfo getProcessRecordInfo(long instId);

    /**
     * 获取当前流程实例的沟通记录
     * 
     * @param instId 流程实例ID
     * @return 沟通记录 {@link CommunicationLogInfo}数组
     */
    public CommunicationLogInfo[] getProcessCommunicationLogs(long instId);

    /**
     * 获取当前流程实例的知会记录
     * 
     * @param instId 流程实例ID
     * @return 知会记录 {@link InformLogInfo}数组
     */
    public InformLogInfo[] getProcessInformLogs(long instId);
    
    /**
     * 获取非流程数据的知会信息
     *
     * @param beanId  记录id
     * @param pageId 页面id
     * @return 知会记录 {@link InformLogInfo}数组
     */
    default InformLogInfo[] getProcessInformLogs(long beanId, String pageId) {
        return new InformLogInfo[0];
    }
    
      /**
     * 获取当前流程实例的催办记录
     * @param instId 流程实例ID
     * @return 催办记录 {@link ProcessLogInfo}数组
     */
    public ReminderLogInfo[] getProcessReminderLogs(long instId);
    
    /**
     * 获取自由流类型环节的参与者之间的tree关系
     * 
     * @param instId 流程实例Id
     * @param stepId 带前缀的stepId
     * @return HdProcessLogInfo [] 流程记录列表
     */
    public FreeSignNode[] getFreeSignTree(long instId, String stepId);
    
    /**
     * 根据流程实例id获取流程实例当前活动环节记录信息
     * @param instId 流程实例id
     */
    public ProcessActiveInfo[] getProcessActives(long instId);
}
