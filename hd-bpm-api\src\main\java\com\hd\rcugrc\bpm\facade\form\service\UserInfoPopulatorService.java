/*
 * Copyright 2013-2019 Smartdot Technologies Co., Ltd. All rights reserved.
 * SMARTDOT PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 */
package com.hd.rcugrc.bpm.facade.form.service;

import com.hd.rcugrc.bpm.facade.mobile.mvc.query.UserInfo;

/**
 * <p>
 * 用户信息填充服务，主要用于移动端获取用户信息接口，用户信息无法满足需求时， 项目或者产品对现有用户信息进行扩展使用
 * <AUTHOR> href="mailto:<EMAIL>">songjj</a>
 * @version 5.4, 2019年7月25日
 */
public interface UserInfoPopulatorService {

    /**
     * 填充用户信息
     * @param userinfo 用户信息
     */
    public void populatorUserInfo(UserInfo userinfo);
}

