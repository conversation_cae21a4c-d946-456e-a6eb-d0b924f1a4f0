/*
 * Copyright 2013-2019 Smartdot Technologies Co., Ltd. All rights reserved.
 * SMARTDOT PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 */
package com.hd.rcugrc.bpm.facade;

import java.io.Serializable;

/**
 * <p>
 * 流程干预-可干预到环节信息
 * 
 * <AUTHOR> href="mailto:<EMAIL>">shang<PERSON><PERSON></a>
 * @version 1.0, 2019年6月17日
 */
public class TargetFlowStepInfo implements Serializable {
    private static final long serialVersionUID = 4070689043232730170L;

    private String processId;
    private String stepId;
    private String name;
    private String type;
    private String participantsStrategy;
    private boolean disabled;
    
    public TargetFlowStepInfo() {
    }
    
    public TargetFlowStepInfo(String processId, String stepId, String name, String type, 
            String participantsStrategy) {
        this.processId = processId;
        this.stepId = stepId;
        this.name = name;
        this.type = type;
        this.participantsStrategy = participantsStrategy;
    }
    
    public TargetFlowStepInfo(String processId, String stepId, String name, String type, 
            String participantsStrategy, boolean disabled) {
        this.processId = processId;
        this.stepId = stepId;
        this.name = name;
        this.type = type;
        this.participantsStrategy = participantsStrategy;
        this.disabled = disabled;
    }
    
    /**
     * 所隶属的流程id
     * 
     * @return
     */
    public String getProcessId() {
        return processId;
    }

    /**
     * 所隶属的流程id
     * 
     * @param processId
     */
    public void setProcessId(String processId) {
        this.processId = processId;
    }

    /**
     * 步骤id，最后一个子流程结束环节的步骤id应为：“步骤id/触发子流程的主流程所在环节的下一个actionID”
     * 
     * @return
     */
    public String getStepId() {
        return stepId;
    }

    /**
     * 步骤id，最后一个子流程结束环节的步骤id应为：“步骤id/触发子流程的主流程所在环节的下一个actionID”
     * 
     * @param stepId
     */
    public void setStepId(String stepId) {
        this.stepId = stepId;
    }

    /**
     * 步骤名称,，最后一个子流程结束环节的步骤名称应为：“步骤名称/触发子流程的主流程所在环节的下一个actionNAME”
     * 
     * @return
     */
    public String getName() {
        return name;
    }

    /**
     * 步骤名称
     * 
     * @param name
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * 步骤类型
     * 
     * @return
     */
    public String getType() {
        return type;
    }

    /**
     * 步骤类型
     * 
     * @param type
     */
    public void setType(String type) {
        this.type = type;
    }

    /**
     * 参与者选择方式
     * 
     * @return
     */
    public String getParticipantsStrategy() {
        return participantsStrategy;
    }

    /**
     * 参与者选择方式
     * 
     * @param participantsSelectModel
     */
    public void setParticipantsStrategy(String participantsSelectModel) {
        this.participantsStrategy = participantsSelectModel;
    }

    /**
     * 返回环节是否禁用
     * @return
     */
    public boolean isDisabled() {
        return disabled;
    }

    /**
     * 设置环节是否禁用
     * @param disabled
     */
    public void setDisabled(boolean disabled) {
        this.disabled = disabled;
    }
}

