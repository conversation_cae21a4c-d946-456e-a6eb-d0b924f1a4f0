/*
 * Copyright 2013-2019 Smartdot Technologies Co., Ltd. All rights reserved.
 * SMARTDOT PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 */
package com.hd.rcugrc.bpm.facade.service;

import java.util.List;
import java.util.Map;
import java.util.Set;

import org.springframework.context.NoSuchMessageException;

import com.hd.rcugrc.bpm.ActionResult;
import com.hd.rcugrc.bpm.FlowCallback;
import com.hd.rcugrc.bpm.Operator;
import com.hd.rcugrc.bpm.User;
import com.hd.rcugrc.bpm.attention.service.Attention;
import com.hd.rcugrc.bpm.communication.service.Communication;
import com.hd.rcugrc.bpm.extra.task.service.ReminderUser;
import com.hd.rcugrc.bpm.facade.ActiveStepOperator;
import com.hd.rcugrc.bpm.facade.FormParamInfo;
import com.hd.rcugrc.bpm.facade.FreeSignStepBean;
import com.hd.rcugrc.bpm.facade.ProcessActiveInfo;
import com.hd.rcugrc.bpm.facade.ReminderTaskInfo;
import com.hd.rcugrc.bpm.facade.StepOperator;
import com.hd.rcugrc.bpm.facade.SubflowActiveStepOperator;
import com.hd.rcugrc.bpm.facade.SubflowOperator;
import com.hd.rcugrc.bpm.facade.TargetFlowStepInfo;
import com.hd.rcugrc.bpm.facade.TaskActiveStepOperator;
import com.hd.rcugrc.bpm.facade.TimeConstraintDefinition;
import com.hd.rcugrc.bpm.facade.TodoResult;
import com.hd.rcugrc.bpm.facade.exception.TaskProcessException;
import com.hd.rcugrc.bpm.facade.mobile.mvc.query.AddGeneralOperator;
import com.hd.rcugrc.bpm.facade.mobile.mvc.query.AddOperator;
import com.hd.rcugrc.bpm.facade.mobile.mvc.query.AddSubFlowOperator;
import com.hd.rcugrc.bpm.facade.mobile.mvc.query.RemoveGeneralOperator;
import com.hd.rcugrc.bpm.facade.mobile.mvc.query.RemoveOperators;
import com.hd.rcugrc.bpm.facade.mobile.mvc.query.RemoveSubflowOperator;
import com.hd.rcugrc.bpm.read.service.Read;
import com.hd.rcugrc.mvc.bpm.WorkflowControllerException;
import com.hd.rcugrc.mvc.bpm.WorkflowSynchroLockException;

/**
 * <p>
 * 流程任务的处理功能，包括沟通转办，增减处理人等
 * 
 * <AUTHOR> href="mailto:<EMAIL>">pandy</a>
 * @version 1.0, 2019年3月7日
 */
public interface TaskProcessService {
    
    //待办沟通模块名
    public static final String COMMUNICATE_MODULE_NAME = "Communicate";
    //待办沟通打开模式
    public static final int COMMUNICATE_OPEN_MODE = 61;

    /**
     * 给指定人发起沟通
     * 
     * @param instId           流程实例ID
     * @param activeStepId     当前活动环节ID
     * @param lockId           锁流程ID，悲观锁时，前台页面打开时需要先上锁，保存结束后再解锁，乐观锁默认传0即可
     * @param caller           调用者
     * @param communicateUsers 被沟通人
     * @param commId           如果是被沟通人又发起的沟通，则需要指定被沟通人的沟通id
     * @param formParams       表单相关参数
     * @param props            扩展属性
     * 
     *                         <pre>
     * formParams参数中必须包含以下属性
     *  formId 表单ID  包含协议前缀 如推荐：非平台表单  tj:testForm；平台表单  model:testForm 
     * 如果是非平台表单，则还需要包含
     *      bean 实体bean对象，该bean方式设置方法 有两种  
     *          调用此方法时直接通过formParams设置
     * <b>如果bean 为空则将 formParams中的inputs 参数作为bean</b>
     *  beanTitleProperty 实体bean对象中作为标题字段属性
     *  beanIdProperty 实体bean对象中作为主键ID字段属性
     *  beanIdType 实体bean对象中作为主键ID类型  目前仅支持string 、 long
     *                         </pre>
     * 
     * @return Communication[] 沟通信息列表
     *
     *         throws TaskProcessException 系统参数不合法 以及沟通出错都会抛出该异常
     */
    public Communication[] communicate(long instId, long activeStepId, long lockId, User caller,
            User[] communicateUsers, long commId, FormParamInfo formParams, Map<String, String> props)
            throws TaskProcessException;

    /**
     * 撤销给指定人员发起的沟通
     * 
     * @param instId       流程实例ID
     * @param activeStepId 当前活动环节ID
     * @param stepN        环节步骤号
     * @param commId       如果是被沟通人又发起的沟通，则需要指定被沟通人的沟通id
     * @param caller       调用者
     * @param formParams   表单相关参数
     * @param props        扩展属性
     * 
     *                     <pre>
     * formParams参数中必须包含以下属性
     *  formId 表单ID  包含协议前缀 如推荐：非平台表单  tj:testForm；平台表单  model:testForm 
     * 如果是非平台表单，则还需要包含
     *      bean 实体bean对象，该bean方式设置方法 有两种  
     *          调用此方法时直接通过formParams设置
     * <b>如果bean 为空则将 formParams中的inputs 参数作为bean</b>
     *  beanTitleProperty 实体bean对象中作为标题字段属性
     *  beanIdProperty 实体bean对象中作为主键ID字段属性
     *  beanIdType 实体bean对象中作为主键ID类型  目前仅支持string 、 long
     *                     </pre>
     * 
     * @return true 撤销沟通成功 false 撤销沟通失败
     * @throws TaskProcessException
     */

    public boolean cancelCommunicate(long instId, long activeStepId, int stepN, String[] commId, User caller,
            FormParamInfo formParams, Map<String, String> props) throws TaskProcessException;

    /**
     * 是否能撤销给指定人员发起的沟通
     * 
     * @param instId 流程实例ID
     * @param formId 表单ID
     * @param beanId 表单实体BeanId
     * @param stepN  步骤ID
     * @param caller 调用者
     * @return boolean : true 能撤销给指定人员发起的沟通， false 不能撤销给指定人员发起的沟通
     * @throws TaskProcessException
     */

    public Boolean canCancelCommunicate(long instId, String formId, String beanId, int stepN, User caller)
            throws TaskProcessException;

    /**
     * 是否能反馈沟通信息
     * 
     * @param instId 流程实例ID
     * @param formId 表单ID
     * @param beanId 表单实体BeanId
     * @param stepN  步骤ID
     * @param caller 调用者
     * @return boolean : true 能反馈沟通信息， false 不能反馈沟通信息
     * @throws TaskProcessException
     */

    public Boolean canFeedbackCommunicate(long instId, String formId, String beanId, int stepN, User caller)
            throws TaskProcessException;

    /**
     * 回复指定沟通
     * 
     * @param instId       流程实例ID
     * @param activeStepId 当前活动环节ID
     * @param lockId       锁流程ID，悲观锁时，前台页面打开时需要先上锁，保存结束后再解锁，乐观锁默认传0即可
     * @param caller       调用者
     * @param commId       回复指定的沟通
     * @param formParams   表单相关参数
     * @param props        扩展属性
     * 
     *                     <pre>
     * formParams参数中必须包含以下属性
     *  formId 表单ID  包含协议前缀 如推荐：非平台表单  tj:testForm；平台表单  model:testForm 
     * 如果是非平台表单，则还需要包含
     *      bean 实体bean对象，该bean方式设置方法 有两种  
     *          调用此方法时直接通过formParams设置
     * <b>如果bean 为空则将 formParams中的inputs 参数作为bean</b>
     *  beanTitleProperty 实体bean对象中作为标题字段属性
     *  beanIdProperty 实体bean对象中作为主键ID字段属性
     *  beanIdType 实体bean对象中作为主键ID类型  目前仅支持string 、 long
     *                     </pre>
     * 
     * @return boolean true 成功 false 失败
     *
     *         throws TaskProcessException 系统参数不合法 以及沟通出错都会抛出该异常
     */
    public boolean replyCommunicate(long instId, long activeStepId, long lockId, User caller, long commId,
            FormParamInfo formParams, Map<String, String> props) throws TaskProcessException;
    
    /**
     * 撤回沟通反馈
     * 
     * @param instId       流程实例ID
     * @param activeStepId 当前活动环节ID
     * @param lockId       锁流程ID，悲观锁时，前台页面打开时需要先上锁，保存结束后再解锁，乐观锁默认传0即可
     * @param caller       调用者
     * @param commId       沟通id
     * @param formParams   表单相关参数
     * @param props        扩展属性
     * 
     *                     <pre>
     * formParams参数中必须包含以下属性
     *  formId 表单ID  包含协议前缀 如推荐：非平台表单  tj:testForm；平台表单  model:testForm 
     * 如果是非平台表单，则还需要包含
     *      bean 实体bean对象，该bean方式设置方法 有两种  
     *          调用此方法时直接通过formParams设置
     * <b>如果bean 为空则将 formParams中的inputs 参数作为bean</b>
     *  beanTitleProperty 实体bean对象中作为标题字段属性
     *  beanIdProperty 实体bean对象中作为主键ID字段属性
     *  beanIdType 实体bean对象中作为主键ID类型  目前仅支持string 、 long
     *                     </pre>
     * 
     * @return boolean true 成功 false 失败
     *
     *         throws TaskProcessException 系统参数不合法 以及沟通出错都会抛出该异常
     */
    public boolean recallFeedbackCommunicate(long instId, long activeStepId, long lockId, User caller, long commId,
            FormParamInfo formParams, Map<String, String> props) throws TaskProcessException;

    /*
     *管理员干预沟通; 禁掉未完成的沟通, 同时隐藏相关的业务待办; 管理员撤转时使用
     * 
     * @param instId       流程实例ID
     * @param caller       调用者
     * @param formParams   表单相关参数
     * @param props        扩展属性
     * */
    public void adminInterveCommunicate(long instId, User caller,
            FormParamInfo formParams, Map<String, String> props) throws TaskProcessException;
    /**
     * 给指定人员发送待阅
     * 
     * @param instId        流程实例ID
     * @param activeStepId  当前活动环节ID
     * @param lockId        锁流程ID，悲观锁时，前台页面打开时需要先上锁，保存结束后再解锁，乐观锁默认传0即可
     * @param caller        调用者
     * @param informIdUsers 被送阅人
     * @param formParams    表单参数
     * @return Read [] 送阅列表
     * @throws TaskProcessException
     */
    public Read[] sendRead(long instId, long activeStepId, long lockId, User caller, User[] informIdUsers,
            FormParamInfo formParams) throws TaskProcessException;

    /**
     * 给指定人员发起知会
     * 
     * @param instId        流程实例ID
     * @param activeStepId  当前活动环节ID
     * @param lockId        锁流程ID，悲观锁时，前台页面打开时需要先上锁，保存结束后再解锁，乐观锁默认传0即可
     * @param caller        调用者
     * @param informIdUsers 被知会人
     * @param formParams    表单参数
     * @return Read [] 知会列表
     * @throws TaskProcessException
     */
    public Read[] inform(long instId, long activeStepId, long lockId, User caller, User[] informIdUsers,
            FormParamInfo formParams) throws TaskProcessException;

    /**
     * 给指定人员发起知会
     * 
     * @param instId                  流程实例ID
     * @param activeStepId            当前活动环节ID
     * @param lockId                  锁流程ID，悲观锁时，前台页面打开时需要先上锁，保存结束后再解锁，乐观锁默认传0即可
     * @param caller                  调用者
     * @param informIdUsers           被知会人
     * @param formParams              表单参数
     * @param props                   扩展属性
     * @param feedbackOpinionRequired 反馈意见是否必填
     * @param isFollowFeedback        是否跟踪反馈
     * @return Read [] 知会列表
     * @throws TaskProcessException
     */
    public Read[] inform(long instId, long activeStepId, long lockId, User caller, User[] informIdUsers,
            FormParamInfo formParams, Map<String, String> props, Boolean feedbackOpinionRequired,
            Boolean isFollowFeedback) throws TaskProcessException;
    
    /**
     * 给指定人员发起知会
     *
     * @param instId                  流程实例ID
     * @param activeStepId            当前活动环节ID
     * @param lockId                  锁流程ID，悲观锁时，前台页面打开时需要先上锁，保存结束后再解锁，乐观锁默认传0即可
     * @param caller                  调用者
     * @param informIdUsers           被知会人
     * @param formParams              表单参数
     * @param props                   扩展属性
     * @param feedbackOpinionRequired 反馈意见是否必填
     * @param isFollowFeedback        是否跟踪反馈
     * @param urgencyLevel            缓急
     * @return Read [] 知会列表
     * @throws TaskProcessException
     */
    public Read[] inform(long instId, long activeStepId, long lockId, User caller, User[] informIdUsers,
                         FormParamInfo formParams, Map<String, String> props, Boolean feedbackOpinionRequired,
                         Boolean isFollowFeedback, Integer urgencyLevel) throws TaskProcessException;

    /**
     * 是否能回复知会
     * 
     * @param instId 流程实例ID
     * @param formId 表单ID
     * @param beanId 表单实体BeanId
     * @param stepN  步骤ID
     * @param caller 调用者
     * @return boolean : true 能回复知会， false 不能知会知会
     * @throws TaskProcessException 系统参数不合法 以及查询是否可以回复知会出错都会抛出该异常
     */
    public boolean canFeedbackInform(long instId, String formId, String beanId, int stepN, User caller)
            throws TaskProcessException;

    /**
     * 获取指定发送人待阅数量(区分知会和送阅)
     * 
     * @param instId   流程实例ID
     * @param formId   表单Id
     * @param beanId   表单实体BeanId
     * @param sendType 发送类型 1知会；2送阅
     * @param sender   发送人
     * @return 指定流程实例待阅数量
     */
    public Long findUnReadCountBySendTypeAndSender(long instId, String formId, String beanId, int sendType, User sender)
            throws TaskProcessException;

    /**
     * 获取指定知会发送人待阅数量
     * 
     * @param instId 流程实例ID
     * @param formId 表单Id
     * @param beanId 表单实体BeanId
     * @param sender 发送人
     * @return 指定流程实例待阅数量
     */
    public Long findUnReadCountBySender(long instId, String formId, String beanId, User sender)
            throws TaskProcessException;

    /**
     * 撤销给指定人员发起的知会
     * 
     * @param instId       流程实例ID
     * @param activeStepId 当前活动环节ID
     * @param informId     知会ID
     * @param formParams   表单信息
     * @return 流程操作结果，当前流程信息
     */
    public boolean cancelInform(long instId, long activeStepId, String informId, FormParamInfo formParams,
            Map<String, String> props) throws TaskProcessException;
    
    
    /**
     * 撤销给指定人员发起的知会,并返回详细结果
     * map key: success  fail
     * map val: List<User>
     *
     * @param instId       流程实例ID
     * @param activeStepId 当前活动环节ID
     * @param informId     知会ID
     * @param formParams   表单信息
     * @return 流程操作结果，当前流程信息
     */
    Map<String, Set<User>> cancelInformAndResult(long instId, long activeStepId, String informId,
                                                 FormParamInfo formParams,
                                                 Map<String, String> props) throws TaskProcessException;

    /**
     * 是否能撤销发起的知会
     * 
     * @param instId 流程实例ID
     * @param formId 表单ID
     * @param beanId 表单实体BeanId
     * @param caller 调用者
     * @return boolean : true 能撤销给指定人员发起的知会， false 不能撤销给指定人员发起的知会
     * @throws TaskProcessException
     */
    public Boolean canCancelInform(long instId, String formId, String beanId, User caller) throws TaskProcessException;

    /**
     * 回复知会
     * 
     * @param instId       流程实例ID
     * @param activeStepId 当前活动环节ID
     * @param lockId       锁流程ID，悲观锁时，前台页面打开时需要先上锁，保存结束后再解锁，乐观锁默认传0即可
     * @param caller       调用者
     * @param informId     知会ID
     * @param formParams   表单信息
     * @param props        扩展属性, 可以为null
     * @return true 成功 false 失败
     * @throws TaskProcessException
     */
    public boolean replyInform(long instId, long activeStepId, long lockId, User caller, long informId,
            FormParamInfo formParams, Map<String, String> props) throws TaskProcessException;

    /**
     * 关注指定的工作流
     * 
     * @param instId 流程实例ID
     * @param caller 调用者
     * @param title  标题
     * @return Attention
     * @throws TaskProcessException
     */
    public Attention attention(long instId, User caller, String title) throws TaskProcessException;

    /**
     * 是否能关注指定的工作流
     * 
     * @param instId 流程实例ID
     * @param caller 调用者账户
     * @return boolean ： true 能关注指定的工作流， false 不能关注指定的工作流
     */
    public boolean canAttention(long instId, User caller);

    /**
     * 撤销关注的指定工作流
     * 
     * @param instId 流程实例ID
     * @param caller 调用者账户
     * @return 流程操作结果，当前流程信息
     */
    public boolean cancelAttention(long instId, User caller) throws TaskProcessException;

    /**
     * 是否能撤销关注指定的工作流
     * 
     * @param instId 流程实例ID
     * @param caller 调用者账户
     * @return boolean ： true 能撤销关注指定的工作流， false 不能撤销关注指定的工作流
     */
    public boolean canCancelAttention(long instId, User caller) throws TaskProcessException;

    /**
     * 将当前人的任务转办给指定人 转办成功后会增加转办日志信息
     * 
     * @param instId       流程实例ID
     * @param activeStepId 当前活动环节ID
     * @param lockId       锁流程ID，悲观锁时，前台页面打开时需要先上锁，保存结束后再解锁，乐观锁默认传0即可
     * @param caller       调用者
     * @param targetUser   待办接收人
     * @param formParams   表单相关参数
     * 
     *                     <pre>
     * 该流程中包含子流程时
     * formParams参数中必须包含以下属性
     * formId 表单ID  包含协议前缀 如推荐：非平台表单  tj:testForm；平台表单  model:testForm 
     * 如果是非平台表单，则还需要包含
     *      bean 实体bean对象，该bean方式设置方法 有两种  
     *          调用此方法时直接通过formParams设置
     * <b>如果bean 为空则将 formParams中的inputs 参数作为bean</b>
     *  beanTitleProperty 实体bean对象中作为标题字段属性
     *  beanIdProperty 实体bean对象中作为主键ID字段属性
     *  beanIdType 实体bean对象中作为主键ID类型  目前仅支持string 、 long
     *                     </pre>
     * 
     * @return true 转办成功 false 转办失败
     *
     *         throws TaskProcessException 系统参数不合法 以及转办出错都会抛出该异常
     */
    public boolean transfer(long instId, long activeStepId, long lockId, User caller, User targetUser,
            FormParamInfo formParams) throws TaskProcessException;

    /**
     * 将当前人的任务转办给指定人 转办成功后会增加转办日志信息
     * 
     * @param instId       流程实例ID
     * @param activeStepId 当前活动环节ID
     * @param lockId       锁流程ID，悲观锁时，前台页面打开时需要先上锁，保存结束后再解锁，乐观锁默认传0即可
     * @param caller       调用者
     * @param targetUsers   待办接收人
     * @param formParams   表单相关参数
     * 
     *                     <pre>
     * 该流程中包含子流程时
     * formParams参数中必须包含以下属性
     * formId 表单ID  包含协议前缀 如推荐：非平台表单  tj:testForm；平台表单  model:testForm 
     * 如果是非平台表单，则还需要包含
     *      bean 实体bean对象，该bean方式设置方法 有两种  
     *          调用此方法时直接通过formParams设置
     * <b>如果bean 为空则将 formParams中的inputs 参数作为bean</b>
     *  beanTitleProperty 实体bean对象中作为标题字段属性
     *  beanIdProperty 实体bean对象中作为主键ID字段属性
     *  beanIdType 实体bean对象中作为主键ID类型  目前仅支持string 、 long
     *                     </pre>
     * 
     * @param props        扩展属性，可以在扩展属性中标记请求来源
     * @return true 转办成功 false 转办失败
     *
     *         throws TaskProcessException 系统参数不合法 以及转办出错都会抛出该异常
     */
    public boolean transfer(long instId, long activeStepId, long lockId, User caller, List<User> targetUsers,
            FormParamInfo formParams, Map<String, String> props) throws TaskProcessException;

    /**
     * 是否能将当前人的任务转办给指定人
     * 
     * @param instId               当流程实例ID
     * @param activeStepId         当前活动环节ID
     * @param caller               调用者
     * @param defaultAllowTransfer 是否允许转办 ，true 允许 false 不允许
     * 
     *                             <pre>
     * defaultAllowTransfer
     *  如果为false 时，结果直接返回false
     *  如果为true 时，则校验该环节是否满足转办条件，如果满足则返回 true ，不满足则返回false
     *  对应v5平台中工作流控制菜单中的附加属性turnToDo 中的值
     *                             </pre>
     * 
     * @return boolean ： true 能将当前人的任务转办给指定人， false 不能将当前人的任务转办给指定人
     */
    public boolean canTransfer(long instId, long activeStepId, User caller, boolean defaultAllowTransfer)
            throws TaskProcessException;

    /**
     * 撤回已转办的任务
     * 
     * @param instId
     * @param activeStepId
     * @param lockId
     * @param caller
     * @param formParams
     * @return
     * @throws TaskProcessException
     */
    public boolean cancelTransfer(long instId, long activeStepId, long lockId, User caller, FormParamInfo formParams)
            throws TaskProcessException;

    /**
     * 撤回已转办的任务
     * 
     * @param instId
     * @param activeStepId
     * @param lockId
     * @param caller
     * @param formParams
     * @param props        扩展属性
     * @return
     * @throws TaskProcessException
     */
    public boolean cancelTransfer(long instId, long activeStepId, long lockId, User caller, FormParamInfo formParams,
            Map<String, String> props) throws TaskProcessException;

    /**
     * 是否能撤回已转办的任务
     * 
     * @param instId
     * @param activeStepId
     * @param caller
     * @param defaultAllowCancelTransfer
     * @return
     * @throws TaskProcessException
     */
    public boolean canCancelTransfer(long instId, long activeStepId, User caller, boolean defaultAllowCancelTransfer)
            throws TaskProcessException;

    /**
     * 当前环节是否已转办过
     * 
     * @param instId       当前流程实例
     * @param activeStepId 当前活动环节
     * @param caller       调用者
     * @return true 当前环节有过转办记录，false 当前环节无转办记录
     * @throws TaskProcessException
     */
    public boolean currentStepHasTrunToLog(long instId, long activeStepId, User caller) throws TaskProcessException;

    /**
     * 多人并行环节(环节类型为非子流程)增加处理人
     * 
     * <pre>
     * 需要满足以下所有条件，才可以进行加签：
     * 1.当前环节为多人并行环节
     * 2.环节类型为非子流程
     * 3.处理人还剩下至少一人
     * 4.调用者与上一环节处理人是同一人
     * 5.defaultAllowAdd 为true
     * </pre>
     * 
     * @param instId          流程实例ID
     * @param caller          调用者
     * @param appendOperators 待加签人列表，目前v5系统仅仅单次加签1人，且目前仅支持操作类型为用户
     * @param formParams      表单相关参数
     * 
     *                        <pre>
     * 该流程中包含子流程时
     * formParams参数中必须包含以下属性
     * formId 表单ID  包含协议前缀 如推荐：非平台表单  tj:testForm；平台表单  model:testForm 
     * 如果是非平台表单，则还需要包含
     *      bean 实体bean对象，该bean方式设置方法 有两种  
     *          调用此方法时直接通过formParams设置
     * <b>如果bean 为空则将 formParams中的inputs 参数作为bean</b>
     *  beanTitleProperty 实体bean对象中作为标题字段属性
     *  beanIdProperty 实体bean对象中作为主键ID字段属性
     *  beanIdType 实体bean对象中作为主键ID类型  目前仅支持string 、 long
     *                        </pre>
     * 
     * @return true 加签成功 ，false 加签失败
     *
     *         throws TaskProcessException 系统参数不合法 以及加签办出错都会抛出该异常
     */
    public boolean addOperator(long instId, User caller, Operator[] appendOperators, FormParamInfo formParams)
            throws TaskProcessException;

    /**
     * 多人并行环节(环节类型为非子流程)增加处理人
     * 
     * <pre>
     * 需要满足以下所有条件，才可以进行加签：
     * 1.当前环节为多人并行环节
     * 2.环节类型为非子流程
     * 3.处理人还剩下至少一人
     * 4.调用者与上一环节处理人是同一人
     * 5.defaultAllowAdd 为true
     * </pre>
     * 
     * @param instId          流程实例ID
     * @param caller          调用者
     * @param appendOperators 待加签人列表，目前v5系统仅仅单次加签1人，且目前仅支持操作类型为用户
     * @param formParams      表单相关参数
     * 
     *                        <pre>
     * 该流程中包含子流程时
     * formParams参数中必须包含以下属性
     * formId 表单ID  包含协议前缀 如推荐：非平台表单  tj:testForm；平台表单  model:testForm 
     * 如果是非平台表单，则还需要包含
     *      bean 实体bean对象，该bean方式设置方法 有两种  
     *          调用此方法时直接通过formParams设置
     * <b>如果bean 为空则将 formParams中的inputs 参数作为bean</b>
     *  beanTitleProperty 实体bean对象中作为标题字段属性
     *  beanIdProperty 实体bean对象中作为主键ID字段属性
     *  beanIdType 实体bean对象中作为主键ID类型  目前仅支持string 、 long
     *                        </pre>
     * 
     * @param props           扩展属性
     * @return true 加签成功 ，false 加签失败
     *
     *         throws TaskProcessException 系统参数不合法 以及加签办出错都会抛出该异常
     */
    public boolean addOperator(long instId, User caller, Operator[] appendOperators, FormParamInfo formParams,
            Map<String, String> props) throws TaskProcessException;
    
    /**
     * 多人并行环节(环节类型为非子流程)增加处理人
     * 
     * <pre>
     * 需要满足以下所有条件，才可以进行加签：
     * 1.当前环节为多人并行环节
     * 2.环节类型为非子流程
     * 3.处理人还剩下至少一人
     * 4.调用者与上一环节处理人是同一人
     * 5.defaultAllowAdd 为true
     * </pre>
     * 
     * @param instId          流程实例ID
     * @param caller          调用者
     * @param activeStepId    当前环节id，如果不指定，则为当前环节数据的第一个
     * @param appendOperators 待加签人列表，目前v5系统仅仅单次加签1人，且目前仅支持操作类型为用户
     * @param formParams      表单相关参数
     * 
     *                        <pre>
     * 该流程中包含子流程时
     * formParams参数中必须包含以下属性
     * formId 表单ID  包含协议前缀 如推荐：非平台表单  tj:testForm；平台表单  model:testForm 
     * 如果是非平台表单，则还需要包含
     *      bean 实体bean对象，该bean方式设置方法 有两种  
     *          调用此方法时直接通过formParams设置
     * <b>如果bean 为空则将 formParams中的inputs 参数作为bean</b>
     *  beanTitleProperty 实体bean对象中作为标题字段属性
     *  beanIdProperty 实体bean对象中作为主键ID字段属性
     *  beanIdType 实体bean对象中作为主键ID类型  目前仅支持string 、 long
     *                        </pre>
     * 
     * @param props           扩展属性
     * @return true 加签成功 ，false 加签失败
     *
     *         throws TaskProcessException 系统参数不合法 以及加签办出错都会抛出该异常
     */
    public boolean addOperator(long instId, long activeStepId, User caller, Operator[] appendOperators, FormParamInfo formParams,
            Map<String, String> props) throws TaskProcessException;
    /**
     * 自由流类型节点(自由串和自由并)，增加处理人
     * 
     * <pre>
     * 需要满足以下所有条件，才可以进行加签：
     * 1.当前环节为自由流类型节点(自由串和自由并)
     * 2.调用者与上一环节处理人是同一人
     * </pre>
     * 
     * @param instId          流程实例ID
     * @param activeStepId    流程实例ID
     * @param caller          调用者
     * @param appendOperators 待加签人列表，目前v5系统仅仅单次加签1人，且目前仅支持操作类型为用户
     * @param context           扩展属性
     * @return true 加签成功 ，false 加签失败
     *
     *         throws TaskProcessException 系统参数不合法 以及加签办出错都会抛出该异常
     * @throws WorkflowControllerException
     */
    public boolean addFreeSignOperator(long instId, long activeStepId, User caller, Operator[] appendOperators,
            Map<String, Object> context) throws TaskProcessException, WorkflowControllerException;

    /**
     * <p>
     * 获取可以在自由流环节执行加人和减人的有效步骤列表
     * 
     * @param instId 当前实例ID
     * @param caller 调用者
     * @return 有效的步骤列表
     * @throws TaskProcessException
     */
    public FreeSignStepBean[] findFreeSignSteps(long instId, User caller) throws TaskProcessException;

    /**
     * 自由流类型节点(自由串和自由并)，减少处理人
     * 
     * <pre>
     * 需要满足以下所有条件，才可以进行减少处理人：
     * 1.当前环节为自由流类型节点(自由串和自由并)
     * 2.调用者与上一环节处理人是同一人
     * </pre>
     * 
     * @param instId          流程实例ID
     * @param activeStepId    活动步骤ID
     * @param caller          调用者
     * @param removeOperators 待减人列表，目前v5系统仅仅单次加签1人，且目前仅支持操作类型为用户
     * @param context           扩展属性
     * @return true 减人成功 ，false 加签失败
     *
     *         throws TaskProcessException 系统参数不合法 以及加签办出错都会抛出该异常
     * @throws WorkflowSynchroLockException
     * @throws IllegalStateException
     * @throws NoSuchMessageException
     * @throws WorkflowControllerException
     */
    public boolean removeFreeSignOperator(long instId, long activeStepId, User caller, Operator[] removeOperators,
            Map<String, Object> context) throws TaskProcessException, NoSuchMessageException, IllegalStateException,
            WorkflowSynchroLockException, WorkflowControllerException;

    /**
     * <p>
     * 获取加人按钮的权限
     * 
     * @param instId      流程实例ID
     * @param user        调用者
     * @param isEditModel 是否编辑模式
     * @return true 允许，false 禁止
     * @throws TaskProcessException
     */
    public boolean canAddFreeSignOperator(long instId, User user, boolean isEditModel) throws TaskProcessException;

    /**
     * <p>
     * 获取减人按钮的权限
     * 
     * @param instId      流程实例ID
     * @param user        调用者
     * @param isEditModel 是否编辑模式
     * @return true 允许，false 禁止
     * @throws TaskProcessException
     */
    public boolean canRemoveFreeSignOperator(long instId, User user, boolean isEditModel) throws TaskProcessException;

    /**
     * 
     * @param instId
     * @param activeStepId
     * @param user
     * @return
     * @throws TaskProcessException
     */
    public TaskActiveStepOperator findRemoveFreeSignOperator(long instId, long activeStepId, User user)
            throws TaskProcessException;

    /**
     * 是否能为多人并行环节(环节类型为非子流程)增加处理人
     * 
     * <pre>
     * 需要满足以下所有条件，才可以进行加签：
     * 1.当前环节为多人并行环节
     * 2.环节类型为非子流程
     * 3.处理人还剩下至少一人
     * 4.调用者与上一环节处理人是同一人
     * 5.defaultAllowAdd 为true
     * </pre>
     * <p>
     * 该方法如果返回true，那{@link #canAddSubflowOperator}方法返回是false
     * </p>
     * 
     * @param caller          流程调用者
     * @param defaultAllowAdd 是否允许追加处理人 ，true 允许 false 不允许
     * 
     *                        <pre>
     *  defaultAllowAdd
     *  如果为false 时，结果直接返回false
     *  如果为true 时，则校验该环节是否满足加签条件，如果满足则返回 true ，不满足则返回false
     *  如果是编辑页面时，需将defaultAllowAdd设置为false,查看页面时设置成true(已办页面中才可以进行加签)
     *                        </pre>
     * 
     * @return boolean ： true 能为多人并行环节添加处理人， false 不能为多人并行环节添加处理人
     */
    public boolean canAddOperator(long instId, User caller, boolean defaultAllowAdd) throws TaskProcessException;

    /**
     * 多人并行环节(环节类型为非子流程)减少处理人
     * 
     * <pre>
     * 需要满足以下所有条件，才可以进行减签：
     * 1.当前环节为多人并行环节
     * 2.环节类型为非子流程
     * 3.尚未处理流程的参与还剩下至少两人
     * 4.调用者与上一环节处理人是同一人
     * 5.defaultAllowRemove 为true
     * </pre>
     * 
     * @param instId          流程实例ID
     * @param stepOperatorId  环节操作人ID
     * @param caller          调用者
     * @param removeOperators 待减签人列表，目前v5系统仅仅单次减签1人
     * @return true 减签成功 ，false 减签失败
     *
     *         throws TaskProcessException 系统参数不合法 以及减签出错都会抛出该异常
     */
    public boolean removeOperator(long instId, long stepOperatorId, User caller, Operator[] removeOperators)
            throws TaskProcessException;

    /**
     * 多人并行环节(环节类型为非子流程)减少处理人
     * 
     * <pre>
     * 需要满足以下所有条件，才可以进行减签：
     * 1.当前环节为多人并行环节
     * 2.环节类型为非子流程
     * 3.尚未处理流程的参与还剩下至少两人
     * 4.调用者与上一环节处理人是同一人
     * 5.defaultAllowRemove 为true
     * </pre>
     * 
     * @param instId          流程实例ID
     * @param stepOperatorId  环节操作人ID
     * @param caller          调用者
     * @param removeOperators 待减签人列表，目前v5系统仅仅单次减签1人
     * @param props
     * @return true 减签成功 ，false 减签失败
     *
     *         throws TaskProcessException 系统参数不合法 以及减签出错都会抛出该异常
     */
    public boolean removeOperator(long instId, long stepOperatorId, User caller, Operator[] removeOperators,
            Map<String, String> props) throws TaskProcessException;

    /**
     * 多人并行环节(环节类型为非子流程)减少处理人
     * 
     * <pre>
     * 需要满足以下所有条件，才可以进行减签：
     * 1.当前环节为多人并行环节
     * 2.环节类型为非子流程
     * 3.尚未处理流程的参与还剩下至少两人
     * 4.调用者与上一环节处理人是同一人
     * 5.defaultAllowRemove 为true
     * </pre>
     * 
     * @param instId       流程实例ID
     * @param caller       调用者
     * @param stepOperator 待减签人列表
     * @return true 减签成功 ，false 减签失败
     *
     *         throws TaskProcessException 系统参数不合法 以及减签出错都会抛出该异常
     */
    public boolean removeOperators(long instId, User caller, StepOperator[] stepOperator,
            Map<String, String> props) throws TaskProcessException;
    
    /**
     * 多人并行环节(环节类型为非子流程)减少处理人
     * 
     * <pre>
     * 需要满足以下所有条件，才可以进行减签：
     * 1.当前环节为多人并行环节
     * 2.环节类型为非子流程
     * 3.尚未处理流程的参与还剩下至少两人
     * 4.调用者与上一环节处理人是同一人
     * 5.defaultAllowRemove 为true
     * </pre>
     * 
     * @param instId          流程实例ID
     * @param activeStepId    当前环节id，如果不指定，则为当前环节数据的第一个
     * @param caller          调用者
     * @param stepOperator 待减签人列表
     * @return true 减签成功 ，false 减签失败
     *
     *         throws TaskProcessException 系统参数不合法 以及减签出错都会抛出该异常
     */
    public boolean removeOperators(long instId, long activeStepId, User caller, StepOperator[] stepOperator,
            Map<String, String> props) throws TaskProcessException;

    /**
     * 是否可以为多人并行环节(环节类型为非子流程)减少处理人
     * 
     * <pre>
     * 需要满足以下所有条件，才可以进行减签：
     * 1.当前环节为多人并行环节
     * 2.环节类型为非子流程
     * 3.尚未处理流程的参与还剩下至少两人
     * 4.调用者与上一环节处理人是同一人
     * 5.defaultAllowRemove 为true
     * </pre>
     * <p>
     * 该方法如果返回true，那{@link #canRemoveSubflowOperator}方法返回是false
     * </p>
     * 
     * @param instId             流程实例ID
     * @param caller             调用者
     * @param defaultAllowRemove 是否允许减少处理人 ，true 允许 false 不允许
     * 
     *                           <pre>
     * 
     *  defaultAllowAdd
     *  如果为false 时，结果直接返回false
     *  如果为true 时，则校验该环节是否满足转办条件，如果满足则返回 true ，不满足则返回false
     *  如果是编辑页面时，需将defaultAllowAdd设置为false,查看页面时设置成true(已办页面中才可以进行加签)
     *                           </pre>
     * 
     * @return boolean ： true 能为多人并行环节减少处理人， false 不能为多人并行环节减少处理人
     */

    public boolean canRemoveOperator(long instId, User caller, boolean defaultAllowRemove) throws TaskProcessException;
    
    /**
     * 是否可以为多人并行环节(环节类型为非子流程)减少处理人
     * 
     * <pre>
     * 需要满足以下所有条件，才可以进行减签：
     * 1.当前环节为多人并行环节
     * 2.环节类型为非子流程
     * 3.尚未处理流程的参与还剩下至少两人
     * 4.调用者与上一环节处理人是同一人
     * 5.defaultAllowRemove 为true
     * </pre>
     * <p>
     * 该方法如果返回true，那{@link #canRemoveSubflowOperator}方法返回是false
     * </p>
     * 
     * @param instId             流程实例ID
     * @param activeStepId    当前环节id，如果不指定，则为当前环节数据的第一个
     * @param caller             调用者
     * @param defaultAllowRemove 是否允许减少处理人 ，true 允许 false 不允许
     * 
     *                           <pre>
     * 
     *  defaultAllowAdd
     *  如果为false 时，结果直接返回false
     *  如果为true 时，则校验该环节是否满足转办条件，如果满足则返回 true ，不满足则返回false
     *  如果是编辑页面时，需将defaultAllowAdd设置为false,查看页面时设置成true(已办页面中才可以进行加签)
     *                           </pre>
     * 
     * @return boolean ： true 能为多人并行环节减少处理人， false 不能为多人并行环节减少处理人
     */

    public boolean canRemoveOperator(long instId, long activeStepId, User caller, boolean defaultAllowRemove) throws TaskProcessException;

    /**
     * 获取流程实例当前环节待处理人员列表
     * <p>
     * 主要用于展示可减签人员列表
     * 
     * @param instId 流程实例ID
     * @return 环节待处理人员列表 throws TaskProcessException 系统参数不合法
     */
    @Deprecated
    public ActiveStepOperator[] getActiveStepUsers(long instId) throws TaskProcessException;
    
    /**
     * 获取流程实例当前环节待处理人员列表-前端加签判断可加签人员用
     * <p>
     * 
     * @param instId 流程实例ID
     * @return 环节待处理人员列表 throws TaskProcessException 系统参数不合法
     */
    public ActiveStepOperator[] getActiveStepUsersByAddOperator(long instId) throws TaskProcessException;

    /**
     * 多人并行环节(环节类型为子流程)增加处理人 每个处理人都对应着一个子流程实例
     * 
     * <pre>
     * 需要满足以下所有条件，才可以进行加签：
     * 1.当前环节为多人并行环节
     * 2.环节类型为子流程
     * 3.处理人还剩下至少一人
     * 4.调用者与上一环节处理人是同一人
     * 5.defaultAllowAdd 为true
     * </pre>
     * 
     * @param instId          主流程实例ID
     * @param caller          调用者
     * @param appendOperators 待加签人列表
     * @param formParams      表单相关参数
     * 
     *                        <pre>
     * 该流程中包含子流程时
     * formParams参数中必须包含以下属性
     * formId 表单ID  包含协议前缀 如推荐：非平台表单  tj:testForm；平台表单  model:testForm 
     * 如果是非平台表单，则还需要包含
     *      bean 实体bean对象，该bean方式设置方法 有两种  
     *          调用此方法时直接通过formParams设置
     * <b>如果bean 为空则将 formParams中的inputs 参数作为bean</b>
     *  beanTitleProperty 实体bean对象中作为标题字段属性
     *  beanIdProperty 实体bean对象中作为主键ID字段属性
     *  beanIdType 实体bean对象中作为主键ID类型  目前仅支持string 、 long
     *                        </pre>
     *
     * @param handler         流程推进前后执行的回调方法 此处必须
     * 
     *                        <pre>
     *   
     * 使用场景：流程中有子流程,且下一环节时创建子流程时需要传入回调函数
     * 用来建立子流程 与表单数据关联关系
     * 使用平台表单样例
     * {@link com.hd.rcugrc.mvc.bpm.SubflowDataBinder}
     * 使用非平台表单样例
     * {@link com.hd.rcugrc.bpm.facade.service.impl.CustomSubflowDataBinder}
     *                        </pre>
     * 
     * @return actionResult 返回操作结果 目前返回新建的对象 没有任何值 throws TaskProcessException
     *         系统参数不合法 以及加签办出错都会抛出该异常
     */
    public ActionResult addSubflowOperator(long instId, User caller, Operator[] appendOperators,
            FormParamInfo formParams, FlowCallback handler) throws TaskProcessException;

    /**
     * 多人并行环节(环节类型为子流程)增加处理人 每个处理人都对应着一个子流程实例
     * 
     * <pre>
     * 需要满足以下所有条件，才可以进行加签：
     * 1.当前环节为多人并行环节
     * 2.环节类型为子流程
     * 3.处理人还剩下至少一人
     * 4.调用者与上一环节处理人是同一人
     * 5.defaultAllowAdd 为true
     * </pre>
     * 
     * @param instId          主流程实例ID
     * @param caller          调用者
     * @param appendOperators 待加签人列表
     * @param formParams      表单相关参数
     * 
     *                        <pre>
     * 该流程中包含子流程时
     * formParams参数中必须包含以下属性
     * formId 表单ID  包含协议前缀 如推荐：非平台表单  tj:testForm；平台表单  model:testForm 
     * 如果是非平台表单，则还需要包含
     *      bean 实体bean对象，该bean方式设置方法 有两种  
     *          调用此方法时直接通过formParams设置
     * <b>如果bean 为空则将 formParams中的inputs 参数作为bean</b>
     *  beanTitleProperty 实体bean对象中作为标题字段属性
     *  beanIdProperty 实体bean对象中作为主键ID字段属性
     *  beanIdType 实体bean对象中作为主键ID类型  目前仅支持string 、 long
     *                        </pre>
     *
     * @param handler         流程推进前后执行的回调方法 此处必须
     * 
     *                        <pre>
     *   
     * 使用场景：流程中有子流程,且下一环节时创建子流程时需要传入回调函数
     * 用来建立子流程 与表单数据关联关系
     * 使用平台表单样例
     * {@link com.hd.rcugrc.mvc.bpm.SubflowDataBinder}
     * 使用非平台表单样例
     * {@link com.hd.rcugrc.bpm.facade.service.impl.CustomSubflowDataBinder}
     *                        </pre>
     * 
     * @param props           扩展属性
     * @return actionResult 返回操作结果 目前返回新建的对象 没有任何值 throws TaskProcessException
     *         系统参数不合法 以及加签办出错都会抛出该异常
     */
    public ActionResult addSubflowOperator(long instId, User caller, Operator[] appendOperators,
            FormParamInfo formParams, FlowCallback handler, Map<String, String> props) throws TaskProcessException;
    
    /**
     * 多人并行环节(环节类型为子流程)增加处理人 每个处理人都对应着一个子流程实例
     * 
     * <pre>
     * 需要满足以下所有条件，才可以进行加签：
     * 1.当前环节为多人并行环节
     * 2.环节类型为子流程
     * 3.处理人还剩下至少一人
     * 4.调用者与上一环节处理人是同一人
     * 5.defaultAllowAdd 为true
     * </pre>
     * 
     * @param instId          主流程实例ID
     * @param activeStepId    当前环节id，如果不指定，则为当前环节数据的第一个
     * @param caller          调用者
     * @param appendOperators 待加签人列表
     * @param formParams      表单相关参数
     * 
     *                        <pre>
     * 该流程中包含子流程时
     * formParams参数中必须包含以下属性
     * formId 表单ID  包含协议前缀 如推荐：非平台表单  tj:testForm；平台表单  model:testForm 
     * 如果是非平台表单，则还需要包含
     *      bean 实体bean对象，该bean方式设置方法 有两种  
     *          调用此方法时直接通过formParams设置
     * <b>如果bean 为空则将 formParams中的inputs 参数作为bean</b>
     *  beanTitleProperty 实体bean对象中作为标题字段属性
     *  beanIdProperty 实体bean对象中作为主键ID字段属性
     *  beanIdType 实体bean对象中作为主键ID类型  目前仅支持string 、 long
     *                        </pre>
     *
     * @param handler         流程推进前后执行的回调方法 此处必须
     * 
     *                        <pre>
     *   
     * 使用场景：流程中有子流程,且下一环节时创建子流程时需要传入回调函数
     * 用来建立子流程 与表单数据关联关系
     * 使用平台表单样例
     * {@link com.hd.rcugrc.mvc.bpm.SubflowDataBinder}
     * 使用非平台表单样例
     * {@link com.hd.rcugrc.bpm.facade.service.impl.CustomSubflowDataBinder}
     *                        </pre>
     * 
     * @param props           扩展属性
     * @return actionResult 返回操作结果 目前返回新建的对象 没有任何值 throws TaskProcessException
     *         系统参数不合法 以及加签办出错都会抛出该异常
     */
    public ActionResult addSubflowOperator(long instId, long activeStepId, User caller, Operator[] appendOperators,
            FormParamInfo formParams, FlowCallback handler, Map<String, String> props) throws TaskProcessException;

    /**
     * 是否能为多人并行环节(环节类型为子流程)增加处理人
     * 
     * <pre>
     * 需要满足以下所有条件，才可以进行加签：
     * 1.当前环节为多人并行环节
     * 2.环节类型为子流程
     * 3.处理人还剩下至少一人
     * 4.调用者与上一环节处理人是同一人
     * 5.defaultAllowAdd 为true
     * </pre>
     * <p>
     * 该方法如果返回true，那{@link #canAddOperator}方法返回是false
     * </p>
     * 
     * @param instId          主流程实例ID
     * @param caller          流程调用者
     * @param defaultAllowAdd 是否允许追加处理人 ，true 允许 false 不允许
     * 
     *                        <pre>
     *  defaultAllowAdd
     *  如果为false 时，结果直接返回false
     *  如果为true 时，则校验该环节是否满足加签条件，如果满足则返回 true ，不满足则返回false
     *  如果是编辑页面时，需将defaultAllowAdd设置为false,查看页面时设置成true(已办页面中才可以进行加签)
     *                        </pre>
     * 
     * @return boolean ： true 能为多人并行环节添加处理人， false 不能为多人并行环节添加处理人
     */
    public boolean canAddSubflowOperator(long instId, User caller, boolean defaultAllowAdd) throws TaskProcessException;

    /**
     * 是否能为多人并行环节(环节类型为子流程)增加处理人
     * 
     * <pre>
     * 需要满足以下所有条件，才可以进行加签：
     * 1.当前环节为多人并行环节
     * 2.环节类型为子流程
     * 3.处理人还剩下至少一人
     * 4.调用者与上一环节处理人是同一人
     * 5.defaultAllowAdd 为true
     * </pre>
     * <p>
     * 该方法如果返回true，那{@link #canAddOperator}方法返回是false
     * </p>
     * 
     * @param instId          主流程实例ID
     * @param activeStepId    当前环节id，如果不指定，则为当前环节数据的第一个
     * @param caller          流程调用者
     * @param defaultAllowAdd 是否允许追加处理人 ，true 允许 false 不允许
     * 
     *                        <pre>
     *  defaultAllowAdd
     *  如果为false 时，结果直接返回false
     *  如果为true 时，则校验该环节是否满足加签条件，如果满足则返回 true ，不满足则返回false
     *  如果是编辑页面时，需将defaultAllowAdd设置为false,查看页面时设置成true(已办页面中才可以进行加签)
     *                        </pre>
     * 
     * @return boolean ： true 能为多人并行环节添加处理人， false 不能为多人并行环节添加处理人
     */
    public boolean canAddSubflowOperator(long instId, long activeStepId, User caller, boolean defaultAllowAdd)
            throws TaskProcessException;
    /**
     * 多人并行环节(环节类型为子流程)减少处理人 每个处理人都对应着一个子流程实例
     * 
     * <pre>
     * 需要满足以下所有条件，才可以进行减签：
     * 1.当前环节为多人并行环节
     * 2.环节类型为子流程
     * 3.尚未处理流程的参与还剩下至少两人
     * 4.调用者与上一环节处理人是同一人
     * 5.defaultAllowRemove 为true
     * </pre>
     * 
     * @param instId          主流程实例ID
     * @param caller          调用者
     * @param removeOperators 待减签人列表
     * @return ActionResult 可以删除所有的尚未参与者 ，删除所有后，会跳过当前子流程，返回主流程可以操作的actionId 和name
     *
     *         throws TaskProcessException 系统参数不合法 以及减签出错都会抛出该异常
     */
    public com.hd.rcugrc.bpm.app.ActionResult removeSubflowOperator(long instId, User caller,
            SubflowOperator[] removeOperators, FormParamInfo formParams) throws TaskProcessException;

    /**
     * 多人并行环节(环节类型为子流程)减少处理人 每个处理人都对应着一个子流程实例
     * 
     * <pre>
     * 需要满足以下所有条件，才可以进行减签：
     * 1.当前环节为多人并行环节
     * 2.环节类型为子流程
     * 3.尚未处理流程的参与还剩下至少两人
     * 4.调用者与上一环节处理人是同一人
     * 5.defaultAllowRemove 为true
     * </pre>
     * 
     * @param instId          主流程实例ID
     * @param caller          调用者
     * @param removeOperators 待减签人列表
     * @param props           扩展属性
     * @return ActionResult 可以删除所有的尚未参与者 ，删除所有后，会跳过当前子流程，返回主流程可以操作的actionId 和name
     *
     *         throws TaskProcessException 系统参数不合法 以及减签出错都会抛出该异常
     */
    public com.hd.rcugrc.bpm.app.ActionResult removeSubflowOperator(long instId, User caller,
            SubflowOperator[] removeOperators, FormParamInfo formParams, Map<String, String> props)
            throws TaskProcessException;

    /**
     * 多人并行环节(环节类型为子流程)减少处理人 每个处理人都对应着一个子流程实例
     * 
     * <pre>
     * 需要满足以下所有条件，才可以进行减签：
     * 1.当前环节为多人并行环节
     * 2.环节类型为子流程
     * 3.尚未处理流程的参与还剩下至少两人
     * 4.调用者与上一环节处理人是同一人
     * 5.defaultAllowRemove 为true
     * </pre>
     * 
     * @param instId          主流程实例ID
     * @param caller          调用者
     * @param removeOperators 待减签人列表
     * @param props           扩展属性
     * @return ActionResult 可以删除所有的尚未参与者 ，删除所有后，会跳过当前子流程，返回主流程可以操作的actionId 和name
     *
     *         throws TaskProcessException 系统参数不合法 以及减签出错都会抛出该异常
     */
    public com.hd.rcugrc.bpm.app.ActionResult removeSubflowOperator(long instId, long activeStepId, User caller,
            SubflowOperator[] removeOperators, FormParamInfo formParams, Map<String, String> props)
            throws TaskProcessException;
    
    /**
     * 
     * 是否可以为多人并行环节(环节类型为子流程)减少处理人
     * 
     * <pre>
     * 需要满足以下所有条件，才可以进行减签：
     * 1.当前环节为多人并行环节
     * 2.环节类型为子流程
     * 3.调用者与上一环节处理人是同一人
     * 4.defaultAllowRemove 为true
     * 5.不处于开始环节
     * </pre>
     * <p>
     * 该方法如果返回true，那{@link #canRemoveOperator}方法返回是false
     * </p>
     * 
     * @param instId             流程实例ID
     * @param caller             调用者
     * @param defaultAllowRemove 是否允许减少处理人 ，true 允许 false 不允许
     * 
     *                           <pre>
     *  defaultAllowAdd
     *  如果为false 时，结果直接返回false
     *  如果为true 时，则校验该环节是否满足减签条件，如果满足则返回 true ，不满足则返回false
     *  如果是编辑页面时，需将defaultAllowAdd设置为false,查看页面时设置成true(已办页面中才可以进行减签)
     *                           </pre>
     * 
     * @return boolean ： true 能为多人并行环节减少处理人， false 不能为多人并行环节减少处理人
     */
    public boolean canRemoveSubflowOperator(long instId, User caller, boolean defaultAllowRemove)
            throws TaskProcessException;

    /**
     * 是否可以为多人并行环节(环节类型为子流程)列表人员进行减签
     * 
     * <pre>
     * 需要满足以下所有条件，才可以进行减签：
     * 1.当前环节为多人并行环节
     * 2.环节类型为子流程
     * 3.调用者与上一环节处理人是同一人
     * 4.defaultAllowRemove 为true
     * 5.不处于开始环节
     * 6.子流程实例的参与者和被减签人员是同一人
     * 7.所有发起的子流程中已经存在开始办理的
     * </pre>
     * 
     * @param subflowOperator 子流程处理人集合
     * @return boolean ： true 多人并行环节所选处理人能够进行减签， false 多人并行环节所选处理人不能够进行减签
     */
    public boolean canRemoveSubListflowOperator(SubflowOperator[] subflowOperator) throws TaskProcessException;

    /**
     * 获取当前环节为子流程待处理人员列表
     * <p>
     * 主要用于展示可减签人员列表
     * </p>
     * 
     * @param instId 流程实例ID
     * @return 环节待处理人员列表 包含处理完成和处理中的人员列表 throws TaskProcessException 系统参数不合法
     */
    public SubflowActiveStepOperator[] getSubFlowActiveStepOperators(long instId) throws TaskProcessException;

    /**
     * 是否可以催办 目前不支持主流程对子流程催办
     * 
     * @param instId 流程实例
     * @param caller 调用者
     * @return true 可以催办，false 不能催办 throws TaskProcessException 系统参数不合法
     */
    public boolean canReminder(long instId, long activeStepId, User caller) throws TaskProcessException;

    /**
     * 发送催办任务
     * 
     * @param instId        流程实例ID
     * @param caller        调用者
     * @param content       催办内容
     * @param reminderUsers 催办人员列表
     * @param formParams
     * @param props         扩展参数
     * @return 催办信息列表 throws TaskProcessException 系统参数不合法
     */
    public ReminderTaskInfo[] reminder(long instId, long activeStepId, User caller, String content,
            ReminderUser[] reminderUsers, FormParamInfo formParams, Map<String, String> props)
            throws TaskProcessException;

    /**
     * 获取可催办人员
     * 
     * @param instId 流程实例ID
     * @param caller 调用者
     * @return 催办信息列表 throws TaskProcessException 系统参数不合法
     */
    public ReminderUser[] getReminderUsers(long instId, long activeStepId, User caller) throws TaskProcessException;

    /**
     * 查看催办信息
     * 
     * @param extraId 催办ID
     * @param caller  调用者
     * @return true 成功，false 失败 throws TaskProcessException 系统参数不合法
     */
    public boolean setReminderSeen(long extraId, User caller) throws TaskProcessException;

    /**
     * 获取流程实例当前环节待处理人员列表
     * <p>
     * 主要用于展示可减签人员列表
     * 
     * @param instId 流程实例ID
     * @return 环节待处理人员列表 throws TaskProcessException 系统参数不合法
     */
    public TaskActiveStepOperator getTaskActiveStepUsers(long instId) throws TaskProcessException;
    
    /**
     * 多人并行环节(环节类型为非子流程)增加处理人
     * 
     * <pre>
     * 需要满足以下所有条件，才可以进行加签：
     * 1.当前环节为多人并行环节
     * 2.环节类型为非子流程
     * 3.处理人还剩下至少一人
     * 4.调用者与上一环节处理人是同一人
     * 5.defaultAllowAdd 为true
     * </pre>
     *                        <pre>
     * 该流程中包含子流程时
     * formParams参数中必须包含以下属性
     * formId 表单ID  包含协议前缀 如推荐：非平台表单  tj:testForm；平台表单  model:testForm 
     * 如果是非平台表单，则还需要包含
     *      bean 实体bean对象，该bean方式设置方法 有两种  
     *          调用此方法时直接通过formParams设置
     * <b>如果bean 为空则将 formParams中的inputs 参数作为bean</b>
     *  beanTitleProperty 实体bean对象中作为标题字段属性
     *  beanIdProperty 实体bean对象中作为主键ID字段属性
     *  beanIdType 实体bean对象中作为主键ID类型  目前仅支持string 、 long
     *                        </pre>
     * 
     * @param props           扩展属性
     * @return true 加签成功 ，false 加签失败
     *
     *         throws TaskProcessException 系统参数不合法 以及加签办出错都会抛出该异常
     */
    public boolean addOperator(String caller, AddOperator addOperator) throws TaskProcessException;
    
    /**
     * 
     * @param caller
     * @param addSubflowOperator
     * @return
     * @throws TaskProcessException
     */
    public boolean addSubflowOperator(String caller, AddSubFlowOperator addSubflowOperator) throws TaskProcessException;
    
    /**
     * 通用加签方法
     * 可以对多人环节，子流程环节 加签
     * @param caller
     * @param addGeneralOperator
     * @return
     * @throws TaskProcessException
     */
    public boolean addGeneralOperator(String caller, AddGeneralOperator addGeneralOperator)
            throws TaskProcessException;
    
    /**
     * 通用减签方法
     * 可以对多人环节，子流程环节 减签
     * @param caller 调用者
     * @param addGeneralOperator
     * @return ProcessActiveInfo[] 当前环节 与 执行人信息
     * @throws TaskProcessException
     */
    public ProcessActiveInfo[] removeGeneralOperator(String caller, RemoveGeneralOperator removeGeneralOperator)
            throws TaskProcessException;
    
    /**
     * 删除子流程执行者
     * @param removeSubflowOperator
     * @return
     * @throws TaskProcessException
     */
    public com.hd.rcugrc.bpm.facade.mobile.mvc.query.ActionResult removeSubflowOperator(String caller, 
            RemoveSubflowOperator removeSubflowOperator) throws TaskProcessException;
    
    /**
     * 删除多人环节程执行者
     * @param caller
     * @param removeOperators
     * @return
     * @throws TaskProcessException
     */
    public TodoResult removeOperators(String caller, RemoveOperators removeOperators) throws TaskProcessException;
    /**
     * 
     * @param timer
     * @return
     */
    public com.hd.rcugrc.bpm.TimeConstraintDefinition getFlowTimeConstraintDefinition(TimeConstraintDefinition timer);

    /**
     * 获取指定流程实例当前活动环节的上一处理环节
     * @param instId 流程实例ID
     * @return
     * @throws TaskProcessException
     */
    public TargetFlowStepInfo[] getActivePreStep(long instId) throws TaskProcessException;
}
