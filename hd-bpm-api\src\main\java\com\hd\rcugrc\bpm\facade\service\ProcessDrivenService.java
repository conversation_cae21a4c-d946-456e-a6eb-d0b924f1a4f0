/*
 * Copyright 2013-2019 Smartdot Technologies Co., Ltd. All rights reserved.
 * SMARTDOT PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 */
package com.hd.rcugrc.bpm.facade.service;

import java.util.Map;

import com.hd.rcugrc.bpm.ActiveStep;
import com.hd.rcugrc.bpm.Candidate;
import com.hd.rcugrc.bpm.FlowCallback;
import com.hd.rcugrc.bpm.FlowDefinition;
import com.hd.rcugrc.bpm.FlowInstance;
import com.hd.rcugrc.bpm.LockInfo;
import com.hd.rcugrc.bpm.Operator;
import com.hd.rcugrc.bpm.User;
import com.hd.rcugrc.bpm.app.ActionResult;
import com.hd.rcugrc.bpm.facade.DispatchTaskInfo;
import com.hd.rcugrc.bpm.facade.FlowFormBeanRelation;
import com.hd.rcugrc.bpm.facade.FormParamInfo;
import com.hd.rcugrc.bpm.facade.RollbackStep;
import com.hd.rcugrc.bpm.facade.TimeConstraintDefinition;
import com.hd.rcugrc.bpm.facade.WorkflowSubmitInfo;
import com.hd.rcugrc.bpm.facade.exception.ProcessDrivenException;
import com.hd.rcugrc.bpm.facade.exception.TaskProcessException;
import com.hd.rcugrc.bpm.facade.form.TaskButton;
import com.hd.rcugrc.bpm.facade.form.TaskPageAllButtons;
import com.hd.rcugrc.form.FormInfo;
import com.hd.rcugrc.form.dao.FlowFormRelation;
import com.hd.rcugrc.mvc.bpm.CandidateInfo;
import com.hd.rcugrc.mvc.bpm.ParticipantInfo;
import com.hd.rcugrc.web.dwr.criterion.Expression;

/**
 * <p>
 * 流程驱动 用于流程实例的新建、保存，驱动流程实例的前进和后退 主要负责流程实例新建、保存、提交、退回、撤回、获取环节处理人等方法
 * 
 * <AUTHOR> href="mailto:<EMAIL>">pandy</a>
 * @version 1.0, 2019年3月7日
 */
public interface ProcessDrivenService {
    /**
     * 获取已启动流程实例操作按钮
     * 
     * @param instId       流程实例ID
     * @param activeStepId 当前活动环节ID
     * @param caller       调用者
     * @param formParam    表单相关参数 ，引擎不处理该数据，仅转发
     * @return String[][]
     * 
     *         <pre>
     *         new String[][] { 
     *              new String[] { &quot;操作id&quot;, &quot;操作显示文字&quot; }, 
     *              new String[] { &quot;操作id&quot;, &quot;操作显示文字&quot; } 
     *         }
     *         </pre>
     * 
     * @throws Exception
     */
    public String[][] getProcessActions(long instId, long activeStepId, User caller, Map<String, Object> formParam)
            throws Exception;

    /**
     * 获取已启动流程实例操作按钮 button 对象
     * 
     * @param instId       流程实例ID
     * @param activeStepId 当前活动环节ID
     * @param caller       调用者
     * @param formParam    表单相关参数 ，引擎不处理该数据，仅转发
     * @return TaskPageAllButtons
     *         </pre>
     * 
     * @throws Exception
     */
    public TaskPageAllButtons getProcessButtons(long instId, long activeStepId, User caller, String mode,
            Map<String, Object> context) throws Exception;

    /**
     * 获取已启动流程实例操作按钮 button 对象
     * 用于开始环节 没有instid和activeStepId的情况
     * @param flowId
     * @param caller
     * @param mode
     * @param context
     * @return
     * @throws Exception
     */
    public TaskPageAllButtons getStartButtons(String flowId, User caller, String mode,
            Map<String, Object> context) throws Exception;
    /**
     * 获取业务按钮 数组
     * 
     * @param instId
     * @param activeStepId
     * @param user
     * @param mode
     * @param context
     * @return
     * @throws TaskProcessException
     */
    public TaskButton[] getBusinessTaskButtons(long instId, long activeStepId, User user, String mode,
            Map<String, Object> context) throws TaskProcessException;

    /**
     * 获取业务按钮 数组
     * 
     * @param instId
     * @param activeStepId
     * @param user
     * @param mode
     * @param context
     * @return
     * @throws TaskProcessException
     */
    public TaskPageAllButtons getPushAndSaveTaskButtons(long instId, long activeStepId, User user, String mode,
            Map<String, Object> context) throws Exception;

    /**
     * 获取业务按钮 数组 用于没有流程实例和当前环节的情况（开始环节）
     * 
     * @param flowId
     * @param caller
     * @param mode
     * @param context
     * @return
     * @throws Exception
     */
    public TaskPageAllButtons getPushAndSaveTaskButtons(String flowId, User caller, String mode,
            Map<String, Object> context) throws Exception;
    
    /**
     * 获取启动流程实例可操作按钮
     * 
     * @param flowId 流程定义ID
     * @param caller 调用者
     * @return String[][]
     * 
     *         <pre>
     *          new String[][] { 
     *              new String[] { &quot;操作id&quot;, &quot;操作显示文字&quot; }, 
     *              new String[] { &quot;操作id&quot;, &quot;操作显示文字&quot; } 
     *           }
     *         </pre>
     * 
     * @throws Exception
     */
    public String[][] getStartActions(String flowId, User caller) throws Exception;
    /**
     * 获取启动流程实例可操作按钮
     * 
     * @param flowId 流程定义ID
     * @param caller 调用者
     * @return String[][]
     * 
     * <pre>
     *  new String[][] { 
     *      new String[] { &quot;操作id&quot;, &quot;操作显示文字&quot; }, 
     *      new String[] { &quot;操作id&quot;, &quot;操作显示文字&quot; } 
     *  }
     * </pre>
     * 
     * @throws Exception
     */
    public String[][] getStartActions(String flowId, User caller, Map<String, Object> context) throws Exception;
    
    /**
     * 获取启动流程实例可操作按钮
     * 
     * @param flowId 流程定义ID
     * @param stepId 环节ID
     * @param caller 调用者
     * @param inputs 表单数据
     * @return String[][]
     * 
     *         <pre>
     *         new String[][] { 
     *              new String[] { &quot;操作id&quot;, &quot;操作显示文字&quot; }, 
     *              new String[] { &quot;操作id&quot;, &quot;操作显示文字&quot; } 
     *         }
     *         </pre>
     * @throws ProcessDrivenException 
     * 
     * @throws Exception
     */
    public default String[][] getActionsDynamic(String flowId, String stepId, User caller, Map<String, Object> inputs)
            throws ProcessDrivenException {
        return null;
    };

    /**
     * <pre>
     * 创建并启用流程实例，使用场景：启动流程，进行保存操作
     * &#64;param flowId 流程定义ID
     * &#64;param actionId 操作ID 可以为空，为空时，系统将自动计算可操作的ActionId 
     * &#64;param entryType 业务类型
     * &#64;param formParams 表单相关参数  ，引擎不处理该数据，仅转发
     * <pre>
     * formParams参数中必须包含以下属性
     * formId 表单ID  包含协议前缀 如推荐：非平台表单  tj:testForm；平台表单  model:testForm 
     * 如果是非平台表单，则还需要包含
     *      bean 实体bean对象，该bean方式设置方法 有两种  
     *          方式一：调用此方法时直接通过formParams设置
     *          方式二：使用接口{@link FormHandlerService}实现类中方法saveOrUpdateFormData 中进行赋值
     * <b>如果bean 为空则将 formParams中的inputs 参数作为bean</b>
     *  beanTitleProperty 实体bean对象中作为标题字段属性
     *  beanIdProperty 实体bean对象中作为主键ID字段属性
     *  beanIdType 实体bean对象中作为主键ID类型  目前仅支持string 、 long
     * </pre>
     * 
     * @param caller  调用者
     * @param handler 流程推进前后，目前主要用于驱动流程到子流程环节时，创建子流程已表单实例关系
     * 
     *                <pre>
     *   
     * 使用场景：流程中有子流程,且下一环节时创建子流程时需要传入回调函数
     * 用来建立子流程 与表单数据关联关系
     * 
     * 
     * 使用平台表单样例
     * {@link com.hd.rcugrc.mvc.bpm.SubflowDataBinder}
     * 使用非平台表单样例
     * {@link com.hd.rcugrc.bpm.facade.service.impl.CustomSubflowDataBinder}
     * 如需要驱动主流程到子流程需要在xml中注入“subflowDataBinderCallback”并设置 scope="prototype" 然后将该bean传过来
     *                </pre>
     * 
     * @return ActionResult 流程环节相关信息
     * @throws ProcessDrivenException 流程驱动类相关异常信息
     */
    public ActionResult startProcessInstance(String flowId, String actionId, String entryType, FormParamInfo formParams,
            User caller, FlowCallback handler) throws ProcessDrivenException;

    /**
     * <pre>
     * 创建并启用流程实例，使用场景：启动流程，进行保存操作
     * &#64;param flowId 流程定义ID
     * &#64;param actionId 操作ID 可以为空，为空时，系统将自动计算可操作的ActionId 
     * &#64;param entryType 业务类型
     * &#64;param formParams 表单相关参数  ，引擎不处理该数据，仅转发
     * <pre>
     * formParams参数中必须包含以下属性
     * formId 表单ID  包含协议前缀 如推荐：非平台表单  tj:testForm；平台表单  model:testForm 
     * 如果是非平台表单，则还需要包含
     *      bean 实体bean对象，该bean方式设置方法 有两种  
     *          方式一：调用此方法时直接通过formParams设置
     *          方式二：使用接口{@link FormHandlerService}实现类中方法saveOrUpdateFormData 中进行赋值
     * <b>如果bean 为空则将 formParams中的inputs 参数作为bean</b>
     *  beanTitleProperty 实体bean对象中作为标题字段属性
     *  beanIdProperty 实体bean对象中作为主键ID字段属性
     *  beanIdType 实体bean对象中作为主键ID类型  目前仅支持string 、 long
     * </pre>
     * 
     * @param caller  调用者
     * @param handler 流程推进前后，目前主要用于驱动流程到子流程环节时，创建子流程已表单实例关系
     * 
     *                <pre>
     *   
     * 使用场景：流程中有子流程,且下一环节时创建子流程时需要传入回调函数
     * 用来建立子流程 与表单数据关联关系
     * 
     * 
     * 使用平台表单样例
     * {@link com.hd.rcugrc.mvc.bpm.SubflowDataBinder}
     * 使用非平台表单样例
     * {@link com.hd.rcugrc.bpm.facade.service.impl.CustomSubflowDataBinder}
     * 如需要驱动主流程到子流程需要在xml中注入“subflowDataBinderCallback”并设置 scope="prototype" 然后将该bean传过来
     *                </pre>
     * 
     * @return ActionResult 流程环节相关信息
     * @throws ProcessDrivenException 流程驱动类相关异常信息
     */
    public ActionResult startProcessInstance(String flowId, String actionId, String entryType, FormParamInfo formParams,
            User caller, FlowCallback handler, Map<String, String> props) throws ProcessDrivenException;

    /**
     * <pre>
     * 创建并推动流程实例，使用场景：启动流程，进行提交操作
     * </pre>
     * 
     * @param flowId       流程定义ID
     * @param actionId     操作ID
     * @param entryType    业务类型
     * @param participants 推进环节ID 以及流程参与者
     * @param formParams   表单相关参数 ，引擎不处理该数据，仅转发
     * 
     *                     <pre>
     * formParams参数中必须包含以下属性
     * formId 表单ID  包含协议前缀 如推荐：非平台表单  tj:testForm；平台表单  model:testForm 
     * 如果是非平台表单，则还需要包含
     *      bean 实体bean对象，该bean方式设置方法 有两种  
     *          方式一：调用此方法时直接通过formParams设置
     *          方式二：使用接口{@link FormHandlerService}实现类中方法 saveOrUpdateFormData中进行赋值
     * <b>如果bean 为空则将 formParams中的inputs 参数作为bean</b>
     *  beanTitleProperty 实体bean对象中作为标题字段属性
     *  beanIdProperty 实体bean对象中作为主键ID字段属性
     *  beanIdType 实体bean对象中作为主键ID类型  目前仅支持string 、 long
     *                     </pre>
     * 
     * @param caller       调用者
     * @param handler      流程推进前后，目前主要用于驱动流程到子流程环节时，创建子流程已表单实例关系
     * 
     *                     <pre>
     *   
     * 使用场景：流程中有子流程,且下一环节时创建子流程时需要传入回调函数
     * 用来建立子流程 与表单数据关联关系
     * 
     * 
     * 使用平台表单样例
     * {@link com.hd.rcugrc.mvc.bpm.SubflowDataBinder}
     * 使用非平台表单样例
     * {@link com.hd.rcugrc.bpm.facade.service.impl.CustomSubflowDataBinder}
     * 如需要驱动主流程到子流程需要在xml中注入“subflowDataBinderCallback”并设置 scope="prototype" 然后将该bean传过来
     *                     </pre>
     * 
     * @return ActionResult流程环节相关信息
     * 
     * @throws ProcessDrivenException 流程驱动类相关异常信息
     */
    public ActionResult startAndDispatchProcessInstance(String flowId, String actionId, String entryType,
            ParticipantInfo[] participants, FormParamInfo formParams, User caller, FlowCallback handler)
            throws ProcessDrivenException;

    /**
     * <pre>
     * 创建并推动流程实例，使用场景：启动流程，进行提交操作
     * </pre>
     * 
     * @param flowId       流程定义ID
     * @param actionId     操作ID
     * @param entryType    业务类型
     * @param participants 推进环节ID 以及流程参与者
     * @param formParams   表单相关参数 ，引擎不处理该数据，仅转发
     * 
     *                     <pre>
     * formParams参数中必须包含以下属性
     * formId 表单ID  包含协议前缀 如推荐：非平台表单  tj:testForm；平台表单  model:testForm 
     * 如果是非平台表单，则还需要包含
     *      bean 实体bean对象，该bean方式设置方法 有两种  
     *          方式一：调用此方法时直接通过formParams设置
     *          方式二：使用接口{@link FormHandlerService}实现类中方法 saveOrUpdateFormData中进行赋值
     * <b>如果bean 为空则将 formParams中的inputs 参数作为bean</b>
     *  beanTitleProperty 实体bean对象中作为标题字段属性
     *  beanIdProperty 实体bean对象中作为主键ID字段属性
     *  beanIdType 实体bean对象中作为主键ID类型  目前仅支持string 、 long
     *                     </pre>
     * 
     * @param caller       调用者
     * @param handler      流程推进前后，目前主要用于驱动流程到子流程环节时，创建子流程已表单实例关系
     * 
     *                     <pre>
     *   
     * 使用场景：流程中有子流程,且下一环节时创建子流程时需要传入回调函数
     * 用来建立子流程 与表单数据关联关系
     * 
     * 
     * 使用平台表单样例
     * {@link com.hd.rcugrc.mvc.bpm.SubflowDataBinder}
     * 使用非平台表单样例
     * {@link com.hd.rcugrc.bpm.facade.service.impl.CustomSubflowDataBinder}
     * 如需要驱动主流程到子流程需要在xml中注入“subflowDataBinderCallback”并设置 scope="prototype" 然后将该bean传过来
     *                     </pre>
     * 
     * @param props        扩展属性，通过扩展属性来标记请求访问来源 {@code}key:source,value:mobile属性
     * @return ActionResult流程环节相关信息
     * 
     * @throws ProcessDrivenException 流程驱动类相关异常信息
     */
    public ActionResult startAndDispatchProcessInstance(String flowId, String actionId, String entryType,
            ParticipantInfo[] participants, FormParamInfo formParams, User caller, FlowCallback handler,
            Map<String, String> props) throws ProcessDrivenException;

    /**
     * 将参数转换调用{@link com.hd.rcugrc.bpm.WorkflowDoActionService}，统一流程提交的同步异步方式
     * 
     * @param flowId
     * @param actionId
     * @param entryType
     * @param participants
     * @param formParams
     * @param caller
     * @param handler
     * @param props
     * @return
     * @throws ProcessDrivenException
     */
    public ActionResult startAndDispatchProcessInstanceAsync(String flowId, String actionId, String entryType,
            ParticipantInfo[] participants, FormParamInfo formParams, User caller, FlowCallback handler,
            Map<String, String> props) throws ProcessDrivenException;
    
    /**
     * 区别于{@link ProcessDrivenService#startAndDispatchProcessInstanceAsync(
     * String, String, String, ParticipantInfo[], FormParamInfo, User, FlowCallback, Map)}<br>
     * 将异步提交的部分去除，不在由此方法管理，<br>
     * 目的在于此方法准备工作完成后事务结束后将准备好的数据交由外部方法执行异步提交，不在讲异步提交的工作放在此事务内
     * @param flowId
     * @param actionId
     * @param entryType
     * @param participants
     * @param formParams
     * @param caller
     * @param handler
     * @param props
     * @return
     * @throws ProcessDrivenException
     */
    public ActionResult prepareStartDispatchInfoAsync(DispatchTaskInfo info) throws ProcessDrivenException;
    
    /**
     * 在{
     * {@link #startAndDispatchProcessInstance(String, String, String, ParticipantInfo[], FormParamInfo, User, 
     *  FlowCallback, Map)}} 基础上增加asynchronizedSave
     * 同步时调用之前的方法。异步时，将参数转换调用{@link com.hd.rcugrc.bpm.WorkflowDoActionService}，统一流程提交的同步异步方式
     * 
     * @param flowId
     * @param actionId
     * @param entryType
     * @param participants
     * @param formParams
     * @param caller
     * @param handler
     * @param props
     * @param asynchronizedSave
     * @return
     * @throws ProcessDrivenException
     */
    public ActionResult startAndDispatchProcessInstance(String flowId, String actionId, String entryType,
            ParticipantInfo[] participants, FormParamInfo formParams, User caller, FlowCallback handler,
            Map<String, String> props, boolean asynchronizedSave) throws ProcessDrivenException;

    /**
     * 在{{@link #startAndDispatchProcessInstance(String flowId, String actionId,
     * String entryType, ParticipantInfo[] participants, FormParamInfo formParams,
     * User caller, FlowCallback handler, Map<String, String> props, boolean
     * asynchronizedSave)} } 基础上增加instProps流程实例扩展属性
     * 同步时调用之前的方法。异步时，将参数转换调用{@link com.hd.rcugrc.bpm.WorkflowDoActionService}，统一流程提交的同步异步方式
     * 
     * @param dispatch
     * @param asynchronizedSave
     * @return
     * @throws ProcessDrivenException
     */
    public ActionResult startAndDispatchProcessInstance(DispatchTaskInfo dispatch, boolean asynchronizedSave)
            throws ProcessDrivenException;

    /**
     * 已启用流程实例进行暂存，使用场景：已启动流程，进行页面编辑并进行保存操作
     * 
     * @param instId       流程实例ID
     * @param actionId     操作ID
     * @param activeStepId 当前流程活动环节ID
     * @param lockId       流程锁ID ，悲观锁时，前台页面打开时需要先上锁，保存结束后再解锁 ，乐观锁默认传0即可
     * @param formParams   表单相关参数 ，引擎不处理该数据，仅转发
     * @param caller       调用者
     * @param handler      流程推进前后，目前主要用于驱动流程到子流程环节时，创建子流程已表单实例关系
     * 
     *                     <pre>
     *   
     * 使用场景：流程中有子流程,且下一环节时创建子流程时需要传入回调函数
     * 用来建立子流程 与表单数据关联关系
     * 
     * 
     * 使用平台表单样例
     * {@link com.hd.rcugrc.mvc.bpm.SubflowDataBinder}
     * 使用非平台表单样例
     * {@link com.hd.rcugrc.bpm.facade.service.impl.CustomSubflowDataBinder}
     *                     </pre>
     * 
     *                     如需要驱动主流程到子流程需要在xml中注入“subflowDataBinderCallback”并设置
     *                     scope="prototype" 然后将该bean传过来
     * @throws ProcessDrivenException 流程驱动类相关异常信息
     */
    public ActionResult saveProcessInstance(long instId, String actionId, long activeStepId, long lockId,
            FormParamInfo formParams, User caller, FlowCallback handler) throws ProcessDrivenException;

    /**
     * 已启用流程实例进行暂存，使用场景：已启动流程，进行页面编辑并进行保存操作
     * 
     * @param instId       流程实例ID
     * @param actionId     操作ID
     * @param activeStepId 当前流程活动环节ID
     * @param lockId       流程锁ID ，悲观锁时，前台页面打开时需要先上锁，保存结束后再解锁 ，乐观锁默认传0即可
     * @param formParams   表单相关参数 ，引擎不处理该数据，仅转发
     * @param caller       调用者
     * @param handler      流程推进前后，目前主要用于驱动流程到子流程环节时，创建子流程已表单实例关系
     * 
     *                     <pre>
     *   
     * 使用场景：流程中有子流程,且下一环节时创建子流程时需要传入回调函数
     * 用来建立子流程 与表单数据关联关系
     * 
     * 使用平台表单样例
     * {@link com.hd.rcugrc.mvc.bpm.SubflowDataBinder}
     * 使用非平台表单样例
     * {@link com.hd.rcugrc.bpm.facade.service.impl.CustomSubflowDataBinder}
     * 如需要驱动主流程到子流程需要在xml中注入“subflowDataBinderCallback”并设置 scope="prototype" 然后将该bean传过来
     *                     </pre>
     * 
     * @param props        扩展属性，通过扩展属性来标记请求访问来源 {@code}key:source,value:mobile属性
     * @return ActionResult 流程环节相关信息
     * @throws ProcessDrivenException 流程驱动类相关异常信息
     */
    public ActionResult saveProcessInstance(long instId, String actionId, long activeStepId, long lockId,
            FormParamInfo formParams, User caller, FlowCallback handler, Map<String, String> props)
            throws ProcessDrivenException;

    /**
     * 向前推动流程实例，使用场景：流程流转，进行提交操作
     * 
     * <pre>
     * 向最后一个环节提交时，不需要参与者，参与者 participants数组设置为 长度为0 即可
     * 判断是否向最后一个环节提交，调用参数来确定{@link #isEndStep(FlowDefinition, String, String)}
     * </pre>
     * 
     * @param instId       流程实例ID
     * @param actionId     操作ID
     * @param activeStepId 流程实例活动环节ID
     * @param lockId       锁流程ID ，悲观锁时，前台页面打开时需要先上锁，保存结束后再解锁，乐观锁默认传0即可
     * @param participants 推进环节ID 以及参与者
     * @param formParams   表单相关参数 ，引擎不处理该数据，仅转发
     * 
     *                     <pre>
     * 该流程中包含子流程时
     * formParams参数中必须包含以下属性
     * formId 表单ID  包含协议前缀 如推荐：非平台表单  tj:testForm；平台表单  model:testForm 
     * 如果是非平台表单，则还需要包含
     *      bean 实体bean对象，该bean方式设置方法 有两种  
     *          方式一：调用此方法时直接通过formParams设置
     *          方式二：使用接口{@link FormHandlerService}实现类中方法 saveOrUpdateFormData中进行赋值
     * <b>如果bean 为空则将 formParams中的inputs 参数作为bean</b>
     *  beanTitleProperty 实体bean对象中作为标题字段属性
     *  beanIdProperty 实体bean对象中作为主键ID字段属性
     *  beanIdType 实体bean对象中作为主键ID类型  目前仅支持string 、 long
     *                     </pre>
     * 
     * @param caller       调用者
     * @param handler      流程推进前后，目前主要用于驱动流程到子流程环节时，创建子流程已表单实例关系
     * 
     *                     <pre>
     *   
     * 使用场景：流程中有子流程,且下一环节时创建子流程时需要传入回调函数
     * 用来建立子流程 与表单数据关联关系
     * 
     * 
     * 使用平台表单样例
     * {@link com.hd.rcugrc.mvc.bpm.SubflowDataBinder}
     * 使用非平台表单样例
     * {@link com.hd.rcugrc.bpm.facade.service.impl.CustomSubflowDataBinder}
     * 如需要驱动主流程到子流程需要在xml中注入“subflowDataBinderCallback”并设置 scope="prototype" 然后将该bean传过来
     *                     </pre>
     * 
     * @return ActionResult 流程环节相关信息
     * @throws ProcessDrivenException 流程驱动类相关异常信息
     */
    public ActionResult dispatchTask(long instId, String actionId, long activeStepId, long lockId,
            ParticipantInfo[] participants, FormParamInfo formParams, User caller, FlowCallback handler)
            throws ProcessDrivenException;

    /**
     * 根据asynchronizedSave分发，是同步处理还是异步处理
     * 
     * @param instId
     * @param actionId
     * @param activeStepId
     * @param lockId
     * @param participants
     * @param formParams
     * @param caller
     * @param handler
     * @param props
     * @param asynchronizedSave
     * @return
     * @throws ProcessDrivenException
     */
    public ActionResult dispatchTask(long instId, String actionId, long activeStepId, long lockId,
            ParticipantInfo[] participants, FormParamInfo formParams, User caller, FlowCallback handler,
            Map<String, String> props, boolean asynchronizedSave) throws ProcessDrivenException;

    /**
     * 根据asynchronizedSave分发，是同步处理还是异步处理
     * 
     * @param info              流程处理信息
     * @param asynchronizedSave 同步or异步处理
     * @return
     * @throws ProcessDrivenException
     */
    public ActionResult dispatchTask(DispatchTaskInfo info, boolean asynchronizedSave) throws ProcessDrivenException;

    /**
     * 向前推动流程实例，使用场景：流程流转，进行提交操作
     * 
     * <pre>
     * 向最后一个环节提交时，不需要参与者，参与者 participants数组设置为 长度为0 即可
     * 判断是否向最后一个环节提交，调用参数来确定{@link #isEndStep(FlowDefinition, String, String)}
     * </pre>
     * 
     * @param instId       流程实例ID
     * @param actionId     操作ID
     * @param activeStepId 流程实例活动环节ID
     * @param lockId       锁流程ID ，悲观锁时，前台页面打开时需要先上锁，保存结束后再解锁，乐观锁默认传0即可
     * @param participants 推进环节ID 以及参与者
     * @param formParams   表单相关参数 ，引擎不处理该数据，仅转发
     * 
     *                     <pre>
     * 该流程中包含子流程时
     * formParams参数中必须包含以下属性
     * formId 表单ID  包含协议前缀 如推荐：非平台表单  tj:testForm；平台表单  model:testForm 
     * 如果是非平台表单，则还需要包含
     *      bean 实体bean对象，该bean方式设置方法 有两种  
     *          方式一：调用此方法时直接通过formParams设置
     *          方式二：使用接口{@link FormHandlerService}实现类中方法 saveOrUpdateFormData中进行赋值
     * <b>如果bean 为空则将 formParams中的inputs 参数作为bean</b>
     *  beanTitleProperty 实体bean对象中作为标题字段属性
     *  beanIdProperty 实体bean对象中作为主键ID字段属性
     *  beanIdType 实体bean对象中作为主键ID类型  目前仅支持string 、 long
     *                     </pre>
     * 
     * @param caller       调用者
     * @param handler      流程推进前后
     * 
     *                     <pre>
     *   
     * 使用场景：流程中有子流程,且下一环节时创建子流程时需要传入回调函数
     * 用来建立子流程 与表单数据关联关系
     * 
     * 
     * 使用平台表单样例
     * {@link com.hd.rcugrc.mvc.bpm.SubflowDataBinder}
     * 使用非平台表单样例
     * {@link com.hd.rcugrc.bpm.facade.service.impl.CustomSubflowDataBinder}
     * 如需要驱动主流程到子流程需要在xml中注入“subflowDataBinderCallback”并设置 scope="prototype" 然后将该bean传过来
     *                     </pre>
     * 
     * @param props        扩展属性，通过扩展属性来标记请求访问来源 {@code}key:source,value:mobile
     * 
     * @return ActionResult 流程环节相关信息
     * @throws ProcessDrivenException 流程驱动类相关异常信息
     */
    public ActionResult dispatchTask(long instId, String actionId, long activeStepId, long lockId,
            ParticipantInfo[] participants, FormParamInfo formParams, User caller, FlowCallback handler,
            Map<String, String> props) throws ProcessDrivenException;

    /**
     * 
     * @param instId
     * @param actionId
     * @param activeStepId
     * @param lockId
     * @param participants
     * @param formParams
     * @param caller
     * @param handler
     * @param props
     * @return
     * @throws ProcessDrivenException
     */
    public ActionResult dispatchTaskAsync(long instId, String actionId, long activeStepId, long lockId,
            ParticipantInfo[] participants, FormParamInfo formParams, User caller, FlowCallback handler,
            Map<String, String> props) throws ProcessDrivenException;

    /**
     * 回退流程实例，使用场景：流程退回到指定环节
     * 
     * @param instId             流程实例ID
     * @param activeStepId       当前流程活动环节ID
     * @param histroyStepId      指定退回历史操作ID
     * @param rollbackToUser     退回给指定用户 ,如果为空，则根据退回历史环节进行计算人员；
     * @param deliveryToThisStep 是否直送到此环节，true 直送到此环节，false 从退回环节开始逐个环节驱动
     * @param formParams         表单相关参数 ，引擎不处理该数据，仅转发
     * @param caller             调用者
     * @param handler            流程推进前后，目前主要用于驱动流程到子流程环节时，创建子流程已表单实例关系
     * 
     *                           <pre>
     *   
     * 使用场景：流程中有子流程,且下一环节时创建子流程时需要传入回调函数
     * 用来建立子流程 与表单数据关联关系
     * 使用平台表单样例
     * {@link com.hd.rcugrc.mvc.bpm.SubflowDataBinder}
     * 使用非平台表单样例
     * {@link com.hd.rcugrc.bpm.facade.service.impl.CustomSubflowDataBinder}
     * 如需要驱动主流程到子流程需要在xml中注入“subflowDataBinderCallback”并设置 scope="prototype" 然后将该bean传过来
     *                           </pre>
     * 
     * @return boolean true 成功 false 失败
     * @throws ProcessDrivenException 流程驱动类相关异常信息
     */
    public boolean rejectTask(long instId, long activeStepId, long histroyStepId, User[] rollbackToUser,
            boolean deliveryToThisStep, FormParamInfo formParams, User caller, FlowCallback handler)
            throws ProcessDrivenException;

    /**
     * 回退流程实例，使用场景：流程退回到指定环节
     * 
     * @param instId             流程实例ID
     * @param activeStepId       活动环节ID
     * @param lockId             流程实例锁ID
     * @param histroyStepId      指定退回历史操作ID
     * @param rollbackToUser     退回给指定用户 ,如果为空，则根据退回历史环节进行计算人员；
     * @param deliveryToThisStep 是否直送到此环节，true 直送到此环节，false 从退回环节开始逐个环节驱动
     * @param formParams         表单相关参数 ，引擎不处理该数据，仅转发
     * @param caller             调用者
     * @param handler            流程推进前后，目前主要用于驱动流程到子流程环节时，创建子流程已表单实例关系
     * 
     *                           <pre>
     *   
     * 使用场景：流程中有子流程,且下一环节时创建子流程时需要传入回调函数
     * 用来建立子流程 与表单数据关联关系
     * 
     * 
     * 使用平台表单样例
     * {@link com.hd.rcugrc.mvc.bpm.SubflowDataBinder}
     * 使用非平台表单样例
     * {@link com.hd.rcugrc.bpm.facade.service.impl.CustomSubflowDataBinder}
     * 如需要驱动主流程到子流程需要在xml中注入“subflowDataBinderCallback”并设置 scope="prototype"
     *                           </pre>
     * 
     * @param props              扩展属性，通过扩展属性来标记请求访问来源 {@code}key:source,value:mobile
     * @return boolean true 成功 false 失败
     * @throws ProcessDrivenException 流程驱动类相关异常信息
     */
    public boolean rejectTask(long instId, long activeStepId, long lockId, long histroyStepId, 
            User[] rollbackToUser, boolean deliveryToThisStep, FormParamInfo formParams, User caller, 
            FlowCallback handler, Map<String, String> props) throws ProcessDrivenException;
    
    /**
     * 回退流程实例，使用场景：流程退回到指定环节
     * 
     * @param instId             流程实例ID
     * @param activeStepId       活动环节ID
     * @param histroyStepId      指定退回历史操作ID
     * @param rollbackToUser     退回给指定用户 ,如果为空，则根据退回历史环节进行计算人员；
     * @param deliveryToThisStep 是否直送到此环节，true 直送到此环节，false 从退回环节开始逐个环节驱动
     * @param formParams         表单相关参数 ，引擎不处理该数据，仅转发
     * @param caller             调用者
     * @param handler            流程推进前后，目前主要用于驱动流程到子流程环节时，创建子流程已表单实例关系
     * 
     *                           <pre>
     *   
     * 使用场景：流程中有子流程,且下一环节时创建子流程时需要传入回调函数
     * 用来建立子流程 与表单数据关联关系
     * 
     * 
     * 使用平台表单样例
     * {@link com.hd.rcugrc.mvc.bpm.SubflowDataBinder}
     * 使用非平台表单样例
     * {@link com.hd.rcugrc.bpm.facade.service.impl.CustomSubflowDataBinder}
     * 如需要驱动主流程到子流程需要在xml中注入“subflowDataBinderCallback”并设置 scope="prototype"
     *                           </pre>
     * 
     * @param props              扩展属性，通过扩展属性来标记请求访问来源 {@code}key:source,value:mobile
     * @return boolean true 成功 false 失败
     * @throws ProcessDrivenException 流程驱动类相关异常信息
     */
    public boolean rejectTask(long instId, long activeStepId, long histroyStepId, User[] rollbackToUser,
            boolean deliveryToThisStep, FormParamInfo formParams, User caller, FlowCallback handler,
            Map<String, String> props) throws ProcessDrivenException;

    /**
     * 获取操作人列表信息
     * 
     * <pre>
     * 页面选择候选人入口，该方法仅是展示提交环节到目标环节相关信息
     * 目标环节下候选人节点信息需要获取
     * {@link ProcessDrivenService#getCandidates(String, long, String, Operator, Candidate, FormParamInfo)}
     * </pre>
     * 
     * @param flowId        流程定义信息
     * @param instId        流程实例 可以为0（创建并提交流程选人时为0）
     * @param actionId      操作ID
     * @param activeStepId  当前流程活动环节ID
     * @param caller        调用者
     * @param formParamInfo 平台仅需要设置inputs参数即可，第三方表单需要设置
     * 
     *                      <pre>
     *  表单ID formId;
     *  beanIdProperty ID属性名称 beanIdProperty;
     *  beanTitleProperty 标题属性名称  ;
     *  beanIdType  实体bean对象中作为主键ID类型   目前仅支持string 、 long  ;
     *  bean 实体   bean;
     *                      </pre>
     * 
     * @return CandidateInfo 推送到目标环节中相关信息 包含actionId 信息 目标环节ID
     * 
     * 
     *         <pre>
     *   （分支环节提交时，会出现多个目标环节操作者信息）
     *   
     *    ——分支节点（此节点进行提交）
     *     ——环节2操作 线（    xxxx.2）   环节2
     *     ——环节3操作线 （    xxxx.3）   环节3
     *   
     *   <p>构建分支节点提交选人样例</p>
     *    <code>
     *    ParticipantInfo[] parts = null;
     *    //该流程引擎中分支节点 操作线为split 此时 actionId="split"
     *    CandidateInfo[] candidateInfos = processDrivenService.getCandidateRoot(flowId,instId, actionId, 
     *              activeStepId, caller, ctx);
     *    //存放各个环节操作信息 此处测试仅存放当前根节点下的第一个节点
     *    Map&lt;String,ParticipantInfo&gt; splitPerson = new HashMap&lt;&gt;();
     *    //取操作线以及到目标环节信息
     *    for (CandidateInfo candidateInfo : candidateInfos) {
     *        List&lt;Operator&gt; operList = new ArrayList&lt;Operator&gt;();
     *        String toActionId = candidateInfo.getActionId();
     *        ParticipantInfo part = splitPerson.get(toActionId);
     *        if (part != null) {
     *            continue;
     *        }
     *        //查询指定环节下的候选者信息
     *        Candidate[] candidates = processDrivenService.getCandidates(flowId,instId, toActionId, null, null, ctx);
     *        if (candidates == null) {
     *           continue;
     *        }
     *        //获取候选者根节点下的第一个子节点
     *        Candidate rootNode = candidates[0];
     *        //operator 候选者信息 可能是组织 、角色、其他
     *        Operator operator = new OperatorBean(rootNode.getType(), rootNode.getCode(),rootNode.getName());
     *               
     *        //展开候选者operator下节点rootNode中子节点信息（获取的信息可能是组织也可能是人员）
     *        candidates = processDrivenService.getCandidates(flowId,instId, toActionId,operator, rootNode, ctx);
     *        if (candidates == null) {
     *            continue;
     *        }
     *        Candidate childNode =candidates[0];
     *        //获取候选者根节点下的第一个子节点为流程参与者 ;页面上做成选择器树展示时选择任意节点，这里暂以第一个节点作为测试节点
     *        operator = new OperatorBean(childNode.getType(), childNode.getCode(), childNode.getName());
     *        //构建流程参与者信息
     *        part = new ParticipantInfo();
     *        operList.add(operator);
     *        //设置目标环节ID
     *        part.setStepId(candidateInfo.getDestStepId());
     *        //设置目标环节参与者
     *        part.setParticipants(operList.toArray(new Operator[operList.size()]));
     *        splitPerson.put(toActionId, part);
     *    }
     *    //获取各个环节参与者信息（作为提交流程参与者参数）
     *    parts = splitPerson.values().toArray(new ParticipantInfo[splitPerson.size()]);
     * 
     *     </code>
     *         </pre>
     * 
     *         <pre>
     *   
     *   <p>（普通环节提交时，选择目标环节操作者信息）</p>
     *    <code>
     *      ParticipantInfo[] parts = null;
     *      //actionId为提交操作线为ID
     *      CandidateInfo[] candidateInfos = processDrivenService.getCandidateRoot(flowId,instId, actionId, 
     *          activeStepId, caller, ctx);
     *      List&lt;Operator&gt; operList = new ArrayList&lt;Operator&gt;();
     *      // 目标环节ID信息
     *      stepId = candidateInfos[0].getDestStepId();
     *      //获取参与者根节点信息
     *      Candidate[] candidates = processDrivenService.getCandidates(flowId,instId, actionId,null, null, ctx);
     *      if (candidates != null) {
     *            
     *          //根节点参与者信息 此时是实际参与者集合，如需要获取该参与者下子节点信息
     *          //需要再次调用方法 
     *          //candidates = processDrivenService.getCandidates(flowId,instId, actionId,operator, candidates[0], ctx);
     *          //来查询指定参与者 operator下指定节点andidates[0]下的其他子节点信息
     *          //但不允许子节点和父节点同时存在现象
     *          Operator operator = new OperatorBean(candidates[0].getType(), candidates[0].getCode(), 
     *              candidates[0].getName());
     *          operList.add(operator);
     *          ParticipantInfo participantInfo = new ParticipantInfo();
     *          //设置目标环节ID
     *          participantInfo.setStepId(stepId);
     *          //设置目标环节参与者
     *          participantInfo.setParticipants(operList.toArray(new Operator[operList.size()]));
     *          //获取目标环节参与者信息（作为提交流程参与者参数）
     *          parts = new ParticipantInfo[]{participantInfo}; 
     *      } else {
     *          //获取目标环节参与者信息（作为提交流程参与者参数）
     *          parts = new ParticipantInfo[0]; 
     *      }
     *    </code>
     *         </pre>
     * 
     *         <pre>
     *   
     *   <p>主流程结束环节无需选择提交人</p>
     *      判断是否是下一环节是否为结束 {@link ProcessDrivenService#isEndStep(FlowDefinition, String, String)}
     *         </pre>
     * 
     *         <pre>
     * <code>
     * <p>子流程最后一个环节向主流程推送</p>
     * 子流程最后一个环节向主流程推送时，返回的数据需要自行处理，提交选人目标环节 需要取主流程目标环节 这里取destStepId中的tjbxChild.4
     * 以下是子流程向主流程提交时CandidateInfo样例信息
     * actionId "tjchild.2/tjbxChild.3" 
     * actionName  "["子流程结束","环节2"]"
     * activeStepId    "["tjchild.2","tjbxChild.3"]" 
     * activeStepName  "["环节1","子流程1"]" 
     * destStepId  "["tjchild.4","tjbxChild.4"]" 
     * destStepName    "["结束1","环节2"]" 
     * 
     *  CandidateInfo[] candidateInfos = processDrivenService.getCandidateRoot(
     *      flowId, instId, actionId, activeStepId, caller, ctx);
     *  stepId = candidateInfos[0].getDestStepId();
     *  if (flowDefinition .getType().equals(FlowDefinition.SUB_FLOW )&amp;&amp; stepId.endsWith("]")) {
     *        List&lt;String&gt;data = JacksonUtils.toList(stepId, String.class);
     *        if (data.size() &gt; 1) {
     *            stepId = data.get(1);
     *        } else if (data.size() &gt; 0) {
     *            stepId = data.get(0);
     *        }
     *    }
     *    其他信息参考普通环节处理
     * </code>
     *         </pre>
     * 
     * @throws ProcessDrivenException 流程驱动类相关异常信息
     */
    public CandidateInfo[] getCandidateRoot(String flowId, long instId, String actionId, long activeStepId, User caller,
            FormParamInfo formParamInfo) throws ProcessDrivenException;

    /**
     * 撤销流程实例，对已提交的流程进行撤销
     * 
     * @param instId     流程实例
     * @param caller     调用者
     * @param formParams 表单相关参数 ，引擎不处理该数据，仅转发
     * 
     *                   <pre>
     * formParams参数中必须包含以下属性
     * formId 表单ID  包含协议前缀 如推荐：非平台表单  tj:testForm；平台表单  model:testForm 
     * 如果是非平台表单，则还需要包含
     *      bean 实体bean对象，该bean方式设置方法 有两种  
     *          方式一：调用此方法时直接通过formParams设置
     *          方式二：使用接口{@link FormHandlerService}实现类中方法 saveOrUpdateFormData中进行赋值
     * <b>如果bean 为空则将 formParams中的inputs 参数作为bean</b>
     *  beanTitleProperty 实体bean对象中作为标题字段属性
     *  beanIdProperty 实体bean对象中作为主键ID字段属性
     *  beanIdType 实体bean对象中作为主键ID类型  目前仅支持string 、 long
     *                   </pre>
     * 
     * @param handler    流程推进前后，目前主要用于驱动流程到子流程环节时，创建子流程已表单实例关系
     * 
     *                   <pre>
     *   
     * 使用场景：流程中有子流程,且下一环节时创建子流程时需要传入回调函数
     * 用来建立子流程 与表单数据关联关系
     * 使用平台表单样例
     * {@link com.hd.rcugrc.mvc.bpm.SubflowDataBinder}
     * 使用非平台表单样例
     * {@link com.hd.rcugrc.bpm.facade.service.impl.CustomSubflowDataBinder}
     * 如需要驱动主流程到子流程需要在xml中注入“subflowDataBinderCallback”并设置 scope="prototype"
     *                   </pre>
     * 
     * @param props      扩展属性，通过扩展属性来标记请求访问来源 {@code}key:source,value:mobile
     * @return boolean true 撤办成功，false 撤办失败 此方法目前撤销成功,不会生成待办，如果需要生成待办信息，需要单独执行
     * 
     *         com.hd.rcugrc.bpm.facade.service.impl.ListDataPopulatorServiceReader#handleTodoListCreate(FlowDefinition
     *         flowDefinition, FlowInstance flowInstance, ActiveStep activeStep,
     *         FormInfo formInfo, Object bean, User sender, Map&lt;String,
     *         Object&gt; context)
     * 
     * @throws ProcessDrivenException 撤销过程中会对当前流程枷锁，如果枷锁失败会抛出相关异常信息，流程撤销如果报错也会抛出相关异常信息
     */
    public boolean retractTask(long instId, User caller, FormParamInfo formParams, FlowCallback handler,
            Map<String, String> props) throws ProcessDrivenException;

    /**
     * 撤销流程实例，对已提交的流程进行撤销
     * 
     * @param instId     流程实例
     * @param caller     调用者
     * @param formParams 表单相关参数 ，引擎不处理该数据，仅转发
     * 
     *                   <pre>
     * formParams参数中必须包含以下属性
     * formId 表单ID  包含协议前缀 如推荐：非平台表单  tj:testForm；平台表单  model:testForm 
     * 如果是非平台表单，则还需要包含
     *      bean 实体bean对象，该bean方式设置方法 有两种  
     *          方式一：调用此方法时直接通过formParams设置
     *          方式二：使用接口{@link FormHandlerService}实现类中方法 saveOrUpdateFormData中进行赋值
     * <b>如果bean 为空则将 formParams中的inputs 参数作为bean</b>
     *  beanTitleProperty 实体bean对象中作为标题字段属性
     *  beanIdProperty 实体bean对象中作为主键ID字段属性
     *  beanIdType 实体bean对象中作为主键ID类型  目前仅支持string 、 long
     *                   </pre>
     * 
     * @param handler    流程推进前后，目前主要用于驱动流程到子流程环节时，创建子流程已表单实例关系
     * 
     *                   <pre>
     *   
     * 使用场景：流程中有子流程,且下一环节时创建子流程时需要传入回调函数
     * 用来建立子流程 与表单数据关联关系
     * 
     * 
     * 使用平台表单样例
     * {@link com.hd.rcugrc.mvc.bpm.SubflowDataBinder}
     * 使用非平台表单样例
     * {@link com.hd.rcugrc.bpm.facade.service.impl.CustomSubflowDataBinder}
     * 如需要驱动主流程到子流程需要在xml中注入“subflowDataBinderCallback”并设置 scope="prototype"
     *                   </pre>
     * 
     * @return boolean true 撤办成功，false 撤办失败 此方法目前撤销成功,不会生成待办，如果需要生成待办信息，需要单独执行
     * 
     *         com.hd.rcugrc.bpm.facade.service.impl.ListDataPopulatorServiceReader#handleTodoListCreate(FlowDefinition
     *         flowDefinition, FlowInstance flowInstance, ActiveStep activeStep,
     *         FormInfo formInfo, Object bean, User sender, Map&lt;String,
     *         Object&gt; context)
     * 
     * @throws ProcessDrivenException 撤销过程中会对当前流程枷锁，如果枷锁失败会抛出相关异常信息，流程撤销如果报错也会抛出相关异常信息
     */
    public boolean retractTask(long instId, User caller, FormParamInfo formParams, FlowCallback handler)
            throws ProcessDrivenException;

    /**
     * 获取指定节点下参与者列表信息
     * 
     * @param flowId        流程定义ID
     * @param instId        流程实例ID 可以为0（创建并提交流程选人时为0）
     * @param actionId      操作ID
     * @param operator      待展开操作者信息，候选人根节点下子孙节点信息构成的操作者信息 , 获取候选参与者根节点时此参数为空
     * @param node          待展开候选人节点 ,获取候选参与者根节点时此参数为空
     * @param formParamInfo 平台仅需要设置inputs参数即可，第三方表单需要设置
     * 
     *                      <pre>
     *  表单ID formId;
     *  beanIdProperty ID属性名称 beanIdProperty;
     *  beanTitleProperty 标题属性名称  ;
     *  beanIdType  实体bean对象中作为主键ID类型   目前仅支持string 、 long  ;
     *  bean 实体   bean;
     *                      </pre>
     * 
     * @return 候选参与者列表
     * @throws Exception 流程驱动类相关异常信息
     * 
     *                   <pre>
     * 方法说明：
     * 1.获取候选人根节点信息时，参数operator和node可以为空
     * 2.展开根节点信息时参数operator和node不能为空，如果根节点为组织或者角色时，参数operator和node内容保持一致即可
     * 使用样例见方法{@link ProcessDrivenService#getCandidateRoot(String, long, String, long, User, FormParamInfo)}
     *                   </pre>
     */
    public Candidate[] getCandidates(String flowId, long instId, String actionId, Operator operator, Candidate node,
            FormParamInfo formParamInfo) throws Exception;
    
    /**
     * 获取指定actionId下参与者-解析的所有用户列表信息
     * 
     * @param flowId        流程定义ID
     * @param instId        流程实例ID 可以为0（创建并提交流程选人时为0）
     * @param actionId      操作ID
     * @param operator      待展开操作者信息，候选人根节点下子孙节点信息构成的操作者信息 , 获取候选参与者根节点时此参数为空
     * @param node          待展开候选人节点 ,获取候选参与者根节点时此参数为空
     * @param formParamInfo 平台仅需要设置inputs参数即可，第三方表单需要设置
     * @return
     * @throws Exception
     */
    public Candidate[] getCandidatesRootUsers(String flowId, long instId, String actionId, FormParamInfo formParamInfo)
            throws Exception;
    
    /**
     * 判断operator 是否能解析出用户
     * @param operator
     * @return
     */
    public boolean hasCandidateUsers(String flowId, long instId, String actionId, FormParamInfo formParamInfo,
            Operator oper) throws Exception;
    
    /**
     * 
     * @param formParamInfo
     * @param expressions
     * @param startPosition
     * @param maxResults
     * @param operator
     * @param context
     * @param searchVal
     * @return
     */
    public Candidate[] searchCandidates(Expression[] expressions, String searchVal, String flowId, long instId,
            long activeStepId, FormParamInfo formParamInfo, String actionId, int startPosition, int maxResults,
            Operator operator) throws Exception;

    /**
     * <pre>
     * 创建流程实例、表单、表单数据关联关系，
     * 该方法仅需要在第一次创建创建流程或者创建流程并推动流程后调用
     * </pre>
     * 
     * @param inst     流程实例
     * @param bean     表单数据bean
     * @param formInfo 表单信息{@link com.hd.rcugrc.bpm.facade.util.FormUtil#genFormInfo}
     * @param context  上下文信息 页面表单相关信息
     * @return FlowFormRelation 流程与表单关系
     */
    public FlowFormRelation createFlowFormRelation(FlowInstance inst, Object bean, FormInfo formInfo,
            Map<String, Object> context);

    /**
     * <pre>
     * 创建子流程时会调用此方法
     * 更新流程实例、表单、表单数据关联关系，
     * 该方法仅需要在第一次创建创建流程或者创建流程并推动流程后调用
     * 使用场景：
     * 创建子流程时会调用此方法
     * 推动流程时创建子流程够会调用此方法{@link ProcessDrivenService#dispatchTask}后
     * </pre>
     * 
     * @param inst     流程实例
     * @param bean     表单数据bean
     * @param formInfo 表单信息{@link com.hd.rcugrc.bpm.facade.util.FormUtil#genFormInfo}
     * @param context  上下文信息 页面表单相关信息
     * @return FlowFormRelation 流程与表单关系
     */
    public FlowFormRelation updateFlowFormRelation(FlowInstance inst, Object bean, FormInfo formInfo,
            Map<String, Object> context);

    /**
     * 环节是否是结束环节 用來计算最后一个环节是否需要选择候选参与者
     * 
     * @param flow     流程定义信息 可以为空
     *                 <p>
     *                 如果不为空则根据此对象和actionId进行查询，如果为空则根据flowId和actionId查询
     *                 </p>
     * @param flowId   流程定义ID
     * @param actionId 操作ID
     * @return true 是结束环节 false 不是结束环节
     */
    public boolean isEndStep(FlowDefinition flow, String flowId, String actionId);

    /**
     * 根据流程实例ID 获取当前活动环节
     * <p>
     * 使用场景: 分支环节 提交后 会出现多个并行活动环节，提交流程时需要逐个获取活动环节配合流程实例ID 进行提交
     * </p>
     * 
     * @param instId 流程实例ID
     * @return ActiveStep 当前活动环节
     */
    public ActiveStep[] getActiveStepsByInstId(long instId);

    /**
     * 根据流程实例ID 获取当前退回环节列表 请使用 {@link #getRejectSteps(long, long)} 根据流程实例ID 当前环节id
     * 获取当前退回环节列表
     * 
     * @param instId 流程实例ID
     * @return RollbackStep 可退回环节
     * @deprecated 由{@link ProcessDrivenService#getRejectSteps(long, long)}替代
     */
    @Deprecated
    public RollbackStep[] getRejectSteps(long instId);

    /**
     * 根据流程实例ID 流程实例ID 获取当前退回环节列表
     * 
     * @param instId       流程实例ID
     * @param activeStepId 当前流程活动环节id
     * @return RollbackStep 可退回环节
     */
    public RollbackStep[] getRejectSteps(long instId, long activeStepId);

    /**
     * 根据流程实例Id获取流程实例与表单、表单实体Bean关系
     * 
     * @param instId 流程实例Id
     * @return FlowFormBeanRelation[] 流程实例与表单、表单实体Bean关系
     */
    public FlowFormBeanRelation[] getFlowFormBeanRelations(long instId);

    /**
     * 申请锁 ，主要用于乐观锁
     * 
     * @param instId 流程实例Id
     * @param caller 申请者
     * @return 流程锁Id
     * @throws ProcessDrivenException 流程实例不存在、加锁失败后系统会报错
     */
    public long lockTask(long instId, User caller) throws ProcessDrivenException;

    /**
     * 申请并刷新锁 ，主要用于乐观锁
     * 
     * @param instId 流程实例Id
     * @param lockId 流程实例锁ID ，如果不为0时进行锁刷新，如果为0 则申请锁
     * @param caller 申请者
     * @return 流程锁信息
     * @throws ProcessDrivenException 流程实例不存在、调用者为空，申请或刷新失败后系统会报错
     */
    public LockInfo lockAndRefreshTask(long instId, long lockId, User caller) throws ProcessDrivenException;

    /**
     * 流程实例调用者对流程实例锁进行解锁
     * 
     * @param instId 流程实例Id
     * @param lockId 流程实例锁ID
     * @param caller 申请者
     * @return true 解锁成功 false 解锁失败
     * @throws ProcessDrivenException 流程实例不存在、调用者为空、调用者与锁着不一致 均会报错
     */
    public boolean unlockTask(long instId, long lockId, User caller) throws ProcessDrivenException;

    /**
     * 流程实例调用者对流程实例锁进行（匿名）解锁
     *
     * @param instId 流程实例Id
     * @param lockId 流程实例锁ID
     * @param caller 申请者
     * @return true 解锁成功 false 解锁失败
     * @throws ProcessDrivenException 流程实例不存在、调用者为空、调用者与锁着不一致 均会报错
     */
    public boolean unlockTaskAnony(long instId, long lockId, User caller) throws ProcessDrivenException;

    /**
     * 查看锁信息 ，主要用于查看流程实例被锁状态信息
     * 
     * @param instId 流程实例Id
     * @return 流程锁信息
     */
    public LockInfo getLockInfo(long instId);

    /**
     * 获取流程属性，及相关业务单的扩展属性， 如果instId为0 或inst所关联的业务单位默认，则使用默认业务单的扩展属性
     * 
     * @param flowId
     * @param actionId
     * @param instId
     * @return
     */
    public Map<String, Object> getFlowProps(String flowId, String actionId, long instId);

    /**
     * 根据流程实例ID 获取流程实例中的时间控制定义信息
     * 
     * @param instId 流程实例ID
     * @return
     */
    public TimeConstraintDefinition getTimerByFlowInstance(long instId);
    
    /**
     * 根据流程提交信息id获取对应的信息
     * 
     * @param id    流程提交信息id
     * @return  流程提交信息
     */
    public WorkflowSubmitInfo getSubmitInfo(long id);
    
    /**
     * 是否提交前先保存
     * @return
     */
    public boolean isSaveBeforeFirstSubmit();

}
