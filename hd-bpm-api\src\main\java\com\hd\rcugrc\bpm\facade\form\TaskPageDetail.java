/*
 * Copyright 2013-2019 Smartdot Technologies Co., Ltd. All rights reserved.
 * SMARTDOT PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 */
package com.hd.rcugrc.bpm.facade.form;

import java.io.Serializable;
import java.util.Map;

import com.hd.rcugrc.bpm.facade.ProcessActiveLogInfo;
import com.hd.rcugrc.bpm.facade.TimeConstraintDefinition;
import com.hd.rcugrc.bpm.facade.form.util.ProcessFormFieldUtil;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <p>
 * 流程实例活动环节页面详情信息
 * 
 * <AUTHOR> href="mailto:<EMAIL>">songjj</a>
 * @version 5.4, 2019年7月12日
 */
@Schema(title = "流程实例活动环节页面详情信息", description = "流程实例活动环节页面详情信息")
public class TaskPageDetail implements Serializable {


    private static final long serialVersionUID = -3787621892800787372L;

    private long instId;

    private long activeStepId;

    private String mode;

    private String processId;

    private long flowFormModelRelationId;

    private ProcessFormData processFormData;

    private long lockId;

    private TaskButton[] buttons;

    private TaskButton[] saveButtons;

    private TaskButton[] pushButtons;

    private ProcessActiveLogInfo[] processActiveLogInfo;

    private Map<String, Object> attrs;

    private boolean optimistic;

    private String pageTitle;
    
    private TimeConstraintDefinition timer;
    
    /**
     * 返回流程实例id
     * 
     * @return 流程实例id
     */

    @Schema(title = "当前流程实例ID", description = "当前流程实例ID", example = "100")
    public long getInstId() {
        return instId;
    }

    /**
     * 设置流程实例ID
     * 
     * @param instId 流程实例ID
     */
    public void setInstId(long instId) {
        this.instId = instId;
    }

    /**
     * 返回流程操作ID
     * 
     * @return 流程操作ID
     */
    @Schema(title = "流程实例活动Id，即当前流程实例所处的活动环节ID", description = "流程实例活动Id，即当前流程实例所处的活动环节ID", example = "100")
    public long getActiveStepId() {
        return activeStepId;
    }

    /**
     * 设置流程实例活动ID
     * 
     * @param activeStepId 流程实例活动ID
     */
    public void setActiveStepId(long activeStepId) {
        this.activeStepId = activeStepId;
    }

    /**
     * 获取模式
     * 
     * <pre>
     * 编辑 {@link ProcessFormFieldUtil#FORM_MODEL_EDIT} 返回表单字段以及值外，还返回字段附加信息 如下拉框信息
     * 只读 {@link ProcessFormFieldUtil#FORM_MODEL_VIEW} 返回表单字段以及值 并且为只读模式
     * 送阅 {@link ProcessFormFieldUtil#FORM_MODEL_VIEWTOREAD} 返回表单字段以及值 并且为只读模式,部分送阅字段可编辑
     * 沟通 {@link ProcessFormFieldUtil#FORM_MODEL_VIEWCOMM}  返回表单字段以及值 并且为只读模式,部分沟通字段可编辑
     * 申请 {@link ProcessFormFieldUtil#FORM_MODEL_VIEWAPP} 返回表单字段以及值 并且为只读模式,部分申请字段可编辑
     * </pre>
     * 
     * @return 模式
     */
    @Schema(title = "详细页面模式，模式有编辑：EDIT、查看：VIEW、送阅：VIEWTOREAD、沟通：VIEWCOMM、申请：VIEWAPP，" 
            + "其中EDIT模式是允许编辑表单字段的（除PC端流程环节配置不允许编辑），非EDIT模式是不允许编辑表单字段值（除VIEWTOREAD、VIEWCOMM模式下的意见字段外）",
            description = "详细页面模式，模式有编辑：EDIT、查看：VIEW、送阅：VIEWTOREAD、沟通：VIEWCOMM、申请：VIEWAPP，"
                    + "其中EDIT模式是允许编辑表单字段的（除PC端流程环节配置不允许编辑），非EDIT模式是不允许编辑表单字段值（除VIEWTOREAD、VIEWCOMM模式下的意见字段外）", example = "EDIT")
    public String getMode() {
        return mode;
    }

    /**
     * @param mode 设置模式
     * 
     *             <pre>
     * 编辑 {@link ProcessFormFieldUtil#FORM_MODEL_EDIT} 返回表单字段以及值外，还返回字段附加信息 如下拉框信息
     * 只读 {@link ProcessFormFieldUtil#FORM_MODEL_VIEW} 返回表单字段以及值 并且为只读模式
     * 送阅 {@link ProcessFormFieldUtil#FORM_MODEL_VIEWTOREAD} 返回表单字段以及值 并且为只读模式,部分送阅字段可编辑
     * 沟通 {@link ProcessFormFieldUtil#FORM_MODEL_VIEWCOMM}  返回表单字段以及值 并且为只读模式,部分沟通字段可编辑
     * 申请 {@link ProcessFormFieldUtil#FORM_MODEL_VIEWAPP} 返回表单字段以及值 并且为只读模式,部分申请字段可编辑
     *             </pre>
     */
    public void setMode(String mode) {
        this.mode = mode;
    }

    /**
     * @return 流程页面表单数据
     */
    @Schema(title = "流程页面表单数据", description = "流程页面表单数据")
    public ProcessFormData getProcessFormData() {
        return processFormData;
    }

    /**
     * 设置流程页面表单数据
     * 
     * @param processFormData 流程页面表单数据
     */
    public void setProcessFormData(ProcessFormData processFormData) {
        this.processFormData = processFormData;
    }

    /**
     * @return 锁流程ID ，悲观锁时，返回非0，乐观锁 返回0
     */
    public long getLockId() {
        return lockId;
    }

    /**
     * @param lockId 锁流程ID ，悲观锁时，返回非0，乐观锁 返回0
     */
    @Schema(title = "锁流程ID ，悲观锁时，返回非0，乐观锁 返回0", description = "锁流程ID ，悲观锁时，返回非0，乐观锁 返回0")
    public void setLockId(long lockId) {
        this.lockId = lockId;
    }

    /**
     * @return 任务按钮列表
     */
    @Schema(title = "任务按钮列表，提交、保存、沟通、转办、撤办等按钮", description = "任务按钮列表，提交、保存、沟通、转办、撤办等按钮")
    public TaskButton[] getButtons() {
        return buttons;
    }

    /**
     * @param buttons 任务按钮列表
     */

    public void setButtons(TaskButton[] buttons) {
        this.buttons = buttons;
    }

    /**
     * @return 保存按钮
     */
    @Schema(title = "保存按钮列表，只有mode为EDIT模式时，才有值", description = "保存按钮列表，只有mode为EDIT模式时，才有值")
    public TaskButton[] getSaveButtons() {
        return saveButtons;
    }

    /**
     * 设置保存按钮
     * 
     * @param saveButtons 保存按钮
     */
    public void setSaveButtons(TaskButton[] saveButtons) {
        this.saveButtons = saveButtons;
    }

    /**
     * @return 提交按钮
     */
    @Schema(title = "提交按钮列表，只有mode为EDIT模式时，才有值，需要调用选人策略接口", description = "提交按钮列表，只有mode为EDIT模式时，才有值，需要调用选人策略接口")
    public TaskButton[] getPushButtons() {
        return pushButtons;
    }

    /**
     * 设置 提交按钮
     * 
     * @param pushButtons 提交按钮
     */
    public void setPushButtons(TaskButton[] pushButtons) {
        this.pushButtons = pushButtons;
    }

    /**
     * @return 流程定义ID
     */
    @Schema(title = "流程定义ID", description = "流程定义ID")
    public String getProcessId() {
        return processId;
    }

    /**
     * @param processId 流程定义ID
     */
    public void setProcessId(String processId) {
        this.processId = processId;
    }

    /**
     * 返回当前流程实例正在活动的环节列表信息
     * 
     * @return 当前流程实例正在活动的环节列表信息
     */
    @Schema(title = "当前流程实例正在活动的环节列表信息", description = "当前流程实例正在活动的环节列表信息")
    public ProcessActiveLogInfo[] getProcessActiveLogInfo() {
        return processActiveLogInfo;
    }

    /**
     * 设置当前流程实例正在活动的环节列表信息
     * 
     * @param processActiveLogInfo 设置当前流程实例正在活动的环节列表信息
     */
    public void setProcessActiveLogInfo(ProcessActiveLogInfo[] processActiveLogInfo) {
        this.processActiveLogInfo = processActiveLogInfo;
    }

    /**
     * @return 扩展属性
     */
    public Map<String, Object> getAttrs() {
        return attrs;
    }

    /**
     * 设置扩展属性
     * 
     * @param attrs 扩展属性
     */
    public void setAttrs(Map<String, Object> attrs) {
        this.attrs = attrs;
    }

    /**
     * @return 当前环节是否是乐观锁 ,true 表示乐观锁、false 表示是悲观锁,此时打开表单要申请锁
     */
    @Schema(title = "当前环节是否是乐观锁 ,true 表示乐观锁;false 表示是悲观锁,此时打开表单要申请锁",
            description = "当前环节是否是乐观锁 ,true 表示乐观锁;false 表示是悲观锁,此时打开表单要申请锁")
    public boolean isOptimistic() {
        return optimistic;
    }

    /**
     * @param optimistic 当前环节是否是乐观锁 true 表示乐观锁、false 表示是悲观锁
     */
    public void setOptimistic(boolean optimistic) {
        this.optimistic = optimistic;
    }

    /**
     * 返回流程绑定页面标题
     * 
     * @return 流程绑定页面标题
     */
    @Schema(title = "流程绑定页面标题", description = "流程绑定页面标题")
    public String getPageTitle() {
        return pageTitle;
    }

    /**
     * 设置流程绑定页面标题
     * 
     * @param pageTitle 流程绑定页面标题
     */
    public void setPageTitle(String pageTitle) {
        this.pageTitle = pageTitle;
    }

    /**
     * @return the flowFormModelRelationId
     */
    @Schema(title = "流程 页面 表单 关联关系id", description = "流程 页面 表单 关联关系id")
    public long getFlowFormModelRelationId() {
        return flowFormModelRelationId;
    }

    /**
     * 设置流程 页面 表单 关联关系id
     * 
     * @param flowFormModelRelationId the flowFormModelRelationId to set
     */
    public void setFlowFormModelRelationId(long flowFormModelRelationId) {
        this.flowFormModelRelationId = flowFormModelRelationId;
    }

    /**
     * 返回流程实例中设置的时间控制配置信息
     * @return
     */
    @Schema(title = "时间控制配置定义信息", description = "时间控制配置定义信息")
    public TimeConstraintDefinition getTimer() {
        return timer;
    }

    /**
     * 设置流程实例中的时间控制配置信息
     * @param timer
     */
    public void setTimer(TimeConstraintDefinition timer) {
        this.timer = timer;
    }
}
