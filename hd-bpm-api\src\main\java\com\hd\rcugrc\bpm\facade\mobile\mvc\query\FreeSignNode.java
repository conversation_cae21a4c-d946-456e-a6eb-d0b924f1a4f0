/*
 * Copyright 2013-2019 Smartdot Technologies Co., Ltd. All rights reserved.
 * SMARTDOT PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 */
package com.hd.rcugrc.bpm.facade.mobile.mvc.query;

import java.io.Serializable;
import java.util.List;

import org.joda.time.DateTime;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <p>
 * 自由流类型节点拓扑图节点，主要用于Restful接口
 *
 * <AUTHOR> href="mailto:<EMAIL>">zhaolk</a>
 * @version 5.5.1, 2021年3月26日
 */
@Schema(title = "FreeSignNode", description = "自由流类型节点拓扑图节点")
public class FreeSignNode implements Serializable {
    
    private static final long serialVersionUID = -8994087188061261187L;
    
    private int sn;
    
    private String code;
    
    private String name;
    
    private DateTime time;
    
    private List<FreeSignNode> child;
    
    @Schema(title = "参与者code", description = "参与者code", required = true, example = "zhao")
    public String getCode() {
        return code;
    }
    
    public void setCode(String code) {
        this.code = code;
    }
    
    @Schema(title = "参与者名称", description = "参与者名称", required = true, example = "赵")
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    @Schema(title = "被添加时间", description = "被添加时间", required = true, example = "1111111")
    public DateTime getTime() {
        return time;
    }
    
    public void setTime(DateTime time) {
        this.time = time;
    }
    
    @Schema(title = "添加的参与者", description = "添加的参与者", example = "{}")
    public List<FreeSignNode> getChild() {
        return child;
    }
    
    public void setChild(List<FreeSignNode> child) {
        this.child = child;
    }
    
    
    @Schema(title = "节点SN", description = "节点SN", required = true, example = "1")
    public int getSn() {
        return sn;
    }
    
    public void setSn(int sn) {
        this.sn = sn;
    }
    
    
}
