/*
 * Copyright 2013-2019 Smartdot Technologies Co., Ltd. All rights reserved.
 * SMARTDOT PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 */
package com.hd.rcugrc.bpm.facade.flow;

import java.util.Map;

import com.hd.rcugrc.bpm.User;
import com.hd.rcugrc.bpm.facade.ApplyTaskInfo;
import com.hd.rcugrc.bpm.facade.AttentionTaskInfo;
import com.hd.rcugrc.bpm.facade.CommunicateTaskInfo;
import com.hd.rcugrc.bpm.facade.CompletedTaskInfo;
import com.hd.rcugrc.bpm.facade.DraftTaskInfo;
import com.hd.rcugrc.bpm.facade.InformTaskInfo;
import com.hd.rcugrc.bpm.facade.ProcessedTaskInfo;
import com.hd.rcugrc.bpm.facade.ProcessingTaskInfo;
import com.hd.rcugrc.bpm.facade.RemindTaskInfo;
import com.hd.rcugrc.bpm.facade.ReminderTaskInfo;
import com.hd.rcugrc.bpm.facade.ReplyCommunicateTaskInfo;
import com.hd.rcugrc.bpm.facade.ReplyInformTaskInfo;
import com.hd.rcugrc.bpm.facade.SenderCommunicateTaskInfo;
import com.hd.rcugrc.bpm.facade.SenderInformTaskInfo;
import com.hd.rcugrc.bpm.facade.TodoTaskInfo;
import com.hd.rcugrc.data.crud.criteria.Criterion;
import com.hd.rcugrc.data.crud.criteria.OrderBy;
import com.hd.rcugrc.web.dwr.criterion.Expression;

/**
 * <p>
 * 任务列表查询, 用于查询待办, 已办, 已完成, 我的申请任务列表
 * 
 * <AUTHOR> href="mailto:<EMAIL>">pandy</a>
 * @version 1.0, 2019年3月7日
 */
public interface UserTaskListService {

    /**
     * 获取满足约束条件的待办任务个数
     * 
     * @param ownerAccount 所属用户id
     * @param criteria     前端查询条件
     * @return 待办任务数
     */
    public int getTodoTaskCount(String ownerAccount, Criterion[] criteria);

    /**
     * 获取满足约束条件的待办任务个数 包含ownerAccount账号的数量和trustors账号的待办数据
     * 
     * @param ownerAccount 所属用户id
     * @param trustors     所属委托人
     * @param criteria     前端查询条件
     * @return 待办任务数
     */
    public int getTodoTaskCount(String ownerAccount, String[] trustors, Criterion[] criteria);

    /**
     * 获取待办任务列表
     * 
     * @param ownerAccount    所属用户id
     * @param createDeptLevel 返回用户的所属机构级别的名称，传空则将CreatorDeptName属性赋值为用户的直接上级
     * @param criteria        前端查询条件
     * @param startPosition   返回记录前跳过的记录条数
     * @param maxResults      返回的结果记录条数，如果传入-1则返回所有的查询结果
     * @param orderBy         查询结果的排序设置，格式为\"字段
     *                        排序设置\"，其中排序设置的值为ASC或DESC，如果涉及多个字段，则多段定义间以，分隔
     * @return 待办任务数组
     */
    public TodoTaskInfo[] getTodoTasks(String ownerAccount, String createDeptLevel, Criterion[] criteria,
            int startPosition, int maxResults, String orderBy);

    /**
     * 获取待办任务列表 包含ownerAccount账号的数量和trustors账号的数据
     * 
     * @param ownerAccount    所属用户id
     * @param trustors        所属委托人
     * @param createDeptLevel 返回用户的所属机构级别的名称，传空则将CreatorDeptName属性赋值为用户的直接上级
     * @param criteria        前端查询条件
     * @param startPosition   返回记录前跳过的记录条数
     * @param maxResults      返回的结果记录条数，如果传入-1则返回所有的查询结果
     * @param orderBy         查询结果的排序设置，格式为\"字段
     *                        排序设置\"，其中排序设置的值为ASC或DESC，如果涉及多个字段，则多段定义间以，分隔
     * @return 待办任务数组
     */
    public TodoTaskInfo[] getTodoTasks(String ownerAccount, String[] trustors, String createDeptLevel,
            Criterion[] criterions, int startPosition, int maxResults, String orderBy);

    /**
     * 设置待办任务是否置顶
     * 
     * @param todoId 待办任务id
     * @param isTop  是否置顶， 0非置顶，1置顶
     * @return 是否成功
     */
    public boolean setTodoTop(long todoId, int isTop);

    /**
     * 获取满足约束条件的待处理任务个数
     * 
     * @param ownerAccount 所属用户id
     * @param criteria     前端查询条件
     * @return 已办任务数
     */
    public int getProcessingTaskCount(String ownerAccount, Criterion[] criteria);

    /**
     * 获取满足约束条件的待处理任务个数 包含ownerAccount账号的数量和trustors账号的数据
     * 
     * @param ownerAccount 所属用户id
     * @param trustors     所属委托人
     * @param criteria     前端查询条件
     * @return 已办任务数
     */
    public int getProcessingTaskCount(String ownerAccount, String[] trustors, Criterion[] criteria);

    /**
     * 获取待处理任务列表
     * 
     * @param ownerAccount  所属用户id
     * @param criteria      前端查询条件
     * @param startPosition 返回记录前跳过的记录条数
     * @param maxResults    返回的结果记录条数，如果传入-1则返回所有的查询结果
     * @param orderBy       查询结果的排序设置，格式为\"字段
     *                      排序设置\"，其中排序设置的值为ASC或DESC，如果涉及多个字段，则多段定义间以，分隔
     * @return 已办任务数组
     */
    public ProcessingTaskInfo[] getProcessingTasks(String ownerAccount, Criterion[] criteria, int startPosition,
            int maxResults, String orderBy);

    /**
     * 获取待处理任务列表
     * 
     * @param ownerAccount  所属用户id
     * @param trustors      所属委托人
     * @param criteria      前端查询条件
     * @param startPosition 返回记录前跳过的记录条数
     * @param maxResults    返回的结果记录条数，如果传入-1则返回所有的查询结果
     * @param orderBy       查询结果的排序设置，格式为\"字段
     *                      排序设置\"，其中排序设置的值为ASC或DESC，如果涉及多个字段，则多段定义间以，分隔
     * @return 已办任务数组
     */
    public ProcessingTaskInfo[] getProcessingTasks(String ownerAccount, String[] trustors, Criterion[] criteria,
            int startPosition, int maxResults, String orderBy);

    /**
     * 获取满足约束条件的已办任务个数
     * 
     * @param ownerAccount 所属用户id
     * @param criteria     前端查询条件
     * @return 已办任务数
     */
    public int getProcessedTaskCount(String ownerAccount, Criterion[] criteria);

    /**
     * 获取满足约束条件的已办任务个数
     * 
     * @param ownerAccount 所属用户id
     * @param criteria     前端查询条件
     * @return 已办任务数
     */
    public int getProcessedTaskCount(String ownerAccount, String[] trustors, Criterion[] criteria);

    /**
     * 获取满足约束条件的承接已办任务个数
     * 
     * @param expression 前端查询条件
     */
    public int getHandoverProcessedTaskCount(Expression[] expressions);

    /**
     * 
     * @param ownerAccount
     * @param expressions
     * @return
     */
    public int getHandoverProcessedTaskCount(String ownerAccount, Expression[] expressions);

    /**
     * 
     * @param ownerAccount
     * @param trustors
     * @param expressions
     * @return
     */
    public int getHandoverProcessedTaskCount(String ownerAccount, String[] trustors, Expression[] expressions);

    /**
     * 获取承接已办任务列表
     * 
     * @param expression    前端查询条件
     * @param startPosition 返回记录前跳过的记录条数
     * @param maxResults    返回的结果记录条数，如果传入-1则返回所有的查询结果
     * @param orderBy       查询结果的排序设置
     * @return 承接已办任务数组
     */
    public ProcessedTaskInfo[] getHandoverProcessedTasks(Expression[] expressions, int startPosition, int maxResults,
            String orderBy);

    /**
     * 获取承接已办任务列表
     * 
     * @param expression    前端查询条件
     * @param startPosition 返回记录前跳过的记录条数
     * @param maxResults    返回的结果记录条数，如果传入-1则返回所有的查询结果
     * @param orderBy       查询结果的排序设置
     * @return 承接已办任务数组
     */
    public ProcessedTaskInfo[] getHandoverProcessedTasks(String ownerAccount, Expression[] expressions,
            int startPosition, int maxResults, String orderBy);

    /**
     * 获取承接已办任务列表
     * 
     * @param expression    前端查询条件
     * @param startPosition 返回记录前跳过的记录条数
     * @param maxResults    返回的结果记录条数，如果传入-1则返回所有的查询结果
     * @param orderBy       查询结果的排序设置
     * @return 承接已办任务数组
     */
    public ProcessedTaskInfo[] getHandoverProcessedTasks(String ownerAccount, String[] trustors,
            Expression[] expressions, int startPosition, int maxResults, String orderBy);

    /**
     * 获取已办任务列表
     * 
     * @param ownerAccount  所属用户id
     * @param criteria      前端查询条件
     * @param startPosition 返回记录前跳过的记录条数
     * @param maxResults    返回的结果记录条数，如果传入-1则返回所有的查询结果
     * @param orderBy       查询结果的排序设置，格式为\"字段
     *                      排序设置\"，其中排序设置的值为ASC或DESC，如果涉及多个字段，则多段定义间以，分隔
     * @return 已办任务数组
     */
    public ProcessedTaskInfo[] getProcessedTasks(String ownerAccount, Criterion[] criteria, int startPosition,
            int maxResults, String orderBy);

    /**
     * 获取已办任务列表
     * 
     * @param ownerAccount  所属用户id
     * @param criteria      前端查询条件
     * @param startPosition 返回记录前跳过的记录条数
     * @param maxResults    返回的结果记录条数，如果传入-1则返回所有的查询结果
     * @param orderBy       查询结果的排序设置，格式为\"字段
     *                      排序设置\"，其中排序设置的值为ASC或DESC，如果涉及多个字段，则多段定义间以，分隔
     * @return 已办任务数组
     */
    public ProcessedTaskInfo[] getProcessedTasks(String ownerAccount, String[] trustors, Criterion[] criteria,
            int startPosition, int maxResults, String orderBy);

    /**
     * 获取满足约束条件的已完成任务个数
     * 
     * @param ownerAccount 所属用户id
     * @param criteria     前端查询条件
     * @return 已完成任务数
     */
    public int getCompletedTaskCount(String ownerAccount, Criterion[] criteria);

    /**
     * 获取满足约束条件的已完成任务个数
     * 
     * @param ownerAccount 所属用户id
     * @param criteria     前端查询条件
     * @return 已完成任务数
     */
    public int getCompletedTaskCount(String ownerAccount, String[] trustors, Criterion[] criteria);

    /**
     * 获取已完成的任务列表
     * 
     * @param ownerAccount  所属用户id
     * @param criteria      前端查询条件
     * @param startPosition 返回记录前跳过的记录条数
     * @param maxResults    返回的结果记录条数，如果传入-1则返回所有的查询结果
     * @param orderBy       查询结果的排序设置，格式为\"字段
     *                      排序设置\"，其中排序设置的值为ASC或DESC，如果涉及多个字段，则多段定义间以，分隔
     * @return 已完成任务数组
     */

    public CompletedTaskInfo[] getCompletedTasks(String ownerAccount, Criterion[] criteria, int startPosition,
            int maxResults, String orderBy);

    /**
     * 获取已完成的任务列表
     * 
     * @param ownerAccount  所属用户id
     * @param criteria      前端查询条件
     * @param startPosition 返回记录前跳过的记录条数
     * @param maxResults    返回的结果记录条数，如果传入-1则返回所有的查询结果
     * @param orderBy       查询结果的排序设置，格式为\"字段
     *                      排序设置\"，其中排序设置的值为ASC或DESC，如果涉及多个字段，则多段定义间以，分隔
     * @return 已完成任务数组
     */

    public CompletedTaskInfo[] getCompletedTasks(String ownerAccount, String[] trustors, Criterion[] criteria,
            int startPosition, int maxResults, String orderBy);

    /**
     * 获取满足约束条件的我的申请任务个数
     * 
     * @param ownerAccount 所属用户id
     * @param criteria     前端查询条件
     * @return 我的申请任务数
     */
    public int getApplyTaskCount(String ownerAccount, Criterion[] criteria);

    /**
     * 获取满足约束条件的我的申请任务个数
     * 
     * @param ownerAccount 所属用户id
     * @param criteria     前端查询条件
     * @return 我的申请任务数
     */
    public int getApplyTaskCount(String ownerAccount, String[] trustors, Criterion[] criteria);

    /**
     * 获取我的申请任务列表
     * 
     * @param ownerAccount  所属用户id
     * @param criteria      前端查询条件
     * @param startPosition 返回记录前跳过的记录条数
     * @param maxResults    返回的结果记录条数，如果传入-1则返回所有的查询结果
     * @param orderBy       查询结果的排序设置，格式为\"字段
     *                      排序设置\"，其中排序设置的值为ASC或DESC，如果涉及多个字段，则多段定义间以，分隔
     * @return 我的申请任务数组
     */
    public ApplyTaskInfo[] getApplyTasks(String ownerAccount, Criterion[] criteria, int startPosition, int maxResults,
            String orderBy);

    /**
     * 获取满足约束条件的我的申请任务个数
     * 
     * @param ownerAccount 所属用户id
     * @param criteria     前端查询条件
     * @return 我的申请任务数
     */
    public ApplyTaskInfo[] getApplyTasks(String ownerAccount, String[] trustors, Criterion[] criterions,
            int startPosition, int maxResults, String orderBy);

    /**
     * 获取满足约束条件的我的草稿箱任务个数
     * 
     * @param ownerAccount 所属用户id
     * @param criteria     前端查询条件
     * @return 我的起草任务数
     */
    public int getDraftTaskCount(String ownerAccount, Criterion[] criteria);

    /**
     * 获取满足约束条件的我的草稿箱任务个数
     * 
     * @param ownerAccount 所属用户id
     * @param criteria     前端查询条件
     * @return 我的起草任务数
     */
    public int getDraftTaskCount(String ownerAccount, String[] trustors, Criterion[] criteria);

    /**
     * 获取我的草稿箱任务列表
     * 
     * @param ownerAccount  所属用户id
     * @param criteria      前端查询条件
     * @param startPosition 返回记录前跳过的记录条数
     * @param maxResults    返回的结果记录条数，如果传入-1则返回所有的查询结果
     * @param orderBy       查询结果的排序设置，格式为\"字段
     *                      排序设置\"，其中排序设置的值为ASC或DESC，如果涉及多个字段，则多段定义间以，分隔
     * @return 分页草稿列表数据
     */
    public DraftTaskInfo[] getDraftTasks(String ownerAccount, Criterion[] criteria, int startPosition, int maxResults,
            String orderBy);

    /**
     * 获取我的草稿箱任务列表
     * 
     * @param ownerAccount  所属用户id
     * @param criteria      前端查询条件
     * @param startPosition 返回记录前跳过的记录条数
     * @param maxResults    返回的结果记录条数，如果传入-1则返回所有的查询结果
     * @param orderBy       查询结果的排序设置，格式为\"字段
     *                      排序设置\"，其中排序设置的值为ASC或DESC，如果涉及多个字段，则多段定义间以，分隔
     * @return 分页草稿列表数据
     */
    public DraftTaskInfo[] getDraftTasks(String ownerAccount, String[] trustors, Criterion[] criterions,
            int startPosition, int maxResults, String orderBy);

    /**
     * 获取满足约束条件的我的沟通任务个数
     * 
     * @param ownerAccount 所属用户账号
     * @param criteria     前端查询条件
     * @return 我的沟通任务数
     */
    public int getCommunicateTaskCount(String ownerAccount, Criterion[] criteria);

    /**
     * 获取满足约束条件的我的沟通任务个数
     * 
     * @param ownerAccount 所属用户账号
     * @param trustors
     * 
     * @param criteria     前端查询条件
     * @return 我的沟通任务数
     */
    public int getCommunicateTaskCount(String ownerAccount, String[] trustors, Criterion[] criterions);

    /**
     * 获取我的沟通任务列表
     * 
     * @param ownerAccount  所属用户账号
     * @param startPosition 返回记录前跳过的记录条数
     * @param maxResults    返回的结果记录条数，如果传入-1则返回所有的查询结果
     * @param orderBy       查询结果的排序设置，格式为\"字段
     *                      排序设置\"，其中排序设置的值为ASC或DESC，如果涉及多个字段，则多段定义间以，分隔
     * @param criteria      前端查询条件
     * @return 分页草稿列表数据
     */
    public CommunicateTaskInfo[] getCommunicateTasks(String ownerAccount, int startPosition, int maxResults,
            OrderBy[] orderBy, Criterion[] criteria);

    /**
     * 获取我的沟通任务列表
     * 
     * @param ownerAccount  所属用户账号
     * @param startPosition 返回记录前跳过的记录条数
     * @param maxResults    返回的结果记录条数，如果传入-1则返回所有的查询结果
     * @param orderBy       查询结果的排序设置，格式为\"字段
     *                      排序设置\"，其中排序设置的值为ASC或DESC，如果涉及多个字段，则多段定义间以，分隔
     * @param criteria      前端查询条件
     * @return 分页草稿列表数据
     */
    public CommunicateTaskInfo[] getCommunicateTasks(String ownerAccount, String[] trustors, int startPosition,
            int maxResults, OrderBy[] orderBy, Criterion[] criterions);

    /**
     * 获取满足约束条件的我的回复沟通任务个数
     * 
     * @param ownerAccount 所属用户账号
     * @param criteria     前端查询条件
     * @return 我的回复沟通任务数
     */
    public int getReplyCommunicateTaskCount(String ownerAccount, Criterion[] criteria);

    /**
     * 获取满足约束条件的我的回复沟通任务个数
     * 
     * @param ownerAccount 所属用户账号
     * @param criteria     前端查询条件
     * @return 我的回复沟通任务数
     */
    public int getReplyCommunicateTaskCount(String ownerAccount, String[] trustors, Criterion[] criteria);

    /**
     * 获取我的回复沟通任务列表
     * 
     * @param ownerAccount  所属用户账号
     * @param startPosition 返回记录前跳过的记录条数
     * @param maxResults    返回的结果记录条数，如果传入-1则返回所有的查询结果
     * @param orderBy       查询结果的排序设置，格式为\"字段
     *                      排序设置\"，其中排序设置的值为ASC或DESC，如果涉及多个字段，则多段定义间以，分隔
     * @param criteria      前端查询条件
     * @return 分页回复沟通列表数据
     */
    public ReplyCommunicateTaskInfo[] getReplyCommunicateTasks(String ownerAccount, int startPosition, int maxResults,
            OrderBy[] orderBy, Criterion[] criteria);

    /**
     * 获取我的回复沟通任务列表
     * 
     * @param ownerAccount  所属用户账号
     * @param startPosition 返回记录前跳过的记录条数
     * @param maxResults    返回的结果记录条数，如果传入-1则返回所有的查询结果
     * @param orderBy       查询结果的排序设置，格式为\"字段
     *                      排序设置\"，其中排序设置的值为ASC或DESC，如果涉及多个字段，则多段定义间以，分隔
     * @param criteria      前端查询条件
     * @return 分页回复沟通列表数据
     */
    public ReplyCommunicateTaskInfo[] getReplyCommunicateTasks(String ownerAccount, String[] trustors,
            int startPosition, int maxResults, OrderBy[] orderBy, Criterion[] criteria);

    /**
     * 返回获取满足约束条件的我发起的沟通任务个数
     * 
     * @param senderAccount 发送用户账号
     * @param criteria      前端查询条件
     * @return 我发起的沟通任务数
     */
    public int getSenderCommunicateTaskCount(String senderAccount, Criterion[] criteria);

    /**
     * 返回获取满足约束条件的我发起的沟通任务个数
     * 
     * @param senderAccount 发送用户账号
     * @param criteria      前端查询条件
     * @return 我发起的沟通任务数
     */
    public int getSenderCommunicateTaskCount(String senderAccount, String[] trustors, Criterion[] criteria);

    /**
     * 获取我发起的沟通任务列表
     * 
     * @param senderAccount 所属用户账号
     * @param startPosition 返回记录前跳过的记录条数
     * @param maxResults    返回的结果记录条数，如果传入-1则返回所有的查询结果
     * @param orderBy       查询结果的排序设置，格式为\"字段
     *                      排序设置\"，其中排序设置的值为ASC或DESC，如果涉及多个字段，则多段定义间以，分隔
     * @param criteria      前端查询条件
     * @return 分页我发起的沟通列表数据
     */
    public SenderCommunicateTaskInfo[] getSenderCommunicateTasks(String senderAccount, int startPosition,
            int maxResults, OrderBy[] orderBy, Criterion[] criteria);

    /**
     * 获取满足约束条件的我的知会（待阅）任务个数
     * 
     * @param ownerAccount 所属用户id
     * @param criteria     前端查询条件
     * @return 我的知会（待阅）任务数
     */
    public int getInformTaskCount(String ownerAccount, Criterion[] criteria);

    /**
     * 获取满足约束条件的我的知会（待阅）任务个数
     * 
     * @param ownerAccount 所属用户id
     * @param criteria     前端查询条件
     * @return 我的知会（待阅）任务数
     */
    public int getInformTaskCount(String ownerAccount, String[] trustors, Criterion[] criteria);

    /**
     * 获取我的知会（待阅）任务列表
     * 
     * @param ownerAccount  所属用户id
     * @param startPosition 返回记录前跳过的记录条数
     * @param maxResults    返回的结果记录条数，如果传入-1则返回所有的查询结果
     * @param orderBy       查询结果的排序设置，格式为\"字段
     *                      排序设置\"，其中排序设置的值为ASC或DESC，如果涉及多个字段，则多段定义间以，分隔
     * @param criteria      前端查询条件
     * @return 分页知会（待阅）列表数据
     */
    public InformTaskInfo[] getInformTasks(String ownerAccount, int startPosition, int maxResults, OrderBy[] orderBy,
            Criterion[] criteria);

    /**
     * 获取我的知会（待阅）任务列表
     * 
     * @param ownerAccount  所属用户id
     * @param startPosition 返回记录前跳过的记录条数
     * @param maxResults    返回的结果记录条数，如果传入-1则返回所有的查询结果
     * @param orderBy       查询结果的排序设置，格式为\"字段
     *                      排序设置\"，其中排序设置的值为ASC或DESC，如果涉及多个字段，则多段定义间以，分隔
     * @param criteria      前端查询条件
     * @return 分页知会（待阅）列表数据
     */
    public InformTaskInfo[] getInformTasks(String ownerAccount, String[] trustors, int startPosition, int maxResults,
            OrderBy[] orderBy, Criterion[] criteria);

    /**
     * 获取满足约束条件的我发送的知会（待阅）任务个数
     * 
     * @param senderAccount 发送者用户account
     * @param criteria      前端查询条件
     * @return 我的知会（待阅）任务数
     */
    public int getSenderInformTaskCount(String senderAccount, Criterion[] criteria);

    /**
     * 获取满足约束条件的我发送的知会（待阅）任务个数
     * 
     * @param senderAccount 发送者用户account
     * @param criteria      前端查询条件
     * @return 我的知会（待阅）任务数
     */
    public int getSenderInformTaskCount(String senderAccount, String[] trustors, Criterion[] criteria);

    /**
     * 获取我发送的知会（待阅）任务列表
     * 
     * @param senderAccount 发送者用户account
     * @param startPosition 返回记录前跳过的记录条数
     * @param maxResults    返回的结果记录条数，如果传入-1则返回所有的查询结果
     * @param orderBy       查询结果的排序设置，格式为\"字段
     *                      排序设置\"，其中排序设置的值为ASC或DESC，如果涉及多个字段，则多段定义间以，分隔
     * @param criteria      前端查询条件
     * @return 分页知会（待阅）列表数据
     */
    public SenderInformTaskInfo[] getSenderInformTasks(String senderAccount, int startPosition, int maxResults,
            OrderBy[] orderBy, Criterion[] criteria);

    /**
     * 获取我发送的知会（待阅）任务列表
     * 
     * @param senderAccount 发送者用户account
     * @param startPosition 返回记录前跳过的记录条数
     * @param maxResults    返回的结果记录条数，如果传入-1则返回所有的查询结果
     * @param orderBy       查询结果的排序设置，格式为\"字段
     *                      排序设置\"，其中排序设置的值为ASC或DESC，如果涉及多个字段，则多段定义间以，分隔
     * @param criteria      前端查询条件
     * @return 分页知会（待阅）列表数据
     */
    public SenderInformTaskInfo[] getSenderInformTasks(String senderAccount, String[] trustors, int startPosition,
            int maxResults, OrderBy[] orderBy, Criterion[] criteria);

    /**
     * 获取满足约束条件的我的回复知会（已阅）任务个数
     * 
     * @param ownerAccount 所属用户账户
     * @param expressions  前端查询条件
     * @return 我的回复知会（待阅）任务数
     * @deprecated 此方法废弃，使用方法{@link #getReplyInformTaskCount(String, Criterion[])}代替
     */
    @Deprecated
    public int getReplyInformTaskCount(User owner, Expression[] expressions, Map<String, String> queryFieldMapping);

    /**
     * 获取满足约束条件的我的回复知会（已阅）任务个数
     * <p>
     * 此方法支持第三方表单过滤
     * 
     * @param ownerAccount 所属用户账户
     * @param criterions   前端查询条件
     * @return 我的回复知会（待阅）任务数
     */
    public int getReplyInformTaskCount(String ownerAccount, Criterion[] criterions);

    /**
     * 获取满足约束条件的我的回复知会（已阅）任务个数
     * <p>
     * 此方法支持第三方表单过滤
     * 
     * @param ownerAccount 所属用户账户
     * @param criterions   前端查询条件
     * @return 我的回复知会（待阅）任务数
     */
    public int getReplyInformTaskCount(String ownerAccount, String[] trustors, Criterion[] criterions);

    /**
     * 获取我的回复知会（已阅）任务列表
     * 
     * @param ownerAccount  所属用户账户
     * @param startPosition 返回记录前跳过的记录条数
     * @param maxResults    返回的结果记录条数，如果传入-1则返回所有的查询结果
     * @param orderBy       查询结果的排序设置，格式为\"字段
     *                      排序设置\"，其中排序设置的值为ASC或DESC，如果涉及多个字段，则多段定义间以，分隔
     * @param expressions   前端查询条件
     * @return 分页回复知会（待阅）列表数据
     * @deprecated 此方法废弃，使用方法{@link #getReplyInformTasks(User, Criterion[], int,
     *             int, String)代替
     * 
     */
    @Deprecated
    public ReplyInformTaskInfo[] getReplyInformTasks(User owner, Expression[] expressions, int startPosition,
            int maxResults, String orderBy);

    /**
     * 获取我的回复知会（已阅）任务列表
     * <p>
     * 此方法支持第三方表单过滤
     * 
     * @param ownerAccount  所属用户账户
     * @param startPosition 返回记录前跳过的记录条数
     * @param maxResults    返回的结果记录条数，如果传入-1则返回所有的查询结果
     * @param orderBy       查询结果的排序设置
     * @param criterions    前端查询条件
     * @return 分页回复知会（待阅）列表数据
     */
    public ReplyInformTaskInfo[] getReplyInformTasks(String ownerAccount, Criterion[] criterions, int startPosition,
            int maxResults, OrderBy[] orderBy);

    /**
     * 获取我的回复知会（已阅）任务列表
     * <p>
     * 此方法支持第三方表单过滤
     * 
     * @param ownerAccount  所属用户账户
     * @param startPosition 返回记录前跳过的记录条数
     * @param maxResults    返回的结果记录条数，如果传入-1则返回所有的查询结果
     * @param orderBy       查询结果的排序设置
     * @param criterions    前端查询条件
     * @return 分页回复知会（待阅）列表数据
     */
    public ReplyInformTaskInfo[] getReplyInformTasks(String ownerAccount, String[] trustors, Criterion[] criterions,
            int startPosition, int maxResults, OrderBy[] orderBy);

    /**
     * 获取满足约束条件的承接已阅任务个数
     * 
     * @param expressions 前端查询条件
     * @return 承接已阅任务个数
     */
    public int getHandoverReplyInformTaskCount(Expression[] expressions);

    /**
     * 获取满足约束条件的承接已阅任务个数
     * 
     * @param expressions 前端查询条件
     * @return 承接已阅任务个数
     */
    public int getHandoverReplyInformTaskCount(String ownerAccount, Expression[] expressions);

    /**
     * 获取满足约束条件的承接已阅任务个数
     * 
     * @param expressions 前端查询条件
     * @return 承接已阅任务个数
     */
    public int getHandoverReplyInformTaskCount(String ownerAccount, String[] trustors, Expression[] expressions);

    /**
     * 获取承接回复知会(已阅)任务列表
     * 
     * @param expressions   前端查询条件
     * @param startPosition 返回记录前跳过的记录条数
     * @param maxResults    返回的结果记录条数，如果传入-1则返回所有的查询结果
     * @param orderBy       查询结果的排序设置，格式为\"字段
     *                      排序设置\"，其中排序设置的值为ASC或DESC，如果涉及多个字段，则多段定义间以，分隔
     * @return 分页展示承接回复知会(已阅)列表数据
     */
    public ReplyInformTaskInfo[] getHandoverReplyInformTasks(Expression[] expressions, int startPosition,
            int maxResults, String orderBy);

    /**
     * 获取承接回复知会(已阅)任务列表
     * 
     * @param expressions   前端查询条件
     * @param startPosition 返回记录前跳过的记录条数
     * @param maxResults    返回的结果记录条数，如果传入-1则返回所有的查询结果
     * @param orderBy       查询结果的排序设置，格式为\"字段
     *                      排序设置\"，其中排序设置的值为ASC或DESC，如果涉及多个字段，则多段定义间以，分隔
     * @return 分页展示承接回复知会(已阅)列表数据
     */
    public ReplyInformTaskInfo[] getHandoverReplyInformTasks(String ownerAccount, Expression[] expressions,
            int startPosition, int maxResults, String orderBy);

    /**
     * 获取承接回复知会(已阅)任务列表
     * 
     * @param expressions   前端查询条件
     * @param startPosition 返回记录前跳过的记录条数
     * @param maxResults    返回的结果记录条数，如果传入-1则返回所有的查询结果
     * @param orderBy       查询结果的排序设置，格式为\"字段
     *                      排序设置\"，其中排序设置的值为ASC或DESC，如果涉及多个字段，则多段定义间以，分隔
     * @return 分页展示承接回复知会(已阅)列表数据
     */
    public ReplyInformTaskInfo[] getHandoverReplyInformTasks(String ownerAccount, String[] trustors,
            Expression[] expressions, int startPosition, int maxResults, String orderBy);

    /**
     * 获取满足约束条件的我的关注任务个数
     * 
     * @param ownerAccount 所属用户id
     * @param criteria     前端查询条件
     * @return 我的申请任务数
     */
    public int getAttentionTaskCount(String ownerAccount, Criterion[] criteria);

    /**
     * 获取满足约束条件的我的关注任务个数
     * 
     * @param ownerAccount 所属用户id
     * @param criteria     前端查询条件
     * @return 我的申请任务数
     */
    public int getAttentionTaskCount(String ownerAccount, String[] trustors, Criterion[] criteria);

    /**
     * 获取我的关注任务列表
     * 
     * @param ownerAccount  所属用户账户
     * @param startPosition 返回记录前跳过的记录条数
     * @param maxResults    返回的结果记录条数，如果传入-1则返回所有的查询结果
     * @param orderBy       查询结果的排序设置，格式为\"字段
     *                      排序设置\"，其中排序设置的值为ASC或DESC，如果涉及多个字段，则多段定义间以，分隔
     * @param criteria      前端查询条件
     * @return 分页关注列表数据
     */
    public AttentionTaskInfo[] getAttentionTasks(String ownerAccount, int startPosition, int maxResults,
            OrderBy[] orderBy, Criterion[] criteria);

    /**
     * 获取我的关注任务列表
     * 
     * @param ownerAccount  所属用户账户
     * @param startPosition 返回记录前跳过的记录条数
     * @param maxResults    返回的结果记录条数，如果传入-1则返回所有的查询结果
     * @param orderBy       查询结果的排序设置，格式为\"字段
     *                      排序设置\"，其中排序设置的值为ASC或DESC，如果涉及多个字段，则多段定义间以，分隔
     * @param criteria      前端查询条件
     * @return 分页关注列表数据
     */
    public AttentionTaskInfo[] getAttentionTasks(String ownerAccount, String[] trustors, int startPosition,
            int maxResults, OrderBy[] orderBy, Criterion[] criteria);

    /**
     * 获取满足约束条件的我的提醒任务个数
     * 
     * @param ownerAccount 所属用户账户
     * @param criteria     前端查询条件
     * @return 我的提醒任务数
     */
    public int getRemindTaskCount(String ownerAccount, Criterion[] criteria);

    /**
     * 获取满足约束条件的我的提醒任务个数
     * 
     * @param ownerAccount 所属用户账户
     * @param criteria     前端查询条件
     * @return 我的提醒任务数
     */
    public int getRemindTaskCount(String ownerAccount, String[] trustors, Criterion[] criteria);

    /**
     * 获取我的提醒任务列表
     * 
     * @param ownerAccount  所属用户id
     * @param startPosition 返回记录前跳过的记录条数
     * @param maxResults    返回的结果记录条数，如果传入-1则返回所有的查询结果
     * @param orderBy       查询结果的排序设置，格式为\"字段
     *                      排序设置\"，其中排序设置的值为ASC或DESC，如果涉及多个字段，则多段定义间以，分隔
     * @param criteria      前端查询条件
     * @return 分页提醒列表数据
     */
    public RemindTaskInfo[] getRemindTasks(String ownerAccount, int startPosition, int maxResults, OrderBy[] orderBy,
            Criterion[] criteria);

    /**
     * 获取我的提醒任务列表
     * 
     * @param ownerAccount  所属用户id
     * @param startPosition 返回记录前跳过的记录条数
     * @param maxResults    返回的结果记录条数，如果传入-1则返回所有的查询结果
     * @param orderBy       查询结果的排序设置，格式为\"字段
     *                      排序设置\"，其中排序设置的值为ASC或DESC，如果涉及多个字段，则多段定义间以，分隔
     * @param criteria      前端查询条件
     * @return 分页提醒列表数据
     */
    public RemindTaskInfo[] getRemindTasks(String ownerAccount, String[] trustors, int startPosition, int maxResults,
            OrderBy[] orderBy, Criterion[] criteria);

    /**
     * 获取我发起的沟通数据
     * 
     * @param formId       表单ID
     * @param beanId       业务数据ID
     * @param activeStepId 当前活动节点ID
     * @param parentId     沟通父ID
     * @return rest沟通列表数据
     */
    public SenderCommunicateTaskInfo[] getSenderCommunicateTasks(String formId, long beanId, long instId,
            long activeStepId, String parentId, String account);

    /**
     * 获取我发起的沟通数据
     * 
     * @param formId       表单ID
     * @param beanId       业务数据ID
     * @param activeStepId 当前活动节点ID
     * @param parentId     沟通父ID
     * @return rest沟通列表数据
     */
    public SenderCommunicateTaskInfo[] getSenderCommunicateTasks(String formId, long beanId, long instId,
            long activeStepId, String parentId, String account, String[] trustors);

    /**
     * 我发起的催办列表
     * 
     * @param senderAccount 发送人账户
     * @param startPosition 起始位置
     * @param maxResults    最大返回数量
     * @param orderBy       排序
     * @param criteria      查询条件
     * @return
     */
    public ReminderTaskInfo[] getSenderReminderTasks(String senderAccount, int startPosition, int maxResults,
            String orderBy, Criterion[] criteria);

    /**
     * 我发起的催办数量
     * 
     * @param senderAccount 发送人账户
     * @param criteria      查询条件
     * @return
     */
    public int getSenderReminderTaskCount(String senderAccount, Criterion[] criteria);

    /**
     * 我收到的催办列表
     * 
     * @param ownerAccount  接收人账户
     * @param startPosition 起始位置
     * @param maxResults    最大返回数量
     * @param orderBy       排序
     * @param criteria      查询条件
     * @return
     */
    public ReminderTaskInfo[] getReminderTasks(String ownerAccount, int startPosition, int maxResults, String orderBy,
            Criterion[] criteria);

    /**
     * 我收到的催办数量
     * 
     * @param senderAccount 发送人账户
     * @param criteria      查询条件
     * @return
     */
    public int getReminderTaskCount(String ownerAccount, Criterion[] criteria);
}
