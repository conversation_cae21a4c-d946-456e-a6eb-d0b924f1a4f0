/*
 * Copyright 2013-2019 Smartdot Technologies Co., Ltd. All rights reserved.
 * SMARTDOT PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 */
package com.hd.rcugrc.bpm.facade.form.util;

/**
 * <p>
 * 表单通用处理工具类
 * <AUTHOR> href="mailto:<EMAIL>">songjj</a>
 * @version 5.4, 2019年7月3日
 */
public class ProcessFormFieldUtil {

    protected ProcessFormFieldUtil() {
        super();
    }
    
    /** 模式 只读   {@value #FORM_MODEL_VIEW}*/
    public static final String FORM_MODEL_VIEW = "VIEW";
    /** 模式 编辑: {@value #FORM_MODEL_EDIT}*/
    public static final String FORM_MODEL_EDIT = "EDIT";
    /** 模式 编辑: {@value #FORM_MODEL_ADMIN_EDIT}*/
    public static final String FORM_MODEL_ADMIN_EDIT = "ADMIN_EDIT";
    /** 模式 送阅:{@value #FORM_MODEL_VIEWTOREAD}*/
    public static final String FORM_MODEL_VIEWTOREAD = "VIEWTOREAD";
    /** 模式 沟通:{@value #FORM_MODEL_VIEWCOMM}*/
    public static final String FORM_MODEL_VIEWCOMM = "VIEWCOMM";
    /** 模式 申请:{@value #FORM_MODEL_VIEWAPP}*/
    public static final String FORM_MODEL_VIEWAPP = "VIEWAPP";
    
    /** jquery附件中 存放附件参数名称{@value #PARAM_NAME_JQUERY_ATTACHMENT} */
    public static final String PARAM_NAME_JQUERY_ATTACHMENT = "attachments";
    
    /** indidox附件中 存放zw附件参数名称 {@value #PARAM_NAME_INDIDOCX_ZW_ATTACHMENT}  */
    public static final String PARAM_NAME_INDIDOCX_ZW_ATTACHMENT = "indiDocZwAttachments";
    
    /** indidox附件中 存放附件参数名称 {@value #PARAM_NAME_INDIDOCX_ATTACHMENT} */
    public static final String PARAM_NAME_INDIDOCX_ATTACHMENT = "indiDocAttachments";
    
    /** jquery控件名称 {@value #FORM_FIELD_NAME_JQUERY_ATTACHEMNT} */
    public static final String FORM_FIELD_NAME_JQUERY_ATTACHEMNT = "hdjqueryattach";
    
    /** indidocx控件名称 {@value #FORM_FIELD_NAME_INDIDOCX_ATTACHEMNT} */
    public static final String FORM_FIELD_NAME_INDIDOCX_ATTACHEMNT = "hdIndiDocX";
    
    /** NTKO控件名称 {@value #FORM_FIELD_NAME_NTKO_ATTACHEMNT} */
    public static final String FORM_FIELD_NAME_NTKO_ATTACHEMNT = "hdDocField";
    
    
    /** 平台控件类型 {@value #PLATFORM_CONTOL_CAT} */
    public static final int PLATFORM_CONTOL_CAT = 1;
    
    /** 产品控件类型 {@value #PRODUCT_CONTOL_CAT} */
    public static final int PRODUCT_CONTOL_CAT = 2;
    
    /** 项目控件类型 {@value #PROJECT_CONTOL_CAT} */
    public static final int PROJECT_CONTOL_CAT = 3;
    
    /** 未知控件类型 {@value #UNKOWN_CONTOL_CAT} */
    public static final int UNKOWN_CONTOL_CAT = -1;
}

