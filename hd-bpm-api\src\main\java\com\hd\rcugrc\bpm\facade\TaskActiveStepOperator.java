/*
 * Copyright 2013-2020 Smartdot Technologies Co., Ltd. All rights reserved.
 * SMARTDOT PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 */
package com.hd.rcugrc.bpm.facade;

import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <p>
 * 返回环节中尚未参加处理人员信息 主要用于并行环节多人处理可减签人员列表展示使用
 * 
 * <AUTHOR> href="mailto:<EMAIL>">yanglx</a>
 * @version v5.5.1, 2020年11月26日
 */
@Schema(title = "流程实例活动环节中尚未参加处理人员信息", description = "主要用于并行环节多人处理可减签人员列表展示")
public class TaskActiveStepOperator implements Serializable {

    private static final long serialVersionUID = 8434546980724485156L;

    private long instId;

    private long activeStepId;

    private String stepName;

    private int stepSn;

    private TaskOperator[] taskOperator;

    /**
     * 返回流程实例ID
     * 
     * @return 流程实例ID
     */
    @Schema(description = "流程实例ID", example = "1")
    public long getInstId() {
        return instId;
    }

    /**
     * 设置流程实例ID
     * 
     * @param instId 流程实例ID
     */
    public void setInstId(long instId) {
        this.instId = instId;
    }

    /**
     * 返回当前环节名称
     * 
     * @return 当前环节名称
     */
    @Schema(description = "当前环节名称", example = "环节1")
    public String getStepName() {
        return stepName;
    }

    /**
     * 设置当前环节名称
     * 
     * @param stepName 当前环节名称
     */
    public void setStepName(String stepName) {
        this.stepName = stepName;
    }

    /**
     * 获取活动环节ID
     * 
     * @return 活动环节ID
     */
    @Schema(description = "活动环节ID", example = "100")
    public long getActiveStepId() {
        return activeStepId;
    }

    /**
     * 设置活动环节ID
     * 
     * @param activeStepId 活动环节ID
     */
    public void setActiveStepId(long activeStepId) {
        this.activeStepId = activeStepId;
    }

    /**
     * 获取环节步骤号
     * 
     * @return 环节步骤号
     */
    @Schema(description = "环节步骤号", example = "2")
    public int getStepSn() {
        return stepSn;
    }

    /**
     * 设置环节步骤号
     * 
     * @param stepSn 环节步骤号
     */
    public void setStepSn(int stepSn) {
        this.stepSn = stepSn;
    }

    @Schema(description = "程实例活动环节中尚未参加处理人员信息", example = "{\r\n"
            + "      \"stepOperatorId\": 1,\r\n"
            + "      \"operators\": [\r\n" 
            + "        {\r\n" 
            + "          \"type\": \"User\",\r\n"
            + "          \"code\": \"慧点用户\",\r\n" 
            + "          \"name\": \"hd\",\r\n" 
            + "          \"attrs\": null,\r\n"
            + "          \"attrNames\": []\r\n" 
            + "        }\r\n" 
            + "      ],\r\n" 
            + "      \"index\": 1,\r\n"
            + "      \"status\": \"处理中\"\r\n" 
            + "    }")
    public TaskOperator[] getTaskOperator() {
        return taskOperator;
    }

    public void setTaskOperator(TaskOperator[] taskOperator) {
        this.taskOperator = taskOperator;
    }

}
