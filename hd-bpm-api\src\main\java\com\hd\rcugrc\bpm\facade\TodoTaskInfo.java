package com.hd.rcugrc.bpm.facade;

import java.io.Serializable;

import org.joda.time.DateTime;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 待办信息对象
 *
 * <AUTHOR> href="mailto:<EMAIL>">songjj</a>
 * @version 1.0, 2019年4月12日
 *
 */
@Schema(description = "待办信息对象,待办包含工作流待办和业务待办，\r\n"
        + "以下examples表示：返回账户hd下标题为test的前10条待办列表数据数据,该数据中包含用户所在GROUP级别的组织信息，并且按照待办产生日期（sentDate）升序排序。")
public class TodoTaskInfo implements Serializable {

    private static final long serialVersionUID = 2114493194784143299L;
    
    private String id;
    private int priority;
    private String title;
    private String link;
    private String receiverType;
    private String receiverCode;
    private String receiverName;
    private String senderCode;
    private String senderName;
    private DateTime sentDate;
    private boolean seen;
    private DateTime seenDate;
    private String module;
    private long instId;
    private String beanId;
    private String formId;
    private String processId;
    private String processName;
    private String currentStep;
    private String currentStepId;
    private DateTime alarmDate;
    private DateTime dueDate;
    private int timeStatus;

    private int status;
    /**
     * 流程发起人账号
     */
    private String creatorAccount;
    private String creatorName;
    private String creatorDeptName;
    private DateTime creationTime;

    private String appCat;
    private String appId;
    private int openMode;
    private int isTop;
    private String bsid;
    private String[] reservesString;
    private Long[] reservesLong;
    private String srcTrustId;
    private int processState;
    private Long[] pdReservesLong;
    
    /**
     * 委托人帐号
     */
    private String trustor;
    /**
     * 委托人姓名
     */
    private String trustorName;
    private String mobileAddr;
    private String openLink;
    private String pcAddr;
    private String openParam;
    private int urgencyLevel;
    private String urgencyLevelName;
    
    /**
     * 返回接收用户是以受托人身份接收待办事宜时的委托人帐号
     *
     * @return
     * @since 5.4
     */
    @Schema(description = "委托人账号", example = "zhangsan")
    public String getTrustor() {
        return trustor;
    }
    
    /**
     * 设置接收用户是以受托人身份接收待办事宜时的委托人帐号
     *
     * @param trustor
     * @since 5.4
     */
    public void setTrustor(String trustor) {
        this.trustor = trustor;
    }
    
    /**
     * @return the trustorName
     */
    @Schema(description = "委托人姓名", example = "张三")
    public String getTrustorName() {
        return trustorName;
    }

    /**
     * @param trustorName the trustorName to set
     */
    public void setTrustorName(String trustorName) {
        this.trustorName = trustorName;
    }

    /**
     * 获取待办信息id
     *
     * @return 待办信息id
     */
    @Schema(title = "ID", description = "待办信息主键", example = "1")
    public String getId() {
        return id;
    }

    /**
     * 设置待办信息id
     *
     * @param id 待办信息id
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * 获取待办优先级
     *
     * @return 待办优先级
     */
    @Schema(description = "处理优先级", example = "1")
    public int getPriority() {
        return priority;
    }

    /**
     * 设置待办优先级
     *
     * @param priority 优先级
     */
    public void setPriority(int priority) {
        this.priority = priority;
    }

    /**
     * 获取待办标题
     *
     * @return 待办标题
     */
    @Schema(description = "标题", example = "待办测试标题")
    public String getTitle() {
        return title;
    }

    /**
     * 设置待办标题
     *
     * @param title 待办标题
     */
    public void setTitle(String title) {
        this.title = title;
    }

    /**
     * 获取待办链接
     *
     * @return 待办链接
     */
    @Schema(description = "链接")
    public String getLink() {
        return link;
    }

    /**
     * 设置待办链接
     *
     * @param link 链接url
     */
    public void setLink(String link) {
        this.link = link;
    }

    /**
     * 获取接收者类型
     *
     * @return 接收者类型
     */
    @Schema(description = "接收者类型")
    public String getReceiverType() {
        return receiverType;
    }

    /**
     * 设置接收者类型
     *
     * @param receiverType 接收者类型
     */
    public void setReceiverType(String receiverType) {
        this.receiverType = receiverType;
    }

    /**
     * 获取接收者编码
     *
     * @return 接收者代码
     */
    @Schema(description = "接收者代码", example = "hd")
    public String getReceiverCode() {
        return receiverCode;
    }

    /**
     * 设置接收者编码
     *
     * @param receiverCode 接收者编码
     */
    public void setReceiverCode(String receiverCode) {
        this.receiverCode = receiverCode;
    }

    /**
     * 获取接收者名字
     *
     * @return 接收者名字
     */
    @Schema(description = "接收者名字", example = "慧点人员")
    public String getReceiverName() {
        return receiverName;
    }

    /**
     * 设置接收者名字
     *
     * @param receiverName 接收者名字
     */
    public void setReceiverName(String receiverName) {
        this.receiverName = receiverName;
    }

    /**
     * 获取发送者代码
     *
     * @return 发送者代码
     */
    @Schema(description = "发送者账户", example = "tester")
    public String getSenderCode() {
        return senderCode;
    }

    /**
     * 设置发送者编码
     *
     * @param senderCode 发送者编码
     */
    public void setSenderCode(String senderCode) {
        this.senderCode = senderCode;
    }

    /**
     * 获取发送者名字
     *
     * @return 发送者名字
     */
    @Schema(description = "发送者名字", example = "发送人员")
    public String getSenderName() {
        return senderName;
    }

    /**
     * 设置发送者名字
     *
     * @parm senderName 发送者名字
     */
    public void setSenderName(String senderName) {
        this.senderName = senderName;
    }

    /**
     * 获取发送日期
     *
     * @return 发送日期
     */
    @Schema(description = "发送日期")
    public DateTime getSentDate() {
        return sentDate;
    }

    /**
     * 设置发送日期
     *
     * @param sentDate 发送日期
     */
    public void setSentDate(DateTime sentDate) {
        this.sentDate = sentDate;
    }

    /**
     * 获取阅读标记
     *
     * @return 阅读标记
     */
    @Schema(description = "阅读标记, true 已阅读，false 未阅读")
    public boolean isSeen() {
        return seen;
    }

    /**
     * 设置阅读标记
     *
     * @param seen 阅读标记
     */
    public void setSeen(boolean seen) {
        this.seen = seen;
    }

    /**
     * 获取阅读日期
     *
     * @return 阅读日期
     */
    @Schema(description = "阅读日期")
    public DateTime getSeenDate() {
        return seenDate;
    }

    /**
     * 设置阅读日期
     *
     * @param seenDate 阅读日期
     */
    public void setSeenDate(DateTime seenDate) {
        this.seenDate = seenDate;
    }

    /**
     * 获取待办事宜所隶属的模块
     *
     * @return 待办事宜所隶属的模块
     */
    @Schema(description = "待办事宜所隶属的模块")
    public String getModule() {
        return module;
    }

    /**
     * 设置待办事宜所隶属的模块
     *
     * @parm module 待办事宜所隶属的模块
     */
    public void setModule(String module) {
        this.module = module;
    }

    /**
     * 获取待办事宜所关联的数据id
     *
     * @return 待办事宜所关联的数据id
     */
    @Schema(description = "待办事宜所关联的流程实例id", example = "1")
    public long getInstId() {
        return instId;
    }

    /**
     * 设置待办事宜所关联的数据id
     *
     * @param instId 待办事宜所关联的数据id
     */
    public void setInstId(long instId) {
        this.instId = instId;
    }

    /**
     * 获取当前所在处理环节名字
     *
     * @return 当前所在处理环节名字
     */
    @Schema(description = "当前所在处理环节名字")
    public String getCurrentStep() {
        return currentStep;
    }

    /**
     * 设置当前所在处理环节名字
     *
     * @param currentStepName 当前所在处理环节名字
     */
    public void setCurrentStep(String currentStepName) {
        this.currentStep = currentStepName;
    }

    /**
     * 获取当前所在处理环节ID
     *
     * @return
     */
    @Schema(description = "当前所在处理环节ID", example = "1")
    public String getCurrentStepId() {
        return currentStepId;
    }

    /**
     * 设置当前所在处理环节名字
     *
     * @param currentStepId 当前所在处理环节ID
     */
    public void setCurrentStepId(String currentStepId) {
        this.currentStepId = currentStepId;
    }

    /**
     * 获取当前待办所关联formId
     *
     * @return 当前待办所关联formId
     */
    @Schema(description = "当前待办所关联FormId,待办所关联FormId 含协议前缀", example = "tj:testFrom")
    public String getFormId() {
        return formId;
    }

    /**
     * 设置当前待办所关联formId
     *
     * @param formId 当前待办所关联formId
     */
    public void setFormId(String formId) {
        this.formId = formId;
    }

    /**
     * 获取当前待办所关联BeanId
     *
     * @return 当前待办所关联BeanId
     */
    @Schema(description = "当前待办所关联BeanId")
    public String getBeanId() {
        return beanId;
    }

    /**
     * 设置当前待办所关联BeanId
     *
     * @param beanId 当前待办所关联BeanId
     */
    public void setBeanId(String beanId) {
        this.beanId = beanId;
    }

    /**
     * 获取处理超时警报的时间
     *
     * @return 处理超时警报的时间
     */
    @Schema(description = "处理超时警报的时间")
    public DateTime getAlarmDate() {
        return alarmDate;
    }

    /**
     * 设置处理超时警报的时间
     *
     * @param alarmDate 处理超时警报的时间
     */
    public void setAlarmDate(DateTime alarmDate) {
        this.alarmDate = alarmDate;
    }

    /**
     * 获取处理超时时间
     *
     * @return 处理超时时间
     */
    @Schema(description = "处理超时时间")
    public DateTime getDueDate() {
        return dueDate;
    }

    /**
     * 设置处理超时时间
     *
     * @param dueDate 处理超时时间
     */
    public void setDueDate(DateTime dueDate) {
        this.dueDate = dueDate;
    }

    /**
     * 获取流转事宜的处理时间状态，为<code>0</code>表示正常， <code>1</code>表示即将到期，<code>2</code>表示已超期
     *
     * @return 流转事宜的处理时间状态
     */
    @Schema(description = "流转事宜的处理时间状态，0 表示正常;1 表示即将到期;2表示已超期", example = "1")
    public int getTimeStatus() {
        return timeStatus;
    }

    /**
     * 设置流转事宜的处理时间状态
     *
     * @param timeStatus 流转事宜的处理时间状态
     */
    public void setTimeStatus(int timeStatus) {
        this.timeStatus = timeStatus;
    }

    /**
     * 获取流转事宜的状态，为0表示正常，为1表示当前正在处理中
     *
     * @return 流转事宜的状态
     */
    @Schema(description = "流转事宜的状态，0表示 正常;1表示当前正在处理中", example = "1")
    public int getStatus() {
        return status;
    }

    /**
     * 设置流转事宜的状态，为0表示正常，为1表示当前正在处理中
     *
     * @param status 流转事宜的状态
     */
    public void setStatus(int status) {
        this.status = status;
    }

    /**
     * 获取分类名称
     *
     * @return 分类名称
     */
    @Schema(description = "分类名称")
    public String getAppCat() {
        return appCat;
    }

    /**
     * 设置分类名称
     *
     * @param appCat 分类名称
     */
    public void setAppCat(String appCat) {
        this.appCat = appCat;
    }

    /**
     * 获取分类ID
     *
     * @return 分类ID
     */
    @Schema(description = "分类ID")
    public String getAppId() {
        return appId;
    }

    /**
     * 设置分类ID
     *
     * @param appId 分类ID
     */
    public void setAppId(String appId) {
        this.appId = appId;
    }

    /**
     * 获取创建人名称
     *
     * @return 创建人名称
     */
    @Schema(description = "创建人名称")
    public String getCreatorName() {
        return creatorName;
    }

    /**
     * 设置创建人名称
     *
     * @param creatorName 创建人名称
     */
    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }

    /**
     * 获取创建人所在组织名称
     *
     * @return 创建人所在组织名称
     */
    @Schema(description = "创建人所在组织名称")
    public String getCreatorDeptName() {
        return creatorDeptName;
    }

    /**
     * 设置创建人所在组织名称
     *
     * @param creatorDeptName 创建人所在组织名称
     */
    public void setCreatorDeptName(String creatorDeptName) {
        this.creatorDeptName = creatorDeptName;
    }

    /**
     * 获取 打开方式
     *
     * @return 打开方式
     */
    public int getOpenMode() {
        return openMode;
    }

    /**
     * 设置 打开方式
     *
     * @param openMode 打开方式
     */
    @Schema(description = "打开方式，目前方式为:" + "1  ： 平台表单并且编辑模式 \r\n" + "2  ： 非平台表单-太极表单并且编辑模式\r\n"
            + "31 ： 平台表单并且视图模式\r\n" + "32 ： 非平台表单-太极表单并且视图模式" + "-1 ： 未知模式", example = "1")
    public void setOpenMode(int openMode) {
        this.openMode = openMode;
    }

    /**
     *
     * @return 流程定义ID
     */
    @Schema(description = "流程定义ID", example = "test0001")
    public String getProcessId() {
        return processId;
    }

    /**
     * 设置 流程定义ID
     *
     * @param processId 流程定义Id
     */
    public void setProcessId(String processId) {
        this.processId = processId;
    }

    /**
     * @return 流程定义名称
     */
    @Schema(description = "流程定义名称", example = "品牌清单流程")
    public String getProcessName() {
        return processName;
    }

    /**
     * 设置 流程定义名称
     *
     * @param processName 流程定义名称
     */
    public void setProcessName(String processName) {
        this.processName = processName;
    }

    /**
     * 返回是否置顶 0非置顶，1置顶
     *
     * @return 是否置顶
     */
    @Schema(description = "是否置顶", example = "0")
    public int getIsTop() {
        return isTop;
    }

    /**
     * 设置是否置顶 0非置顶，1置顶
     *
     * @param isTop 是否置顶
     */
    public void setIsTop(int isTop) {
        this.isTop = isTop;
    }
    
    /**
     * 返回应用id
     * @return
     */
    public String getBsid() {
        return bsid;
    }
    
    /**
     * 设置应用id
     * @param bsid
     */
    public void setBsid(String bsid) {
        this.bsid = bsid;
    }
    
    /**
     * 待办所有数字型保留属性值列表
     * @return the reservesString
     */
    @Schema(description = "待办所有数字型保留属性值列表", example = "['', 'sameString2', 'sameString3', null, null]")
    public String[] getReservesString() {
        return reservesString;
    }

    /**
     * @param reservesString the reservesString to set
     */
    public void setReservesString(String[] reservesString) {
        this.reservesString = reservesString;
    }

    /**
     * 待办所有数字型保留属性值列表
     * @return the reservesLong
     */
    @Schema(description = "待办所有数字型保留属性值列表", example = "[null, 1, null, 5, null]")
    public Long[] getReservesLong() {
        return reservesLong;
    }

    /**
     * @param reservesLong the reservesLong to set
     */
    public void setReservesLong(Long[] reservesLong) {
        this.reservesLong = reservesLong;
    }

    /**
     * 数据包含兼职账号数据时，此字段为兼职信息的主键id
     * @return the srcTrustId
     */
    @Schema(description = "数据包含兼职账号数据时，此字段为兼职信息的主键id，"
            + "用于前端判断该数据是否为兼职账号数据，来做相关处理", example = "11")
    public String getSrcTrustId() {
        return srcTrustId;
    }

    /**
     * @param srcTrustId the srcTrustId to set
     */
    public void setSrcTrustId(String srcTrustId) {
        this.srcTrustId = srcTrustId;
    }

    /**
     * 返回待办的处理状态
     *
     * @return 待办的处理状态
     */
    @Schema(description = "待办的处理状态，0为就绪状态，可正常处理；3为回滚状态，为提交失败回滚回来的待办", example = "0")
    public int getProcessState() {
        return processState;
    }

    /**
     * 设置待办的处理状态
     *
     * @param processState 待办的处理状态
     */
    public void setProcessState(int processState) {
        this.processState = processState;
    }
    
    /**
     * 返回待办产品专用数字型保留属性值列表
     *
     * @return 待办产品专用数字型保留属性值列表
     */
    public Long[] getPdReservesLong() {
        return pdReservesLong;
    }

    /**
     * 设置待办产品专用数字型保留属性值列表
     *
     * @param pdReservesLong 待办产品专用数字型保留属性值列表
     */
    @Schema(description = "待办产品专用数字型保留属性值列表", example = "[null, 1, null, 5, null]")
    public void setPdReservesLong(Long[] pdReservesLong) {
        this.pdReservesLong = pdReservesLong;
    }
    
    @Schema(description = "流程发起人账号", example = "zhangsan")
    public String getCreatorAccount() {
        return creatorAccount;
    }

    public void setCreatorAccount(String creatorAccount) {
        this.creatorAccount = creatorAccount;
    }
    
    public DateTime getCreationTime() {
        return creationTime;
    }
    
    public void setCreationTime(DateTime creationTime) {
        this.creationTime = creationTime;
    }
    
    public void setMobileAddr(String mobileAddr) {
        this.mobileAddr = mobileAddr;
    }
    
    public String getMobileAddr() {
        return mobileAddr;
    }
    
    public void setOpenLink(String openLink) {
        this.openLink = openLink;
    }
    
    public String getOpenLink() {
        return openLink;
    }
    
    public void setPcAddr(String pcAddr) {
        this.pcAddr = pcAddr;
    }
    
    public String getPcAddr() {
        return pcAddr;
    }
    
    public void setOpenParam(String openParam) {
        this.openParam = openParam;
    }
    
    public String getOpenParam() {
        return openParam;
    }
    
    public void setUrgencyLevel(int urgencyLevel) {
        this.urgencyLevel = urgencyLevel;
    }
    
    public int getUrgencyLevel() {
        return urgencyLevel;
    }
    
    public void setUrgencyLevelName(String urgencyLevelName) {
        this.urgencyLevelName = urgencyLevelName;
    }
    
    public String getUrgencyLevelName() {
        return urgencyLevelName;
    }
}
