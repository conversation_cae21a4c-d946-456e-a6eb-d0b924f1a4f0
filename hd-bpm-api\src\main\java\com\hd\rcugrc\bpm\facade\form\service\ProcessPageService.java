/*
 * Copyright 2013-2019 Smartdot Technologies Co., Ltd. All rights reserved.
 * SMARTDOT PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 */
package com.hd.rcugrc.bpm.facade.form.service;

import java.util.Map;

import com.hd.rcugrc.bpm.facade.form.ProcessFormData;
import com.hd.rcugrc.bpm.facade.form.util.ProcessFormFieldUtil;
import com.hd.rcugrc.web.dwr.FormFieldValue;

/**
 * <p>
 * 
 * <pre>
 * 流程页面表单数据服务
 * 主要包含页面表单字段以及字段值获取
 * 
 * </pre>
 * 
 * <AUTHOR> href="mailto:<EMAIL>">songjj</a>
 * @version 5.4, 2019年7月2日
 */
public interface ProcessPageService {

    /**
     * 页面定义对象在上下文中的键值
     */
    public static final String PAGE_DEFINITION_OBJECT = "page";

    /**
     * 启动流程参数的流程ID KEY flowId
     */
    public static final String START_PROCESS_FLOW_ID = "flowId";
    /**
     * 启动流程参数保存在上下文的KEY paramMap
     */
    public static final String START_PROCESS_FLOW_URL_PARAM = "hdParamMap";

    /**
     * 获取流程页面表单数据,如果 就按照流程定义读取表单信息，只是表单信息中只有表单相关
     * 
     * @param processId    流程定义Id ，如果流程定义不为空但流程实例Id为空，就读取表单字段相关信息
     * @param instId       流程实例Id ,流程实例Id不为空 就读取表单字段信息以及字段值信息
     * @param activeStepId 当前活动环节Id
     * @param model        表单模式
     * 
     *                     <pre>
     * 编辑 {@link ProcessFormFieldUtil#FORM_MODEL_EDIT} 返回表单字段以及值外，还返回字段附加信息 如下拉框信息
     * 只读 {@link ProcessFormFieldUtil#FORM_MODEL_VIEW} 返回表单字段以及值 并且为只读模式
     * 送阅 {@link ProcessFormFieldUtil#FORM_MODEL_VIEWTOREAD} 返回表单字段以及值 并且为只读模式,部分送阅字段可编辑
     * 沟通 {@link ProcessFormFieldUtil#FORM_MODEL_VIEWCOMM}  返回表单字段以及值 并且为只读模式,部分沟通字段可编辑
     * 申请 {@link ProcessFormFieldUtil#FORM_MODEL_VIEWAPP} 返回表单字段以及值 并且为只读模式,部分申请字段可编辑
     *                     </pre>
     * 
     * @param context      上下文信息
     * @return 页面控件以及表单信息
     */
    @Deprecated
    public ProcessFormData getProcessFormData(String processId, long instId, long activeStepId, String model,
            Map<String, Object> context);

    /**
     * 获取流程页面表单数据,如果 就按照流程定义读取表单信息，只是表单信息中只有表单相关
     * 
     * @param processId    流程定义Id ，如果流程定义不为空但流程实例Id为空，就读取表单字段相关信息
     * @param instId       流程实例Id ,流程实例Id不为空 就读取表单字段信息以及字段值信息
     * @param activeStepId 当前活动环节Id
     * @param relId        流程-页面-表单-关联关系id
     * @param model        表单模式
     * 
     *                     <pre>
     * 编辑 {@link ProcessFormFieldUtil#FORM_MODEL_EDIT} 返回表单字段以及值外，还返回字段附加信息 如下拉框信息
     * 只读 {@link ProcessFormFieldUtil#FORM_MODEL_VIEW} 返回表单字段以及值 并且为只读模式
     * 送阅 {@link ProcessFormFieldUtil#FORM_MODEL_VIEWTOREAD} 返回表单字段以及值 并且为只读模式,部分送阅字段可编辑
     * 沟通 {@link ProcessFormFieldUtil#FORM_MODEL_VIEWCOMM}  返回表单字段以及值 并且为只读模式,部分沟通字段可编辑
     * 申请 {@link ProcessFormFieldUtil#FORM_MODEL_VIEWAPP} 返回表单字段以及值 并且为只读模式,部分申请字段可编辑
     *                     </pre>
     * 
     * @param context      上下文信息
     * @return 页面控件以及表单信息
     */
    public ProcessFormData getProcessFormData(String processId, long instId, long activeStepId, long relId,
            String model, Map<String, Object> context);
    
    /**
     * 移动端获取流程页面表单数据,如果 就按照流程定义读取表单信息，只是表单信息中只有表单相关
     * 
     * @param processId    流程定义Id ，如果流程定义不为空但流程实例Id为空，就读取表单字段相关信息
     * @param instId       流程实例Id ,流程实例Id不为空 就读取表单字段信息以及字段值信息
     * @param activeStepId 当前活动环节Id
     * @param relId        流程-页面-表单-关联关系id
     * @param model        表单模式
     * 
     *                     <pre>
     * 编辑 {@link ProcessFormFieldUtil#FORM_MODEL_EDIT} 返回表单字段以及值外，还返回字段附加信息 如下拉框信息
     * 只读 {@link ProcessFormFieldUtil#FORM_MODEL_VIEW} 返回表单字段以及值 并且为只读模式
     * 送阅 {@link ProcessFormFieldUtil#FORM_MODEL_VIEWTOREAD} 返回表单字段以及值 并且为只读模式,部分送阅字段可编辑
     * 沟通 {@link ProcessFormFieldUtil#FORM_MODEL_VIEWCOMM}  返回表单字段以及值 并且为只读模式,部分沟通字段可编辑
     * 申请 {@link ProcessFormFieldUtil#FORM_MODEL_VIEWAPP} 返回表单字段以及值 并且为只读模式,部分申请字段可编辑
     *                     </pre>
     * 
     * @param context      上下文信息
     * @return 页面控件以及表单信息按照移动端排序顺序返回
     */
    public ProcessFormData getMobileProcessFormData(String processId, long instId, long activeStepId, long relId,
            String model, Map<String, Object> context);
    

    /**
     * 根据业务beanId和表单Id,获取表单业务数据
     * 
     * @param beanId 业务beanId
     * @param formId 业务表单ID
     * @return 业务数据数组。
     */
    public FormFieldValue[] getFormData(long beanId, String formId);
}
