/*
 * Copyright 2013-2020 Smartdot Technologies Co., Ltd. All rights reserved.
 * SMARTDOT PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 */
package com.hd.rcugrc.bpm.facade.form.service;

/**
 * ProcessPageService 协议匹配代理
 * <p>
 * 
 * <AUTHOR> href="mailto:<EMAIL>">pandy</a>
 * @version 1.0, 2020年7月23日
 */
public interface ProcessPageServiceAgent extends ProcessPageService {

    /**
     * 
     * @param pageId 根据页面id，匹配到对应协议的 {@link ProcessPageService}实现
     * @return
     */
    public ProcessPageService getService(String pageId);
}

