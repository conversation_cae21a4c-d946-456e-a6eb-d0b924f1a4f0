/*
 * Copyright 2013-2021 Smartdot Technologies Co., Ltd. All rights reserved.
 * SMARTDOT PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 */
package com.hd.rcugrc.bpm.facade;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <p>用户密级信息
 * 
 * <AUTHOR> href="mailto:<EMAIL>">yanglx</a>
 * @version 1.0, 2021年4月12日
 */
public class UserClassifiedStrategyInfo extends UserClassifiedInfo {

    private static final long serialVersionUID = 782211303383651970L;
    private boolean includeUpper; //含大写字母
    private boolean includeLower; //含小写字母
    private boolean includeNum;   //含数字
    private boolean includeSpecialChar; //含特殊字符
    
    @Schema(description = "含大写字母", defaultValue = "false")
    public boolean isIncludeUpper() {
        return includeUpper;
    }
    
    public void setIncludeUpper(boolean includeUpper) {
        this.includeUpper = includeUpper;
    }
    
    @Schema(description = "含小写字母", defaultValue = "false")
    public boolean isIncludeLower() {
        return includeLower;
    }
    
    public void setIncludeLower(boolean includeLower) {
        this.includeLower = includeLower;
    }
    
    @Schema(description = "含数字", defaultValue = "false")
    public boolean isIncludeNum() {
        return includeNum;
    }
    
    public void setIncludeNum(boolean includeNum) {
        this.includeNum = includeNum;
    }
    
    @Schema(description = "含特殊字符", defaultValue = "false")
    public boolean isIncludeSpecialChar() {
        return includeSpecialChar;
    }
    
    public void setIncludeSpecialChar(boolean includeSpecialChar) {
        this.includeSpecialChar = includeSpecialChar;
    }
}

