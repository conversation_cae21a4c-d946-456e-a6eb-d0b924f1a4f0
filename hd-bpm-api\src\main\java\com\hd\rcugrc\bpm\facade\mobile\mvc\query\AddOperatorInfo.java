/*
 * Copyright 2013-2019 Smartdot Technologies Co., Ltd. All rights reserved.
 * SMARTDOT PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 */
package com.hd.rcugrc.bpm.facade.mobile.mvc.query;

import java.io.Serializable;
import java.util.Map;

import com.hd.rcugrc.bpm.facade.TimeConstraintDefinition;

import io.swagger.v3.oas.annotations.media.Schema;


/**
 * <p>
 * 流程实例活动环节加签参数，主要用于Restful接口
 *
 * <AUTHOR> href="mailto:<EMAIL>">songjj</a>
 * @version 5.3, 2019年4月10日
 */
@Schema(title = "AddOperatorInfo", description = "流程实例活动环节加签参数-加签环节-于需要加签的人员")
public class AddOperatorInfo implements Serializable {
    
    /**
     *
     */
    private static final long serialVersionUID = -2207146071441161892L;
    
    private long activeStepId;
    
    private OperatorBean[] operators;
    
    private TimeConstraintDefinition timer;
    
    private Map<String, String> props;
    
    /**
     * @return the activeStepId
     */
    @Schema(title = "当前环节ID，如果不指定，则为当前环节数据的第一个", description = "当前环节ID，如果不指定，则为当前环节数据的第一个", example = "100")
    public long getActiveStepId() {
        return activeStepId;
    }
    
    /**
     * @param activeStepId the activeStepId to set
     */
    public void setActiveStepId(long activeStepId) {
        this.activeStepId = activeStepId;
    }
    
    /**
     * 返回待加签人员列表
     *
     * @return 流程实例创建者
     */
    @Schema(title = "流程实例待加签操作人员列表,如果是第三方系统建议和GRCv5进行同步系统用户，目前仅支持操作类型为用户的操作人",
            description = "流程实例待加签操作人员列表,如果是第三方系统建议和GRCv5进行同步系统用户，目前仅支持操作类型为用户的操作人", required = true)
    public OperatorBean[] getOperators() {
        return operators;
    }
    
    /**
     * 设置操作人员列表
     *
     * @param operators 流程实例待处理人员列表（加签人员）
     */
    public void setOperators(OperatorBean[] operators) {
        this.operators = operators;
    }
    
    /**
     * 时限定义，用于加签时设置子流程时限
     *
     * @return
     */
    @Schema(title = "时限定义，用于加签时设置子流程时限，如果是给子流程加签则不需要设置",
            description = "时限定义，用于加签时设置子流程时限，如果是给子流程加签则不需要设置",
            example = "{\r\n" + "    \"dueTimeDefintion\" : {\r\n"
                    + "        \"dateValue\" : 3,\r\n" + "        \"dateUnit\" : \"d\"\r\n" + "    },\r\n"
                    + "    \"alarmTimeDefinition\" : {\r\n" + "        \"dateValue\" : 1,\r\n"
                    + "        \"dateUnit\" : \"d\"\r\n" + "    },\r\n" + "    \"notificationReceivers\": [\r\n" + " "
                    + "     {\r\n"
                    + "        \"type\": \"User\",\r\n" + "        \"code\": \"hd\",\r\n" + "        \"name\": "
                    + "\"慧点人\",\r\n"
                    + "        \"attrs\": null\r\n" + "      }\r\n" + "    ],\r\n"
                    + "    \"messengerBeanIds\":\"hdWorkflowTodolistMessenger,hdCustomWorkflowTodolistMessenger\",\r\n"
                    + "    \"dueNotificationDefinition\" :[],\r\n" + "    \"alarmNotificationDefinition\" : []\r\n" + "  }")
    public TimeConstraintDefinition getTimer() {
        return timer;
    }
    
    /**
     * 时限定义，用于加签时设置子流程时限
     *
     * @param timer
     */
    public void setTimer(TimeConstraintDefinition timer) {
        this.timer = timer;
    }
    
    /**
     * 获取存储表单数据实体bean的扩展属性
     *
     * @return
     */
    @Schema(title = "扩展属性，可以通过扩展属性来标记数据来源， 可以为空",
            description = "扩展属性，可以通过扩展属性来标记数据来源， 可以为空",
            example = "{\"source\":\"mobile\"}")
    public Map<String, String> getProps() {
        return props;
    }
    
    /**
     * 设置存储表单数据实体bean的扩展属性
     *
     * @param props
     */
    public void setProps(Map<String, String> props) {
        this.props = props;
    }
}
