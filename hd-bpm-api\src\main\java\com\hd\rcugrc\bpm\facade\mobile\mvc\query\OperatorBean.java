package com.hd.rcugrc.bpm.facade.mobile.mvc.query;

import java.io.Serializable;
import java.util.Map;

import io.swagger.v3.oas.annotations.media.Schema;


/**
 * 流程参与者
 */
@Schema(title = "operatorBean", description = "流程参与者信息,主用流程驱动时使用,以下example 表示参与者为Id为1000的慧点科技组织")
public class OperatorBean implements Serializable {
    
    /**
     *
     */
    private static final long serialVersionUID = 2603796811644127382L;
    private String type;
    private String code;
    private String name;
    private Map<String, String> attrs;
    
    /**
     * 参与者类型 {@link com.hd.rcugrc.bpm.Operator}
     *
     * @return 参与者类型
     */
    @Schema(required = true,
            description = "操作者类型，用户类型：User \r\n 组织类型：Group \r\n 角色类型：Role \r\n 函数类型：Function \r\n",
            example = "Group")
    public String getType() {
        return type;
    }
    
    public void setType(String type) {
        this.type = type;
    }
    
    /**
     * 返回参与者编码
     *
     * @return 参与者编码
     * <pre>
     * 操作者编码，用户时，使用的是账户 ；
     * 组织时，使用的是组织ID；
     * 角色时，使用的角色编码
     * </pre>
     */
    @Schema(required = true,
            description = "操作者编码，用户时，使用的是账户 \r\n ；组织时，使用的是组织ID；\r\n，角色时，使用的角色编码 \r\n，函数时，使用的函数方法名",
            example = "1000")
    public String getCode() {
        return code;
    }
    
    /**
     * 设置参与者编码
     *
     * @param code 参与者编码
     *             <pre>
     *             操作者编码，用户时，使用的是账户 ；
     *             组织时，使用的是组织ID；
     *             角色时，使用的角色编码
     *             </pre>
     */
    public void setCode(String code) {
        this.code = code;
    }
    
    /**
     * 返回参与者名称
     *
     * @return 参与者名称
     * <pre>
     * 操作者名称,用户时，使用的是姓名
     * 组织时，使用的是组织名称
     * 角色时，使用的角色名称
     * </pre>
     */
    @Schema(required = true,
            description = "操作者名称,用户时，使用的是姓名\r\n ；组织时，使用的是组织名称；\r\n，角色时，使用的角色名称\r\n ，函数时，使用的函数名称 ",
            example = "慧点科技")
    public String getName() {
        return name;
    }
    
    /**
     * 设置参与者名称
     *
     * @param name 参与者名称
     *             <pre>
     *             操作者名称,用户时，使用的是姓名
     *             组织时，使用的是组织名称
     *             角色时，使用的角色名称
     *             </pre>
     */
    public void setName(String name) {
        this.name = name;
    }
    
    /**
     * 返回操作者扩展参数
     *
     * @return 操作者扩展参数
     * <p>
     * 操作者扩展参数，用于补充现有参数不足
     * </p>
     */
    @Schema(title = "操作者扩展参数", required = false,
            description = "操作者扩展参数，用于补充现有参数不足", example = "{}")
    public Map<String, String> getAttrs() {
        return attrs;
    }
    
    /**
     * 设置操作者扩展参数
     *
     * @param attrs 操作者扩展参数
     *              <p>
     *              操作者扩展参数，用于补充现有参数不足
     *              </p>
     */
    public void setAttrs(Map<String, String> attrs) {
        this.attrs = attrs;
    }
    
}
