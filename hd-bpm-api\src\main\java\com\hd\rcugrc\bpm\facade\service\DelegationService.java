/*
 * Copyright 2013-2019 Smartdot Technologies Co., Ltd. All rights reserved.
 * SMARTDOT PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 */
package com.hd.rcugrc.bpm.facade.service;

import com.hd.rcugrc.bpm.TrustInfo;
import com.hd.rcugrc.bpm.facade.EntrustItemInfo;
import com.hd.rcugrc.bpm.list.EntrustedFlowInstanceInfo;
import com.hd.rcugrc.data.crud.criteria.Criterion;

/**
 * <p>
 * 委托服务, 用于查询委托列表, 创建, 编辑, 删除委托
 * 
 * <AUTHOR> href="mailto:<EMAIL>">pandy</a>
 * @version 1.0, 2019年3月7日
 */
public interface DelegationService {

    /**
     * 返回满足指定约束条件的委托设置信息个数
     * 
     * @param criterions 前端查询条件
     * @return 委托项数
     */
    public int getEntrustItemCount(Criterion[] criterions);

    /**
     * 返回满足指定约束条件的委托设置信息列表，用于管理员对委托信息的管理
     * 
     * @param criterions    前端查询条件
     * @param startPosition 返回记录前跳过的记录条数
     * @param maxResults    返回的结果记录条数，如果传入-1则返回所有的查询结果
     * @param orderBy       查询结果的排序设置，格式为\"字段
     *                      排序设置\"，其中排序设置的值为ASC或DESC，如果涉及多个字段，则多段定义间以，分隔
     * @return 委托项数组
     */
    public EntrustItemInfo[] getEntrustItems(Criterion[] criterions, int startPosition, int maxResults, String orderBy);

    /**
     * 创建委托项
     * 
     * @param trustInfo 委托项信息
     * 
     * @return 委托项信息
     */
    public EntrustItemInfo createTrustInfo(TrustInfo trustInfo);

    /**
     * 更新委托项
     * 
     * @param trustInfo 委托项信息
     * 
     * @return 委托项信息
     */
    public EntrustItemInfo updateTrustInfo(TrustInfo trustInfo);

    /**
     * 删除委托项
     * 
     * @param ids 委托项id数组
     * 
     */
    public void removeTrustInfos(long[] ids);

    /**
     * 
     * 返回 所属账户的满足约束条件的已完成的委托流程实例个数
     * 
     * @param ownerAccount 实例的委托者
     * @param criterions   查询约束条件集合
     * @return
     */
    public int getEntrustedItemCount(String ownerAccount, Criterion[] criterions);

    /**
     * 
     * 返回 所属账户的满足约束条件的已完成的委托流程实例个数（包含一人多岗兼职数据）
     * 
     * @param ownerAccount 实例的委托者
     * @param criterions   查询约束条件集合
     * @return
     */
    public int getEntrustedItemCountWithTrustor(String ownerAccount, Criterion[] criterions);

    /**
     * 返回 所属账户的满足约束条件的已完成的委托流程实例列表
     * 
     * @param ownerAccount  实例的委托者
     * @param criterions    查询约束条件集合
     * @param startPosition 返回结果前跳过的记录条数
     * @param maxResults    返回的最大记录条数，如果是-1则返回所有记录
     * @param orderBy       返回结果的排序设置
     * @return
     */
    public EntrustedFlowInstanceInfo[] getEntrustedItems(String ownerAccount, Criterion[] criterions, int startPosition,
            int maxResults, String orderBy);

    /**
     * 返回 所属账户的满足约束条件的已完成的委托流程实例列表（包含一人多岗兼职数据）
     * 
     * @param ownerAccount  实例的委托者
     * @param criterions    查询约束条件集合
     * @param startPosition 返回结果前跳过的记录条数
     * @param maxResults    返回的最大记录条数，如果是-1则返回所有记录
     * @param orderBy       返回结果的排序设置
     * @return
     */
    public EntrustedFlowInstanceInfo[] getEntrustedItemsWithTrustor(String ownerAccount, Criterion[] criterions,
            int startPosition, int maxResults, String orderBy);

}
