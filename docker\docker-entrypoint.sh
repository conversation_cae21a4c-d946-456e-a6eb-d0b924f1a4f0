#!/bin/bash
source /etc/profile
if [ -n "${DEPEND_SERVERS}" ] ; then
    list=(${DEPEND_SERVERS//,/ })
    if [ "$DEPEND_SERVER_TIMEOUT" = "" ] ; then
      DEPEND_SERVER_TIMEOUT="30"
    fi
    
    for server in ${list[@]}
    do
      wait-for-it.sh $server -t ${DEPEND_SERVER_TIMEOUT}
      (($? != 0)) && { printf 'server %s is unavailable!\n' "${server}"; exit $?; }
    done
fi

LOCALCP=/opt/app
if [ -n "$CLASSPATH" ] ; then
  LOCALCP=$CLASSPATH:$LOCALCP
  CLASSPATH=""
fi

JAVA_OPTS=
if [ -n "${JAVA_MEM_OPTS}" ] ; then 
  JAVA_OPTS="${JAVA_OPTS} ${JAVA_MEM_OPTS}"
fi
if [ -n "${JAVA_EXTRA_OPTS}" ] ; then 
  JAVA_OPTS="${JAVA_OPTS} ${JAVA_EXTRA_OPTS}"
fi
if [ -n "${ACTIVE_PROFILE}" ] ; then 
  JAVA_OPTS="${JAVA_OPTS} -Dspring.profiles.active=${ACTIVE_PROFILE}"
fi

if [ -z "${LOG_OPTS}" ] ; then
  LOG_OPTS="-Dlogging.file.name=${LOG_FILE} -Dlogging.file.path=${LOG_PATH}"
fi

exec java ${JAVA_OPTS} ${LOG_OPTS} -cp ${LOCALCP} ${MAIN_CLASS}