/*
 * Copyright 2013-2021 Smartdot Technologies Co., Ltd. All rights reserved.
 * SMARTDOT PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 */
package com.hd.rcugrc.bpm.facade.exception;

/**
 * <p>
 * 
 * <AUTHOR> href="mailto:<EMAIL>">pandy</a>
 * @version 1.0, 2021年3月10日
 */
public class FacadeException  extends Exception {

    /**
     * 
     */
    private static final long serialVersionUID = -8870625295387806840L;
    
    private int code = -1;
    
    public FacadeException() {
        super();
    }

    public FacadeException(int code, String message) {
        super(message);
        this.code = code;
    }
    
    public FacadeException(int code, String message, Throwable cause) {
        super(message, cause);
        this.code = code;
    }
    
    public FacadeException(String message, Throwable cause) {
        super(message, cause);
    }

    public FacadeException(String message) {
        super(message);
    }

    public FacadeException(Throwable cause) {
        super(cause);
    }

    /**
     * @return the code
     */
    public int getCode() {
        return code;
    }

    /**
     * @param code the code to set
     */
    public void setCode(int code) {
        this.code = code;
    }
}

