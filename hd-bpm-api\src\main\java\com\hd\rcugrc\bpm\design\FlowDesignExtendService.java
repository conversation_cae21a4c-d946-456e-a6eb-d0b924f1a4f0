/*
 * Copyright 2013-2020 Smartdot Technologies Co., Ltd. All rights reserved.
 * SMARTDOT PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 */
package com.hd.rcugrc.bpm.design;

import com.hd.rcugrc.bpm.tool.FormInfoTreeNode;
import com.hd.rcugrc.bpm.tool.ZtreeNodeForToolCategory;
import com.hd.rcugrc.web.dwr.TreeNode;

/**
 * <p>
 * 
 * <AUTHOR> href="mailto:<EMAIL>">pandy</a>
 * @version 1.0, 2020年7月22日
 */
public interface FlowDesignExtendService {

    /**
     * 获取页面树节点扩展 
     * @param groupId 在不分级的情况下为-1 在分级的情况下，是当前分级的机构id，当前分级机构的可管理几点可通过
     *      HierarchyAuth[] nodesAuth_oper = hierarchyAuthService.findHierarchyAuths(groupId, ResourceType.PAGE_2);
            HierarchyAuth[] nodesAuth_flow = hierarchyAuthService.findHierarchyAuths(groupId, ResourceType.PAGE_3);
            {@link com.hd.rcugrc.security.hierarchy.service.HierarchyAuthService}
     * @param parentCatId
     * @return
     */
    public TreeNode[] getPageChildrenWithHierarchyAuth(String groupId, String parentCatId, Boolean isFlow);
    
    /**
     * 
     * @param groupId <br>
     * 在不分级的情况下为-1 在分级的情况下，是当前分级的机构id，当前分级机构的可管理几点可通过<br>
     *      HierarchyAuth[] nodesAuth_oper = hierarchyAuthService.findHierarchyAuths(groupId, ResourceType.PAGE_2);<br>
            HierarchyAuth[] nodesAuth_flow = hierarchyAuthService.findHierarchyAuths(groupId, ResourceType.PAGE_3);<br>
            {@link com.hd.rcugrc.security.hierarchy.service.HierarchyAuthService}<br>
     * @param useParent <br>
     * 当分级时，如果当前没有找到分级节点，则使用上级分级几点<br>
     * if (useParent) {<br>
            nodesAuth_oper = hierarchyAuthService.findHierarchyAuthIsNullUseParentGroup(groupId, ResourceType.PAGE_2);
            <br>
            nodesAuth_flow = hierarchyAuthService.findHierarchyAuthIsNullUseParentGroup(groupId, ResourceType.PAGE_3);
            <br>
        } else {<br>
            nodesAuth_oper = hierarchyAuthService.findHierarchyAuths(groupId, ResourceType.PAGE_2);<br>
            nodesAuth_flow = hierarchyAuthService.findHierarchyAuths(groupId, ResourceType.PAGE_3);<br>
        }<br>
     * @return <br>
     * ZtreeNodeForToolCategory rootNode = new ZtreeNodeForToolCategory();<br>
            rootNode.setId("C-3");<br>
            rootNode.setIsParent("true");<br>
            rootNode.setName("页面集合");<br>
     */
    public ZtreeNodeForToolCategory[] getPageTreeRootNodes(String groupId, boolean useParent);
    
    /**
     * 
     * @param groupId
     * @return
     */
    public FormInfoTreeNode[] getFormChildrenHirerarchy(String groupId);

    /**
     * 根据查询参数查询所有符合条件页面的全路径
     *
     * @param groupId 分级机构id
     * @param param 查询参数
     * @return 页面全路径数组，一个页面的全路径作为数组的一个元素
     */
    public default String[] searchPageByParam(String groupId, String param) {
        return new String[0];
    }
}

