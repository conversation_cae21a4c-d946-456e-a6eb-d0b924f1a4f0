/*
 * Copyright 2013-2020 Smartdot Technologies Co., Ltd. All rights reserved.
 * SMARTDOT PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 */
package com.hd.rcugrc.bpm.extra.task.service;

import java.io.Serializable;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <p>
 * 催办人员信息
 * <AUTHOR> href="mailto:<EMAIL>">songjj</a>
 * @version 5.4, 2020年7月28日
 */
@Schema(title = "催办接收人信息", description = "催办接收人信息")
public class ReminderUser implements Serializable {
    
    private static final long serialVersionUID = -1261394792640700217L;
    private long  todoId;
    private String account;
    private String name;
    
    public ReminderUser() {
        super();
    }
            
    public ReminderUser(long todoId, String account, String name) {
        super();
        this.todoId = todoId;
        this.account = account;
        this.name = name;
    }
    /**
     * 获取待办ID
     * @return 待办ID
     */
    @Schema(title = "待办ID", description = "待办ID", required = true, example = "1")
    public long getTodoId() {
        return todoId;
    }
    /**
     * 设置待办ID
     * @param todoId 待办ID
     */
    public void setTodoId(long todoId) {
        this.todoId = todoId;
    }
    /**
     * 获取账户
     * @return  账户
     */
    @Schema(title = "账户", description = "账户", required = true, example = "hd")
    public String getAccount() {
        return account;
    }
    /**
     * 设置账户
     * @param account 账户
     */
    public void setAccount(String account) {
        this.account = account;
    }
    /**
     * 获取用户名
     * @return  用户名
     */
    @Schema(title = "姓名", description = "姓名", required = true, example = "慧点人员")
    public String getName() {
        return name;
    }
    /**
     * 设置用户名
     * @param name 用户名
     */
    public void setName(String name) {
        this.name = name;
    }
    
    
}

