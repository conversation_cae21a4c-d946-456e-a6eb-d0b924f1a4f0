/*
 * Copyright 2013-2019 Smartdot Technologies Co., Ltd. All rights reserved.
 * SMARTDOT PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 */
package com.hd.rcugrc.bpm.facade.form.service;

import java.util.Map;

import com.hd.rcugrc.bpm.facade.form.TaskPageDetail;

/**
 * <p>
 * 待办详情页面信息填充服务 主要用于意见、附件填充，如项目需要扩展则直接继承该类
 * 
 * <AUTHOR> href="mailto:<EMAIL>">songjj</a>
 * @version 5.4, 2019年7月17日
 */
public interface ProcessFormDataPopulatorService extends ProcessFormDataExtendPopulatorService {

    /**
     * 追加意见、附件等信息
     * 
     * @param processId
     *            流程定义ID
     * @param instId
     *            流程实例ID
     * @param activeStepId
     *            活动环节Id
     * @param mode
     *            模式
     * @param taskPageDetail
     *            表单页面信息
     * @param context
     *            上下文
     */
    public void appendFormData(String processId, long instId, long activeStepId, String mode,
            TaskPageDetail taskPageDetail, Map<String, Object> context);
}
