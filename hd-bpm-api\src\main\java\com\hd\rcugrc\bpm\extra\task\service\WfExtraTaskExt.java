/** 
 * @Company: 北京慧点科技有限公司 | www.smartdot.com.cn
 * @Copyright 1998-2020 © Smartdot Technologies Co., Ltd. 
 */
package com.hd.rcugrc.bpm.extra.task.service;

import java.io.Serializable;

/**
 * 流程辅助任务信息-实体对象
 * 
 * @class: HdWfExtraTaskExt
 * @date 2020-07-27 10:09:21
 * <AUTHOR>
 * @version 1.0
 * @see
 */

public class WfExtraTaskExt implements Serializable {

    private static final long serialVersionUID = 2163559278963897227L;

    // ID
    private long id;

    // 扩展内容
    private String content;

    /**
     * 获取ID
     * 
     * @return ID
     */
    public long getId() {
        return id;
    }

    /**
     * 设置ID
     * 
     * @param id ID
     */
    public void setId(long id) {
        this.id = id;
    }

    /**
     * 获取扩展内容
     * 
     * @return 扩展内容
     */
    public String getContent() {
        return content;
    }

    /**
     * 设置扩展内容
     * 
     * @param content 扩展内容
     */
    public void setContent(String content) {
        this.content = content;
    }

}
