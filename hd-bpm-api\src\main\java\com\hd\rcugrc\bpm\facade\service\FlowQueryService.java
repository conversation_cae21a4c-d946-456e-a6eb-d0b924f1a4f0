/*
 * Copyright 2013-2019 Smartdot Technologies Co., Ltd. All rights reserved.
 * SMARTDOT PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 */
package com.hd.rcugrc.bpm.facade.service;

import com.hd.rcugrc.web.dwr.ListRange;
import com.hd.rcugrc.web.dwr.criterion.Expression;

/**
 * <p>
 * 流程定义查询 可用于查询在流程建模工具中定义的流程
 * 
 * <AUTHOR> href="mailto:<EMAIL>">pandy</a>
 * @version 1.0, 2019年3月7日
 */
public interface FlowQueryService {

    /**
     * 查询在流程建模工具中定义的流程
     * @param isEnabled 是否启用的流程
     * @param expressions 前端查询条件 
     * @param startPosition
     * @param maxResults
     * @param orderBy
     * @return ListRange 分页列表数据
     */
    public ListRange listFlow(boolean isEnabled, Expression[] expressions, int startPosition, int maxResults,
            String orderBy);
}
