/*
 * Copyright 2013-2019 Smartdot Technologies Co., Ltd. All rights reserved.
 * SMARTDOT PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 */
package com.hd.rcugrc.bpm.facade.form.service;

import java.util.Map;

/**
 * <p>
 * 待办详情页面信息填充服务 主要用于意见、附件填充，如项目需要扩展则直接继承该类
 * 
 * <AUTHOR> href="mailto:<EMAIL>">pandy</a>
 * @version 5.4, 2019年7月17日
 */
public interface ProcessFormDataExtendPopulatorService {

    /**
     * 
     * @param instId 流程实例id
     * @param activeStepId 当前活动环节id
     * @param mode 页面打开模式 {@link com.hd.rcugrc.bpm.facade.form.util.ProcessFormFieldUtil}
     * @param context 上下文
     * @param attrs 需要填充的扩展属性
     */
    public void appendFormAttrs(long instId, long activeStepId, String mode, 
            Map<String, Object> context, Map<String, Object> attrs);
}
