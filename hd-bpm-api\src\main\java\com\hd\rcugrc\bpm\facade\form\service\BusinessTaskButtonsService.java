/*
 * Copyright 2013-2020 Smartdot Technologies Co., Ltd. All rights reserved.
 * SMARTDOT PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 */
package com.hd.rcugrc.bpm.facade.form.service;

import java.util.Map;

import com.hd.rcugrc.bpm.User;
import com.hd.rcugrc.bpm.facade.exception.TaskProcessException;
import com.hd.rcugrc.bpm.facade.form.TaskButton;

/**
 * <p>
 * 
 * <AUTHOR> href="mailto:<EMAIL>">pandy</a>
 * @version 1.0, 2020年7月15日
 */
public interface BusinessTaskButtonsService {

    /**
     * 
     * @param pageId
     * @param instId
     * @param activeStepId
     * @param user
     * @param mode {@link com.hd.rcugrc.bpm.facade.form.util.ProcessFormFieldUtil}
     * @param context 流程上下文
     * @return
     */
    public TaskButton[] getBusinessTaskButtons(String pageId, long instId, long activeStepId, User user, String mode,
            Map<String, Object> context) throws TaskProcessException;
}

