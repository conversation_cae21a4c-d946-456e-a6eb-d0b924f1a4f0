/** 
 * @Company: 北京慧点科技有限公司 | www.smartdot.com.cn
 * @Copyright 1998-2020 © Smartdot Technologies Co., Ltd. 
 */
package com.hd.rcugrc.bpm.extra.task.service;

import java.io.Serializable;
import java.util.Map;

import org.joda.time.DateTime;

/**
 * 流程辅助任务信息-实体对象
 * 
 * @class: WfExtraTaskList
 * @date 2020-07-27 09:51:59
 * <AUTHOR>
 * @version 1.0
 * @see
 */

public class WfExtraTaskList implements Serializable {

    private static final long serialVersionUID = 2016203380335141848L;

    // ID
    private long id;

    // 标题
    private String title;

    // 任务链接
    private String taskLink;

    // 待办ID
    private long todoId;

    // 类型： 1 催办
    private int taskType = 1;

    // 流程实例ID
    private long instId;

    // 步骤ID
    private String stepId;

    // 步骤号
    private int stepSn;

    //
    private String senderAccount;

    //
    private String senderName;

    // 扩展字符串字段1
    private String reservestring1;

    // 扩展字符串字段2
    private String reservestring2;

    // 扩展字符串字段3
    private String reservestring3;

    // 扩展字符串字段4
    private String reservestring4;

    // 扩展字符串字段5
    private String reservestring5;

    // 扩展长整形字段1
    private long reservelong1;

    // 扩展长整形字段2
    private long reservelong2;

    // 扩展长整形字段3
    private long reservelong3;

    // 扩展长整形字段4
    private long reservelong4;

    // 扩展长整形字段5
    private long reservelong5;

    // 发送日期
    private DateTime sentDate;

    // 扩展属性列表
    private Map<String, String> props;
    
    //任务接收人
    private WfExtraTaskUser[]receiverUsers;
    
    // 步骤名称
    private String stepName;
    
    
    /**
     * 获取ID
     * 
     * @return ID
     */
    public long getId() {
        return id;
    }

    /**
     * 设置ID
     * 
     * @param id ID
     */
    public void setId(long id) {
        this.id = id;
    }

    /**
     * 获取标题
     * 
     * @return 标题
     */
    public String getTitle() {
        return title;
    }

    /**
     * 设置标题
     * 
     * @param title 标题
     */
    public void setTitle(String title) {
        this.title = title;
    }

    /**
     * 获取任务链接
     * 
     * @return 任务链接
     */
    public String getTaskLink() {
        return taskLink;
    }

    /**
     * 设置任务链接
     * 
     * @param taskLink 任务链接
     */
    public void setTaskLink(String taskLink) {
        this.taskLink = taskLink;
    }

    /**
     * 获取待办ID
     * 
     * @return 待办ID
     */
    public long getTodoId() {
        return todoId;
    }

    /**
     * 设置待办ID
     * 
     * @param todoId 待办ID
     */
    public void setTodoId(long todoId) {
        this.todoId = todoId;
    }

    /**
     * 获取类型： 1 催办
     * 
     * @return 类型： 1 催办
     */
    public int getTaskType() {
        return taskType;
    }

    /**
     * 设置类型： 1 催办
     * 
     * @param type 类型： 1 催办
     */
    public void setTaskType(int taskType) {
        this.taskType = taskType;
    }

    /**
     * 获取流程实例ID
     * 
     * @return 流程实例ID
     */
    public long getInstId() {
        return instId;
    }

    /**
     * 设置流程实例ID
     * 
     * @param instId 流程实例ID
     */
    public void setInstId(long instId) {
        this.instId = instId;
    }

    /**
     * 获取步骤ID
     * 
     * @return 步骤ID
     */
    public String getStepId() {
        return stepId;
    }

    /**
     * 设置步骤ID
     * 
     * @param stepId 步骤ID
     */
    public void setStepId(String stepId) {
        this.stepId = stepId;
    }

    /**
     * 获取步骤号
     * 
     * @return 步骤号
     */
    public int getStepSn() {
        return stepSn;
    }

    /**
     * 设置步骤号
     * 
     * @param stepSn 步骤号
     */
    public void setStepSn(int stepSn) {
        this.stepSn = stepSn;
    }

    /**
     * 获取
     * 
     * @return
     */
    public String getSenderAccount() {
        return senderAccount;
    }

    /**
     * 设置
     * 
     * @param senderAccount
     */
    public void setSenderAccount(String senderAccount) {
        this.senderAccount = senderAccount;
    }

    /**
     * 获取
     * 
     * @return
     */
    public String getSenderName() {
        return senderName;
    }

    /**
     * 设置
     * 
     * @param senderName
     */
    public void setSenderName(String senderName) {
        this.senderName = senderName;
    }

    /**
     * 获取扩展字符串字段1
     * 
     * @return 扩展字符串字段1
     */
    public String getReservestring1() {
        return reservestring1;
    }

    /**
     * 设置扩展字符串字段1
     * 
     * @param reservestring1 扩展字符串字段1
     */
    public void setReservestring1(String reservestring1) {
        this.reservestring1 = reservestring1;
    }

    /**
     * 获取扩展字符串字段2
     * 
     * @return 扩展字符串字段2
     */
    public String getReservestring2() {
        return reservestring2;
    }

    /**
     * 设置扩展字符串字段2
     * 
     * @param reservestring2 扩展字符串字段2
     */
    public void setReservestring2(String reservestring2) {
        this.reservestring2 = reservestring2;
    }

    /**
     * 获取扩展字符串字段3
     * 
     * @return 扩展字符串字段3
     */
    public String getReservestring3() {
        return reservestring3;
    }

    /**
     * 设置扩展字符串字段3
     * 
     * @param reservestring3 扩展字符串字段3
     */
    public void setReservestring3(String reservestring3) {
        this.reservestring3 = reservestring3;
    }

    /**
     * 获取扩展字符串字段4
     * 
     * @return 扩展字符串字段4
     */
    public String getReservestring4() {
        return reservestring4;
    }

    /**
     * 设置扩展字符串字段4
     * 
     * @param reservestring4 扩展字符串字段4
     */
    public void setReservestring4(String reservestring4) {
        this.reservestring4 = reservestring4;
    }

    /**
     * 获取扩展字符串字段5
     * 
     * @return 扩展字符串字段5
     */
    public String getReservestring5() {
        return reservestring5;
    }

    /**
     * 设置扩展字符串字段5
     * 
     * @param reservestring5 扩展字符串字段5
     */
    public void setReservestring5(String reservestring5) {
        this.reservestring5 = reservestring5;
    }

    /**
     * 获取扩展长整形字段1
     * 
     * @return 扩展长整形字段1
     */
    public long getReservelong1() {
        return reservelong1;
    }

    /**
     * 设置扩展长整形字段1
     * 
     * @param reservelong1 扩展长整形字段1
     */
    public void setReservelong1(long reservelong1) {
        this.reservelong1 = reservelong1;
    }

    /**
     * 获取扩展长整形字段2
     * 
     * @return 扩展长整形字段2
     */
    public long getReservelong2() {
        return reservelong2;
    }

    /**
     * 设置扩展长整形字段2
     * 
     * @param reservelong2 扩展长整形字段2
     */
    public void setReservelong2(long reservelong2) {
        this.reservelong2 = reservelong2;
    }

    /**
     * 获取扩展长整形字段3
     * 
     * @return 扩展长整形字段3
     */
    public long getReservelong3() {
        return reservelong3;
    }

    /**
     * 设置扩展长整形字段3
     * 
     * @param reservelong3 扩展长整形字段3
     */
    public void setReservelong3(long reservelong3) {
        this.reservelong3 = reservelong3;
    }

    /**
     * 获取扩展长整形字段4
     * 
     * @return 扩展长整形字段4
     */
    public long getReservelong4() {
        return reservelong4;
    }

    /**
     * 设置扩展长整形字段4
     * 
     * @param reservelong4 扩展长整形字段4
     */
    public void setReservelong4(long reservelong4) {
        this.reservelong4 = reservelong4;
    }

    /**
     * 获取扩展长整形字段5
     * 
     * @return 扩展长整形字段5
     */
    public long getReservelong5() {
        return reservelong5;
    }

    /**
     * 设置扩展长整形字段5
     * 
     * @param reservelong5 扩展长整形字段5
     */
    public void setReservelong5(long reservelong5) {
        this.reservelong5 = reservelong5;
    }

    /**
     * 获取发送日期
     * 
     * @return 发送日期
     */
    public DateTime getSentDate() {
        return sentDate;
    }

    /**
     * 设置发送日期
     * 
     * @param sentDate 发送日期
     */
    public void setSentDate(DateTime sentDate) {
        this.sentDate = sentDate;
    }
    /**
     * 获取扩展属性以及属性值列表
     * @return 扩展属性以及属性值列表
     */
    public Map<String, String> getProps() {
        return props;
    }

    /**
     * 设置扩展属性以及属性值列表
     * @param props 扩展属性以及属性值列表
     */
    public void setProps(Map<String, String> props) {
        this.props = props;
    }

    /**
     *任务接收人列表
     * @return  任务接收人列表
     */
    public WfExtraTaskUser[] getReceiverUsers() {
        return receiverUsers;
    }

    /**
     * 设置任务接收人列表
     * @param wfExtraTaskUser 任务接收人列表 
     */
    public void setReceiverUsers(WfExtraTaskUser[] receiverUsers) {
        this.receiverUsers = receiverUsers;
    }

    /**
     * 步骤名称
     * @return  步骤名称
     */
    public String getStepName() {
        return stepName;
    }

    /**
     * 设置步骤名称
     * @param stepName 步骤名称
     */
    public void setStepName(String stepName) {
        this.stepName = stepName;
    }

}
