/*
 * Copyright 2013-2020 Smartdot Technologies Co., Ltd. All rights reserved.
 * SMARTDOT PROPRIETARY/CONFIDENTIAL. Use is subject to license
terms.
 *技术文档
版权所有 © 北京慧点科技有限公司 第 9 页 共 45 页
 */
package com.hd.rcugrc.bpm.facade.service;

/**
 * 扫描含义formBean定义的class
 * 
 * <AUTHOR>
 * @since 1.0, 2022年8月19日
 */
public interface ScanningEntityService {
    
    /**
     * 根据实体类的formId获取实体类
     *
     * @param formId
     * @return
     */
    public Class<?> getFormBean(String formId);
}
