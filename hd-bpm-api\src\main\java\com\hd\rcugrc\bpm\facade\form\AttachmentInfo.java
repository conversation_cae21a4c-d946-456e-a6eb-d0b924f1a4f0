/*
 * Copyright 2013-2019 Smartdot Technologies Co., Ltd. All rights reserved.
 * SMARTDOT PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 */
package com.hd.rcugrc.bpm.facade.form;

import java.io.Serializable;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <p>
 * 附件信息,用于rest接口页面展示附件使用
 * 
 * <AUTHOR> href="mailto:<EMAIL>">songjj</a>
 * @version 5.4, 2019年7月17日
 */
@Schema(title = "附件信息", description = "附件信息")
public class AttachmentInfo implements Serializable {
    
    private static final long serialVersionUID = 8187505949320615776L;


    private String name;

    private String code;

    private String groupCode;

    private String size;
    
    private long fileSize;
    
    private long creationTime;
    
    private long lastModifiedTime;
    
    private String catNum;
    
    /**
     * @return 附件名称
     */
    @Schema(title = "附件名称", description = "附件名称", example = "测试名称.txt")
    public String getName() {
        return name;
    }

    /**
     * 设置附件名称
     * 
     * @param name 附件名称
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * @return 附件编码
     */
    @Schema(title = "附件编码,获取具体File内容使用", description = "附件编码,获取具体File内容使用", example = "1")
    public String getCode() {
        return code;
    }

    /**
     * @param code 设置编码
     */
    public void setCode(String code) {
        this.code = code;
    }

    /**
     * @return 附件组
     */
    @Schema(title = "附件组,一个附件组下面可以挂多个附件", description = "附件组,一个附件组下面可以挂多个附件", example = "HFuesSNIdhqsjDw")
    public String getGroupCode() {
        return groupCode;
    }

    /**
     * 设置 附件组
     * 
     * @param groupCode 附件组
     */
    public void setGroupCode(String groupCode) {
        this.groupCode = groupCode;
    }

    /**
     * @return 附件大小
     */
    @Schema(title = "附件大小", description = "附件大小", example = "1k")
    public String getSize() {
        return size;
    }

    /**
     * @param size 附件大小
     */
    public void setSize(String size) {
        this.size = size;
    }

    /**
     * @return 附件大小
     */
    @Schema(title = "附件大小,数值型", description = "附件大小,数值型", example = "1024")
    public long getFileSize() {
        return fileSize;
    }

    /**
     * @param fileSize 附件大小
     */
    public void setFileSize(long fileSize) {
        this.fileSize = fileSize;
    }

    public long getCreationTime() {
        return creationTime;
    }

    public void setCreationTime(long creationTime) {
        this.creationTime = creationTime;
    }

    public long getLastModifiedTime() {
        return lastModifiedTime;
    }

    public void setLastModifiedTime(long lastModifiedTime) {
        this.lastModifiedTime = lastModifiedTime;
    }
    
    public String getCatNum() {
        return catNum;
    }
    
    public void setCatNum(String catNum) {
        this.catNum = catNum;
    }
}
