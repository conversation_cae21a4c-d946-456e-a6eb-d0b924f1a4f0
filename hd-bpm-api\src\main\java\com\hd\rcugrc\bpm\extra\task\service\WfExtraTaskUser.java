/** 
 * @Company: 北京慧点科技有限公司 | www.smartdot.com.cn
 * @Copyright 1998-2020 © Smartdot Technologies Co., Ltd. 
 */
package com.hd.rcugrc.bpm.extra.task.service;


import java.io.Serializable;

import org.joda.time.DateTime;
/**
 * 流程辅助任务接收人信息-实体对象
 * @class: WfExtraTaskUser 
 * @date 2020-07-27 10:19:43 
 * <AUTHOR>
 * @version 1.0
 * @see
 */

public class WfExtraTaskUser implements Serializable {

   
    private static final long serialVersionUID = -9082804562953686188L;

    //ID
    private Long id;
   
       //辅助任务ID
    private long extraTaskId;
   
       //接收人账户
    private String receiverAccount;
   
       //接收人姓名
    private String receiverName;
    
    private int state;
    
    private DateTime seenDate;
    
    private DateTime lastModifiedTime;
    

    /**
     * 获取ID
     * @return  ID
     * */    
    public Long getId() {       
        return id;
    }

    /**
     * 设置ID
     * @param  id ID
     */
    public void setId(Long id) {
        this.id = id;
    }


    /**
     * 获取辅助任务ID
     * @return  辅助任务ID
     * */    
    public long getExtraTaskId() {       
        return extraTaskId;
    }

    /**
     * 设置辅助任务ID
     * @param  extraTaskId 辅助任务ID
     */
    public void setExtraTaskId(long extraTaskId) {
        this.extraTaskId = extraTaskId;
    }


    /**
     * 获取接收人账户
     * @return  接收人账户
     * */    
    public String getReceiverAccount() {       
        return receiverAccount;
    }

    /**
     * 设置接收人账户
     * @param  receiverAccount 接收人账户
     */
    public void setReceiverAccount(String receiverAccount) {
        this.receiverAccount = receiverAccount;
    }


    /**
     * 获取接收人姓名
     * @return  接收人姓名
     * */    
    public String getReceiverName() {       
        return receiverName;
    }

    /**
     * 设置接收人姓名
     * @param  receiverName 接收人姓名
     */
    public void setReceiverName(String receiverName) {
        this.receiverName = receiverName;
    }

    /**
     * 获取状态 0 未查看 ，1 已查看 ，2 已处理
     * 
     * @return 状态 0 未查看 ，1 已查看 ，2 已处理
     */
    public int getState() {
        return state;
    }

    /**
     * 设置状态 0 未查看 ，1 已查看 ，2 已处理
     * 
     * @param state 状态 0 未查看 ，1 已查看 ，2 已处理
     */
    public void setState(int state) {
        this.state = state;
    }
    
    /**
     * 获取查看日期
     * 
     * @return 查看日期
     */
    public DateTime getSeenDate() {
        return seenDate;
    }

    /**
     * 设置查看日期
     * 
     * @param seenDate 查看日期
     */
    public void setSeenDate(DateTime seenDate) {
        this.seenDate = seenDate;
    }

    /**
     * 获取修改日期
     * 
     * @return 修改日期
     */
    public DateTime getLastModifiedTime() {
        return lastModifiedTime;
    }

    /**
     * 设置修改日期
     * 
     * @param seenDate 修改日期
     */
    public void setLastModifiedTime(DateTime lastModifiedTime) {
        this.lastModifiedTime = lastModifiedTime;
    }
      
}
