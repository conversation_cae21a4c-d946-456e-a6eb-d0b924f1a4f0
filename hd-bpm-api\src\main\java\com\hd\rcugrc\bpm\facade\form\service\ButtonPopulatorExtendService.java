/*
 * Copyright 2013-2021 Smartdot Technologies Co., Ltd. All rights reserved.
 * SMARTDOT PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 */
package com.hd.rcugrc.bpm.facade.form.service;

import com.hd.rcugrc.bpm.FlowDefinition;
import com.hd.rcugrc.bpm.FlowInstance;
import com.hd.rcugrc.bpm.StepDefinition;
import com.hd.rcugrc.bpm.facade.form.TaskButton;

/**
 * <p>
 * 对页面详情返回的按钮进行加工
 * 
 * <AUTHOR> href="mailto:<EMAIL>">pandy</a>
 * @version 1.0, 2021年6月9日
 */
public interface ButtonPopulatorExtendService {

    /**
     * 对页面详情返回的保存按钮进行加工
     * 
     * @param flowDefinition
     * @param stepDefinition
     * @param flowInstance
     * @param saveButtons
     * @return
     */
    public TaskButton[] saveButtonPopulator(FlowDefinition flowDefinition, StepDefinition stepDefinition,
            FlowInstance flowInstance, TaskButton[] saveButtons);

    /**
     * 对页面详情返回的提交按钮进行加工
     * 
     * @param flowDefinition
     * @param stepDefinition
     * @param flowInstance
     * @param pushButtons
     * @return
     */
    public TaskButton[] pushButtonPopulator(FlowDefinition flowDefinition, StepDefinition stepDefinition,
            FlowInstance flowInstance, TaskButton[] pushButtons);
}
