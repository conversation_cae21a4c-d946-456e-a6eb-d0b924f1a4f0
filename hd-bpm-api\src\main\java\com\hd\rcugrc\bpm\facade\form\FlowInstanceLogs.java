/*
 * Copyright 2013-2019 Smartdot Technologies Co., Ltd. All rights reserved.
 * SMARTDOT PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 */
package com.hd.rcugrc.bpm.facade.form;

import java.io.Serializable;
import java.util.Map;

import com.hd.rcugrc.bpm.facade.HdProcessLogInfo;
import com.hd.rcugrc.bpm.facade.InformLogInfo;
import com.hd.rcugrc.bpm.facade.ProcessActiveLogInfo;
import com.hd.rcugrc.bpm.facade.ProcessLogInfo;
import com.hd.rcugrc.bpm.facade.ReminderLogInfo;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <p>
 * 流程实例活动环节页面详情信息
 * 
 * <AUTHOR> href="mailto:<EMAIL>">pandy</a>
 * @version 5.5, 2022年12月29日
 */
@Schema(title = "流程实例活动环节页面详情信息", description = "流程实例活动环节页面详情信息")
public class FlowInstanceLogs implements Serializable {

    /**
     * 
     */
    private static final long serialVersionUID = -7263015100304959580L;

    private HdProcessLogInfo[] processLogInfo;

    private ProcessLogInfo[] communicationLogInfo;
    
    private InformLogInfo[] informLogInfo;
    
    private ReminderLogInfo[] reminderLogInfo;
    
    private ProcessActiveLogInfo[] processActiveLogInfo;

    private Map<String, Object> attrs;

    /**
     * 返回流转日志列表
     * 
     * @return 流转日志列表
     */
    @Schema(title = "流转日志列表", description = "流转日志列表")
    public HdProcessLogInfo[] getProcessLogInfo() {
        return processLogInfo;
    }

    /**
     * 设置流转日志列表
     * 
     * @param processLogInfo 流转日志列表
     */
    public void setProcessLogInfo(HdProcessLogInfo[] processLogInfo) {
        this.processLogInfo = processLogInfo;
    }

    /**
     * 返回沟通记录列表
     * 
     * @return 沟通记录列表
     */
    @Schema(title = "沟通记录列表", description = "沟通记录列表")
    public ProcessLogInfo[] getCommunicationLogInfo() {
        return communicationLogInfo;
    }

    /**
     * 设置沟通记录
     * 
     * @param communicationLogInfo 沟通记录列表
     */
    public void setCommunicationLogInfo(ProcessLogInfo[] communicationLogInfo) {
        this.communicationLogInfo = communicationLogInfo;
    }

    /**
     * 返回当前流程实例正在活动的环节列表信息
     * 
     * @return 当前流程实例正在活动的环节列表信息
     */
    @Schema(title = "当前流程实例正在活动的环节列表信息", description = "当前流程实例正在活动的环节列表信息")
    public ProcessActiveLogInfo[] getProcessActiveLogInfo() {
        return processActiveLogInfo;
    }

    /**
     * 设置当前流程实例正在活动的环节列表信息
     * 
     * @param processActiveLogInfo 设置当前流程实例正在活动的环节列表信息
     */
    public void setProcessActiveLogInfo(ProcessActiveLogInfo[] processActiveLogInfo) {
        this.processActiveLogInfo = processActiveLogInfo;
    }

    /**
     * 获取知会记录信息
     * 
     * @return informLogInfo 知会记录
     */
    @Schema(title = "知会记录", description = "知会记录")
    public InformLogInfo[] getInformLogInfo() {
        return informLogInfo;
    }
    /**
     * 设置知会记录
     * 
     * @param informLogInfo 知会记录
     */
    public void setInformLogInfo(InformLogInfo[] informLogInfo) {
        this.informLogInfo = informLogInfo;
    }

    /**
     * 催办提醒日志
     * @return 催办提醒日志
     */
    public ReminderLogInfo[] getReminderLogInfo() {
        return reminderLogInfo;
    }

    /**
     * 设置催办提醒日志
     * @param reminderLogInfo 催办提醒日志
     */
    public void setReminderLogInfo(ReminderLogInfo[] reminderLogInfo) {
        this.reminderLogInfo = reminderLogInfo;
    }

    /**
     * @return 扩展属性
     */
    public Map<String, Object> getAttrs() {
        return attrs;
    }

    /**
     * 设置扩展属性
     * 
     * @param attrs 扩展属性
     */
    public void setAttrs(Map<String, Object> attrs) {
        this.attrs = attrs;
    }
}
