/*
 * Copyright 2013-2019 Smartdot Technologies Co., Ltd. All rights reserved.
 * SMARTDOT PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 */
package com.hd.rcugrc.bpm.facade.mobile.mvc.query;

import java.io.Serializable;

import io.swagger.v3.oas.annotations.media.Schema;


/**
 * <p>
 * 流程实例活动环节减签参数，主要用于Restful接口
 *
 * <AUTHOR> href="mailto:<EMAIL>">songjj</a>
 * @version 5.3, 2019年4月10日
 */
@Schema(title = "removeSubflowOperator", description = "流程实例活动多人并行子流程环节减签参数，流程实例多人并行子流程环节减签,"
        + "  formId 表单ID  包含协议前缀 如推荐：非平台表单  tj:testForm；平台表单  model:testForm \r\n" + " 如果是非平台表单，则还需要包含\r\n"
        + " bean 实体bean对象，该bean方式设置方法 有两种  \r\n" + "   方式一：调用此方法时直接通过SaveFlowInstance设置  \r\n"
        + "   方式二：使用接口{@link com.hd.rcugrc.bpm.facade.service.FormHandlerService}实现类中方法 "
        + "handlerFormBeforeFlowStateChange中进行赋值\r\n"
        + " <b>如果bean 为空则将 formParams中的inputs 参数作为bean</b>\r\n" + "  beanTitleProperty 实体bean对象中作为标题字段属性 \r\n"
        + "  beanIdProperty 实体bean对象中作为主键ID字段属性 \r\n"
        + "以下example表示：表单formId为tj:tjbx， 当前用户为hd，将该流程实例中并行处理人列表中增减用户tjUser")
public class RemoveOperatorInfo implements Serializable {
    
    /**
     *
     */
    private static final long serialVersionUID = 4834310105118795311L;
    
    private long activeStepId;
    
    private RemoveOperatorStepInfo[] operatorStepInfos;
    
    
    /**
     * @return the activeStepId
     */
    @Schema(title = "当前环节ID", description = "当前环节ID", required = true, example = "100")
    public long getActiveStepId() {
        return activeStepId;
    }
    
    /**
     * @param activeStepId the activeStepId to set
     */
    public void setActiveStepId(long activeStepId) {
        this.activeStepId = activeStepId;
    }
    
    /**
     * @return the operatorStepInfos
     */
    @Schema(title = "需要删除的执行者信息", description = "需要删除的执行者信息", required = true)
    public RemoveOperatorStepInfo[] getOperatorStepInfos() {
        return operatorStepInfos;
    }
    
    /**
     * @param operatorStepInfos the operatorStepInfos to set
     */
    public void setOperatorStepInfos(RemoveOperatorStepInfo[] operatorStepInfos) {
        this.operatorStepInfos = operatorStepInfos;
    }
}
