/*
 * Copyright 2013-2019 Smartdot Technologies Co., Ltd. All rights reserved.
 * SMARTDOT PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 */
package com.hd.rcugrc.bpm.facade.mobile.mvc.query;

import java.io.Serializable;

import io.swagger.v3.oas.annotations.media.Schema;


/**
 * <p>
 * 执行流程应用操作后的结果，主要用于Restful接口
 *
 * <AUTHOR> href="mailto:<EMAIL>">songjj</a>
 * @version 5.3, 2019年4月10日
 */
@Schema(title = "actionResult", description = "执行流程应用操作后的结果,主用用于接收流程处理后结果信息")
public class ActionResult implements Serializable {
    
    private static final long serialVersionUID = -6249862490403540368L;
    
    private ActiveStepInfo[] activeStepInfo;
    
    private FlowInstanceResult[] flowInstanceResult;
    
    /**
     * 获取当前流程实例活动环节信息,包含当前流程实例ID以及流程实例活动环节ID
     *
     * @return 当前流程活动环节信息
     */
    @Schema(title = "当前流程实例活动环节信息,包含当前流程实例ID以及流程实例活动环节ID", description = "当前流程实例活动环节信息,包含当前流程实例ID以及流程实例活动环节ID")
    public ActiveStepInfo[] getActiveStepInfo() {
        return activeStepInfo;
    }
    
    /**
     * 设置流程实例活动环节信息
     *
     * @param activeStepInfo 流程实例活动环节信息
     */
    public void setActiveStepInfo(ActiveStepInfo[] activeStepInfo) {
        this.activeStepInfo = activeStepInfo;
    }
    
    /**
     * 获取当前流程实例处理后结果信息
     *
     * @return 当前流程实例处理后结果信息
     */
    @Schema(title = "当前流程实例处理后结果信息", description = "当前流程实例处理后结果信息")
    public FlowInstanceResult[] getFlowInstanceResult() {
        return flowInstanceResult;
    }
    
    /**
     * 设置当前流程实例处理后结果信息
     *
     * @param flowInstanceResult 当前流程实例处理后结果信息
     */
    public void setFlowInstanceResult(FlowInstanceResult[] flowInstanceResult) {
        this.flowInstanceResult = flowInstanceResult;
    }
    
}

