/*
 * Copyright 2013-2021 Smartdot Technologies Co., Ltd. All rights reserved.
 * SMARTDOT PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 */
package com.hd.rcugrc.bpm.facade;

import java.io.Serializable;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <p>待办结果集
 * 
 * <AUTHOR> href="mailto:<EMAIL>">yanglx</a>
 * @version 1.0, 2021年9月8日
 */
@Schema(title = "todoResult", description = "流程实例活动环节全部减签后返回的结果集")
public class TodoResult implements Serializable {
    private static final long serialVersionUID = 1811817790056684854L;
    private Object data;
    private boolean success = true;
    
    public TodoResult() {
    }
    
    public TodoResult(Object data, boolean success) {
        super();
        this.data = data;
        this.success = success;
    }
    
    public TodoResult(boolean success) {
        super();
        this.success = success;
    }

    @Schema(description = "返回结果集，例如返回待办ID", required = false, nullable = false, example = "1")
    public Object getData() {
        return data;
    }
    
    public void setData(Object data) {
        this.data = data;
    }
    
    @Schema(description = "流程操作执行结果", required = true, nullable = false, example = "true")
    public boolean isSuccess() {
        return success;
    }
    
    public void setSuccess(boolean success) {
        this.success = success;
    }
}

