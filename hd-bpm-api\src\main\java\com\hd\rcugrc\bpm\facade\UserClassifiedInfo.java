/*
 * Copyright 2013-2020 Smartdot Technologies Co., Ltd. All rights reserved.
 * SMARTDOT PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 */
package com.hd.rcugrc.bpm.facade;

import java.io.Serializable;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <p>
 * 用户密级信息
 * 
 * <AUTHOR> href="mailto:<EMAIL>">shanglh</a>
 * @version 1.0, 2020年8月3日
 */

@Schema(title = "用户密级信息", description = "用于创建编辑用户密级信息")
public class UserClassifiedInfo implements Serializable {
    private static final long serialVersionUID = 3633323321014841097L;

    @Schema(description = "密级id, 新增填-1, 修改填已有id", example = "-1")
    private Long id;

    @Schema(description = "密级名称", example = "一般")
    private String name;

    @Schema(description = "密级", example = "common")
    private String value;

    @Schema(description = "密码最小长度, 0: 不限制", example = "0")
    private int minLength;

    @Schema(description = "密码复杂度(js正则表达式)", example = "")
    private String complexity;

    @Schema(description = "密码有效期(天), 0: 永远有效", example = "180")
    private int periodOfValidity;

    @Schema(description = "登录失败最大尝试次数", example = "3")
    private int maxAllowAttempts;

    @Schema(description = "登录失败后锁定时间(分钟)", example = "60")
    private int autoUnlockTime;

    @Schema(description = "需要给出登录警告的连续登录失败次数", example = "5")
    private int warnAttempts;

    @Schema(description = "是否需要首次登陆修改密码", example = "false")
    private boolean firstLoginChangePwd;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public int getMinLength() {
        return minLength;
    }

    public void setMinLength(int minLength) {
        this.minLength = minLength;
    }

    public String getComplexity() {
        return complexity;
    }

    public void setComplexity(String complexity) {
        this.complexity = complexity;
    }

    public int getPeriodOfValidity() {
        return periodOfValidity;
    }

    public void setPeriodOfValidity(int periodOfValidity) {
        this.periodOfValidity = periodOfValidity;
    }

    public int getMaxAllowAttempts() {
        return maxAllowAttempts;
    }

    public void setMaxAllowAttempts(int maxAllowAttempts) {
        this.maxAllowAttempts = maxAllowAttempts;
    }

    public int getAutoUnlockTime() {
        return autoUnlockTime;
    }

    public void setAutoUnlockTime(int autoUnlockTime) {
        this.autoUnlockTime = autoUnlockTime;
    }

    public int getWarnAttempts() {
        return warnAttempts;
    }

    public void setWarnAttempts(int warnAttempts) {
        this.warnAttempts = warnAttempts;
    }

    public boolean isFirstLoginChangePwd() {
        return firstLoginChangePwd;
    }

    public void setFirstLoginChangePwd(boolean firstLoginChangePwd) {
        this.firstLoginChangePwd = firstLoginChangePwd;
    }

    public static long getSerialversionuid() {
        return serialVersionUID;
    }

}
