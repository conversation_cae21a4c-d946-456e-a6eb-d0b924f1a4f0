/*
 * Copyright 2013-2019 Smartdot Technologies Co., Ltd. All rights reserved.
 * SMARTDOT PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 */
package com.hd.rcugrc.bpm.facade.mobile.mvc.query;

import java.io.Serializable;

import io.swagger.v3.oas.annotations.media.Schema;


/**
 * <p>
 * 流程实例操作者信息 ，主要用于Restful
 * 用户账户信息和用户姓名信息不能为空
 * 如果是第三方系统建议和GRCv5进行同步系统用户
 *
 * <AUTHOR> href="mailto:<EMAIL>">songjj</a>
 * @version 5.3, 2019年4月10日
 * <code>
 * <pre>
 *   {
 *     "userAccount": "hd",
 *     "userName": "慧点人员"
 *   }
 *   </pre>
 * </code>
 */
@Schema(title = "caller", description = "流程实例操作用户信息，一般都是当前登录用户信息")
public class FlowInstanceCaller implements Serializable {
    
    private static final long serialVersionUID = 5049962827064517543L;
    
    private String userAccount;
    
    private String userName;
    
    /**
     * 返回操作者账户
     *
     * @return 操作者账户
     */
    @Schema(title = "操作用户账户", description = "操作用户账户", required = true, example = "hd")
    public String getUserAccount() {
        return userAccount;
    }
    
    /**
     * 设置操作者账户
     *
     * @param userAccount 操作者账户
     */
    
    public void setUserAccount(String userAccount) {
        this.userAccount = userAccount;
    }
    
    /**
     * 返回操作者姓名
     *
     * @return 操作者姓名
     */
    @Schema(title = "操作用户姓名", description = "操作用户姓名", required = true, example = "慧点人员")
    public String getUserName() {
        return userName;
    }
    
    /**
     * 设置操作者姓名
     *
     * @param userName 操作者姓名
     */
    public void setUserName(String userName) {
        this.userName = userName;
    }
    
    
}

