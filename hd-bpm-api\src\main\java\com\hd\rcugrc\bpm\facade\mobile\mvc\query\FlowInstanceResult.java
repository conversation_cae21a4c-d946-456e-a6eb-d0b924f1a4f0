/*
 * Copyright 2013-2019 Smartdot Technologies Co., Ltd. All rights reserved.
 * SMARTDOT PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 */
package com.hd.rcugrc.bpm.facade.mobile.mvc.query;

import java.io.Serializable;

import io.swagger.v3.oas.annotations.media.Schema;


/**
 * <p>
 * 执行流程应用操作后的结果，主要用于Restful接口
 *
 * <AUTHOR> href="mailto:<EMAIL>">songjj</a>
 * @version 5.3, 2019年4月10日
 */
@Schema(title = "flowInstanceResult", description = "执行流程应用操作后的结果,主用用于接收流程处理后返回对象相关信息，主要包含非流程实例活动ID的其他相关结果信息")
public class FlowInstanceResult implements Serializable {
    
    
    /**
     *
     */
    private static final long serialVersionUID = -2808375499219185706L;
    
    /**
     * 返回 接收执行流程应用操作后的结果对象key
     *
     * @return 接收执行流程应用操作后的结果对象key
     */
    @Schema(title = "接收执行流程应用操作后的结果对象key", description = "接收执行流程应用操作后的结果对象key", example = "successPageId")
    public String getResultKey() {
        return resultKey;
    }
    
    /**
     * 设置接收执行流程应用操作后的结果对象key
     *
     * @param resultKey 接收执行流程应用操作后的结果对象key
     */
    public void setResultKey(String resultKey) {
        this.resultKey = resultKey;
    }
    
    /**
     * 返回接收执行流程应用操作后的结果对象Value
     *
     * @return 接收执行流程应用操作后的结果对象Value
     */
    @Schema(title = "接收执行流程应用操作后的结果对象Value", description = "接收执行流程应用操作后的结果对象Value", example = "home.jsp")
    public Object getResultValue() {
        return resultValue;
    }
    
    /**
     * 设置接收执行流程应用操作后的结果对象Value
     *
     * @param resultValue 接收执行流程应用操作后的结果对象Value
     */
    public void setResultValue(Object resultValue) {
        this.resultValue = resultValue;
    }
    
    private String resultKey;
    
    private Object resultValue;
    
    
}

