/*
 * Copyright 2013-2019 Smartdot Technologies Co., Ltd. All rights reserved.
 * SMARTDOT PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 */
package com.hd.rcugrc.bpm.facade.mobile.mvc.query;

import com.hd.rcugrc.web.dwr.TreeNode;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.Map;

/**
 * <p>
 * 用户信息
 *
 * <AUTHOR> href="mailto:<EMAIL>">songjj</a>
 * @version 5.4, 2019年7月5日
 */
@Schema(title = "userInfo", description = "用户信息，包含账户以及名称、所在组织以及待办数量、关注数量、待回复沟通数量等")
public class UserInfo implements Serializable {
    
    private static final long serialVersionUID = 4495997919683305633L;
    private long id;
    private long deptId;
    private String deptName;
    private String account;
    private String name;
    private int securityLevel;
    
    private RoleInfo[] roles;
    private TreeNode[] belongedOrgIds;
    
    private Map<String, String> attrs;
    
    /**
     * @return 用户ID
     */
    @Schema(title = "用户ID", description = "用户ID", example = "10")
    public long getId() {
        return id;
    }
    
    /**
     * 设置用户ID
     *
     * @param id 用户ID
     */
    public void setId(long id) {
        this.id = id;
    }
    
    /**
     * @return 组织机构Id
     */
    @Schema(title = "机构ID，组织机构唯一标识", description = "机构ID，组织机构唯一标识", example = "-1")
    public long getDeptId() {
        return deptId;
    }
    
    /**
     * 设置组织机构Id
     *
     * @param l 组织机构Id
     */
    public void setDeptId(long l) {
        this.deptId = l;
    }
    
    /**
     * 返回上级名称
     *
     * @return 上级机构名称
     */
    @Schema(title = "机构名称", description = "机构名称", example = "组织机构")
    public String getDeptName() {
        return deptName;
    }
    
    /**
     * 设置机构名称
     *
     * @param deptName 机构名称
     */
    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }
    
    /**
     * @return 账户
     */
    @Schema(title = "账户", description = "账户", example = "tester")
    public String getAccount() {
        return account;
    }
    
    /**
     * @param account 设置 账户
     */
    public void setAccount(String account) {
        this.account = account;
    }
    
    
    /**
     * @return 用户姓名
     */
    @Schema(title = "用户姓名", description = "用户姓名", example = "测试用户")
    public String getName() {
        return name;
    }
    
    /**
     * 设置用户姓名
     *
     * @param name 用户姓名
     */
    public void setName(String name) {
        this.name = name;
    }
    
    /**
     * @return 用户扩展属性
     */
    @Schema(title = "用户扩展属性", description = "用户扩展属性", example = "{}")
    public Map<String, String> getAttrs() {
        return attrs;
    }
    
    /**
     * @param attrs 用户扩展属性
     */
    public void setAttrs(Map<String, String> attrs) {
        this.attrs = attrs;
    }
    
    /**
     * @return 用户拥有角色
     */
    @Schema(title = "用户拥有角色", description = "用户拥有角色")
    public RoleInfo[] getRoles() {
        return roles;
    }
    
    /**
     * 设置用户拥有角色
     *
     * @param roles 用户拥有角色
     */
    public void setRoles(RoleInfo[] roles) {
        this.roles = roles;
    }
    
    @Schema(title = "用户密级", description = "用户密级")
    public int getSecurityLevel() {
        return securityLevel;
    }
    
    public void setSecurityLevel(int securityLevel) {
        this.securityLevel = securityLevel;
    }
    
    @Schema(title = "分级管理节点", description = "分级管理节点")
    public TreeNode[] getBelongedOrgIds() {
        return belongedOrgIds;
    }
    
    public void setBelongedOrgIds(TreeNode[] belongedOrgIds) {
        this.belongedOrgIds = belongedOrgIds;
    }
}
