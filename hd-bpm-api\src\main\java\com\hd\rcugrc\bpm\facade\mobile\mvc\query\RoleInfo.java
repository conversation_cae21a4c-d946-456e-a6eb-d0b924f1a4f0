/*
 * Copyright 2013-2019 Smartdot Technologies Co., Ltd. All rights reserved.
 * SMARTDOT PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 */
package com.hd.rcugrc.bpm.facade.mobile.mvc.query;

import java.io.Serializable;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <p>
 *
 * <AUTHOR> href="mailto:<EMAIL>">songjj</a>
 * @version 5.4, 2019年9月17日
 */
@Schema(title = "roleInfo", description = "用户拥有的角色")
public class RoleInfo implements Serializable {
    private static final long serialVersionUID = 3940251568901310506L;
    
    private String name;
    
    private String code;
    
    /**
     * 获取角色名称
     *
     * @return 角色名称
     */
    @Schema(title = "角色名称", description = "角色名称", example = "普通用户")
    public String getName() {
        return name;
    }
    
    /**
     * 设置角色名称
     *
     * @param name 角色名称
     */
    public void setName(String name) {
        this.name = name;
    }
    
    /**
     * 获取角色编码
     *
     * @return 角色编码
     */
    @Schema(title = "角色编码", description = "角色编码", example = "USER")
    public String getCode() {
        return code;
    }
    
    /**
     * 设置 角色编码
     *
     * @param code 角色编码
     */
    public void setCode(String code) {
        this.code = code;
    }
    
}
