/** 
 *北京慧点科技有限公司XCOA产品。
 *注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的。 
 *@Date: 2022年7月13日
 *@Copyright: Copyright 2022 © Smartdot Technologies Co., Ltd.
 */
package com.hd.rcugrc.bpm.facade;

import io.swagger.v3.oas.annotations.media.Schema;

import org.joda.time.DateTime;

/**
 * 待办待阅信息对象
 * 
 * <AUTHOR>
 * @since 1.0, 2022年7月13日
 */
@Schema(description = "待办待阅信息对象,待办包含工作流待办和业务待办，\r\n"
        + "以下examples表示：返回账户hd下标题为test的前10条待办列表数据数据,该数据中包含用户所在GROUP级别的组织信息，并且按照待办待阅产生日期（sentDate）升序排序。")
public class TodoToreadTaskInfo extends TodoTaskInfo {
    
    /**
     * 
     */
    private static final long serialVersionUID = 1419361018086500542L;
    
    private Long srcId;
    private Integer recordTag;
    
    private Long stepId;
    private String stepName;
    private Long operId;
    private String trustor;
    private String trustorName;
    private Long senduuid;
    
    /**
     * 流程实例步骤号
     */
    private int stepSerialNumber;
    /**
     * 是否待阅 true 待阅 false 已阅
     */
    private Boolean isRead;
    
    /**
     * 创建人id
     */
    private Long creatorId;
    /**
     * 创建人账号
     */
    private String creatorAccount;
    /**
     * 创建人姓名
     */
    private String creatorName;
    /**
     * 修改人id
     */
    private Long modifyEmpId;
    /**
     * 修改人账号
     */
    private String modifyEmpAccount;
    /**
     * 修改人姓名
     */
    private String modifyEmpName;
    /**
     * 创建部门id
     */
    private Long createDeptId;
    /**
     * 创建部门code
     */
    private String createDeptCode;
    /**
     * 创建部门名称
     */
    private String createDeptName;
    /**
     * 修改部门id
     */
    private Long modifyDeptId;
    /**
     * 修改部门code
     */
    private String modifyDeptCode;
    /**
     * 修改部门名称
     */
    private String modifyDeptName;
    /**
     * 创建时间
     */
    private DateTime creationTime;
    /**
     * 最后修改时间
     */
    private DateTime lastModifiedTime;
    /**
     * 发送人账号 account
     */
    private String senderAccount;
    
    /**
     * 反馈意见是否必填 true 必填 false 不必填
     */
    private Boolean feedbackOpinionRequired;
    
    /**
     * 急缓级别
     */
    private int urgencyLevel;
    /**
     * 急缓级别描述
     */
    private String urgencyLevelName;
    
    /**
     * 催办提醒，0：无；1：催办
     */
    private int reminder;
    
    /**
     * 组织机构路径
     */
    private String groupPath;
    
    private String stepColor;
    
    private String stepTimeToDeadline;
    
    private String flowColor;
    
    private String flowTimeToDeadline;
    
    /**
     * 文件类型（文，事，会）
     */
    private String docType;
    
    /**
     * 打开链接
     */
    private String openLink;
    /**
     * 打开参数
     */
    private String openParam;
    /**
     * pc端地址
     */
    private String pcAddr;
    /**
     * 移动端地址
     */
    private String mobileAddr;
    /**
     * 组合ID,由todoId+formId+beanId组成
     */
    private String combinationId;
    
    public String getPcAddr() {
        return pcAddr;
    }
    
    public void setPcAddr(String pcAddr) {
        this.pcAddr = pcAddr;
    }
    
    public String getMobileAddr() {
        return mobileAddr;
    }
    
    public void setMobileAddr(String mobileAddr) {
        this.mobileAddr = mobileAddr;
    }
    
    public String getCombinationId() {
        return combinationId;
    }
    
    public void setCombinationId(String combinationId) {
        this.combinationId = combinationId;
    }
    
    /**
     * 急缓级别
     *
     * @return the urgencyLevel
     */
    public int getUrgencyLevel() {
        return urgencyLevel;
    }
    
    /**
     * @param urgencyLevel the urgencyLevel to set
     */
    public void setUrgencyLevel(int urgencyLevel) {
        this.urgencyLevel = urgencyLevel;
    }
    
    /**
     * 急缓级别描述
     *
     * @return the urgencyLevelName
     */
    public String getUrgencyLevelName() {
        return urgencyLevelName;
    }
    
    /**
     * @param urgencyLevelName the urgencyLevelName to set
     */
    public void setUrgencyLevelName(String urgencyLevelName) {
        this.urgencyLevelName = urgencyLevelName;
    }
    
    /**
     * 催办 0：无；1：催办
     *
     * @return the reminder
     */
    public int getReminder() {
        return reminder;
    }
    
    /**
     * @param reminder the reminder to set
     */
    public void setReminder(int reminder) {
        this.reminder = reminder;
    }
    
    public String getGroupPath() {
        return groupPath;
    }
    
    /**
     * 组织机构
     */
    public void setGroupPath(String groupPath) {
        this.groupPath = groupPath;
    }
    
    /**
     * @return the stepColor
     */
    @Schema(description = "节点时限字体颜色", example = "red")
    public String getStepColor() {
        return stepColor;
    }
    
    /**
     * @param stepColor the stepColor to set
     */
    public void setStepColor(String stepColor) {
        this.stepColor = stepColor;
    }
    
    /**
     * @return the stepTimeToDeadline
     */
    @Schema(description = "距离节点限时时限", example = "1天2小时")
    public String getStepTimeToDeadline() {
        return stepTimeToDeadline;
    }
    
    /**
     * @param stepTimeToDeadline the stepTimeToDeadline to set
     */
    public void setStepTimeToDeadline(String stepTimeToDeadline) {
        this.stepTimeToDeadline = stepTimeToDeadline;
    }
    
    /**
     * @return the flowColor
     */
    @Schema(description = "流程时限字体颜色", example = "blue")
    public String getFlowColor() {
        return flowColor;
    }
    
    /**
     * @param flowColor the flowColor to set
     */
    public void setFlowColor(String flowColor) {
        this.flowColor = flowColor;
    }
    
    /**
     * @return the flowTimeToDeadline
     */
    @Schema(description = "距离流程实例限时时限", example = "1天2小时")
    public String getFlowTimeToDeadline() {
        return flowTimeToDeadline;
    }
    
    /**
     * @param flowTimeToDeadline the flowTimeToDeadline to set
     */
    public void setFlowTimeToDeadline(String flowTimeToDeadline) {
        this.flowTimeToDeadline = flowTimeToDeadline;
    }
    
    public String getDocType() {
        return docType;
    }
    
    public void setDocType(String docType) {
        this.docType = docType;
    }
    
    public String getOpenLink() {
        return openLink;
    }
    
    public void setOpenLink(String openLink) {
        this.openLink = openLink;
    }
    
    public String getOpenParam() {
        return openParam;
    }
    
    public void setOpenParam(String openParam) {
        this.openParam = openParam;
    }
    
    /**
     * @return the srcId
     */
    @Schema(description = "源记录id", example = "1")
    public Long getSrcId() {
        return srcId;
    }
    
    /**
     * @param srcId the srcId to set
     */
    public void setSrcId(Long srcId) {
        this.srcId = srcId;
    }
    
    /**
     * @return the recordTag
     */
    @Schema(description = "记录标识，0待办，1待阅", example = "0")
    public Integer getRecordTag() {
        return recordTag;
    }
    
    /**
     * @param recordTag the recordTag to set
     */
    public void setRecordTag(Integer recordTag) {
        this.recordTag = recordTag;
    }
    
    /**
     * 是否已阅
     * 
     * @return 是否已阅， false:未阅， true:已阅
     */
    @Schema(description = "是否已阅， false:未阅， true:已阅", example = "0")
    public Boolean getIsRead() {
        return isRead;
    }
    
    /**
     * 设置是否已阅
     * 
     * @param isRead 是否已阅， false:未阅， true:已阅
     */
    public void setIsRead(Boolean isRead) {
        this.isRead = isRead;
    }
    
    /**
     * 创建人ID
     * 
     * @return 创建人ID
     */
    @Schema(description = "创建人ID", example = "2")
    public Long getCreatorId() {
        return creatorId;
    }
    
    /**
     * 设置创建人ID
     * 
     * @param creatorId 创建人ID
     */
    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }
    
    /**
     * 创建人账号
     * 
     * @return the creatorAccount
     */
    @Schema(description = "创建人账号", example = "user01")
    public String getCreatorAccount() {
        return creatorAccount;
    }
    
    /**
     * 设置创建人账号
     * 
     * @param creatorAccount 创建人姓名
     */
    public void setCreatorAccount(String creatorAccount) {
        this.creatorAccount = creatorAccount;
    }
    
    /**
     * 创建人姓名
     * 
     * @return 创建人姓名
     */
    @Schema(description = "创建人姓名", example = "张三")
    public String getCreatorName() {
        return creatorName;
    }
    
    /**
     * 设置创建人姓名
     * 
     * @param creatorName 创建人姓名
     */
    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }
    
    /**
     * 修改人ID
     * 
     * @return the modifyEmpId
     */
    @Schema(description = "修改人ID", example = "3")
    public Long getModifyEmpId() {
        return modifyEmpId;
    }
    
    /**
     * 设置修改人ID
     * 
     * @param modifyEmpId 修改人ID
     */
    public void setModifyEmpId(Long modifyEmpId) {
        this.modifyEmpId = modifyEmpId;
    }
    
    /**
     * 修改人账号
     * 
     * @return 修改人账号
     */
    @Schema(description = "修改人账号", example = "user02")
    public String getModifyEmpAccount() {
        return modifyEmpAccount;
    }
    
    /**
     * 设置修改人账号
     * 
     * @param modifyEmpAccount 修改人账号
     */
    public void setModifyEmpAccount(String modifyEmpAccount) {
        this.modifyEmpAccount = modifyEmpAccount;
    }
    
    /**
     * 修改人姓名
     * 
     * @return 修改人姓名
     */
    @Schema(description = "修改人姓名", example = "李四")
    public String getModifyEmpName() {
        return modifyEmpName;
    }
    
    /**
     * 设置修改人姓名
     * 
     * @param modifyEmpName 修改人姓名
     */
    public void setModifyEmpName(String modifyEmpName) {
        this.modifyEmpName = modifyEmpName;
    }
    
    /**
     * 创建部门ID
     * 
     * @return 创建部门ID
     */
    @Schema(description = "创建部门ID", example = "1")
    public Long getCreateDeptId() {
        return createDeptId;
    }
    
    /**
     * 设置创建部门ID
     * 
     * @param createDeptId 创建部门ID
     */
    public void setCreateDeptId(Long createDeptId) {
        this.createDeptId = createDeptId;
    }
    
    /**
     * 创建部门编码
     * 
     * @return 创建部门编码
     */
    @Schema(description = "创建部门编码", example = "2")
    public String getCreateDeptCode() {
        return createDeptCode;
    }
    
    /**
     * 设置创建部门编码
     * 
     * @param createDeptCode 创建部门编码
     */
    public void setCreateDeptCode(String createDeptCode) {
        this.createDeptCode = createDeptCode;
    }
    
    /**
     * 创建部门名称
     * 
     * @return 创建部门名称
     */
    @Schema(description = "创建部门名称", example = "财务部")
    public String getCreateDeptName() {
        return createDeptName;
    }
    
    /**
     * 设置创建部门名称
     * 
     * @param createDeptName 创建部门名称
     */
    public void setCreateDeptName(String createDeptName) {
        this.createDeptName = createDeptName;
    }
    
    /**
     * 修改部门ID
     * 
     * @return 修改部门ID
     */
    @Schema(description = "修改部门ID", example = "3")
    public Long getModifyDeptId() {
        return modifyDeptId;
    }
    
    /**
     * 设置修改部门ID
     * 
     * @param modifyDeptId 修改部门ID
     */
    public void setModifyDeptId(Long modifyDeptId) {
        this.modifyDeptId = modifyDeptId;
    }
    
    /**
     * 修改部门编码
     * 
     * @return 修改部门编码
     */
    @Schema(description = "修改部门编码", example = "3")
    public String getModifyDeptCode() {
        return modifyDeptCode;
    }
    
    /**
     * 设置修改部门编码
     * 
     * @param modifyDeptCode 修改部门编码
     */
    public void setModifyDeptCode(String modifyDeptCode) {
        this.modifyDeptCode = modifyDeptCode;
    }
    
    /**
     * 修改部门名称
     * 
     * @return the modifyDeptName
     */
    @Schema(description = "修改部门名称", example = "审计部")
    public String getModifyDeptName() {
        return modifyDeptName;
    }
    
    /**
     * 设置修改部门名称
     * 
     * @param modifyDeptName 修改部门名称
     */
    public void setModifyDeptName(String modifyDeptName) {
        this.modifyDeptName = modifyDeptName;
    }
    
    /**
     * 创建时间
     * 
     * @return the creationTime
     */
    @Schema(description = "创建时间", example = "2019-05-15 18:53:33")
    public DateTime getCreationTime() {
        return creationTime;
    }
    
    /**
     * 设置创建时间
     * 
     * @param creationTime 创建时间
     */
    public void setCreationTime(DateTime creationTime) {
        this.creationTime = creationTime;
    }
    
    /**
     * 修改时间
     * 
     * @return the lastModifiedTime
     */
    @Schema(description = "修改时间", example = "2019-05-15 18:53:33")
    public DateTime getLastModifiedTime() {
        return lastModifiedTime;
    }
    
    /**
     * 设置修改时间
     * 
     * @param lastModifiedTime 修改时间
     */
    public void setLastModifiedTime(DateTime lastModifiedTime) {
        this.lastModifiedTime = lastModifiedTime;
    }
    
    /**
     * @return 流程实例步骤号
     */
    @Schema(description = "流程实例步骤号", example = "2")
    public int getStepSerialNumber() {
        return stepSerialNumber;
    }
    
    /**
     * 设置流程实例步骤号
     * 
     * @param stepSerialNumber 流程实例步骤号
     */
    public void setStepSerialNumber(int stepSerialNumber) {
        this.stepSerialNumber = stepSerialNumber;
    }
    
    /**
     * @return 发送者账号
     */
    @Schema(description = "发送者账号", example = "hd")
    public String getSenderAccount() {
        return senderAccount;
    }
    
    /**
     * 设置发送者账号
     * 
     * @param senderAccount 发送者账号
     */
    public void setSenderAccount(String senderAccount) {
        this.senderAccount = senderAccount;
    }
    
    public Boolean getFeedbackOpinionRequired() {
        return feedbackOpinionRequired;
    }
    
    public void setFeedbackOpinionRequired(Boolean feedbackOpinionRequired) {
        this.feedbackOpinionRequired = feedbackOpinionRequired;
    }
    
    /**
     * @return the stepId
     */
    public Long getStepId() {
        return stepId;
    }
    
    /**
     * @param stepId the stepId to set
     */
    public void setStepId(Long stepId) {
        this.stepId = stepId;
    }
    
    /**
     * @return the stepName
     */
    public String getStepName() {
        return stepName;
    }
    
    /**
     * @param stepName the stepName to set
     */
    public void setStepName(String stepName) {
        this.stepName = stepName;
    }
    
    /**
     * @return the operId
     */
    public Long getOperId() {
        return operId;
    }
    
    /**
     * @param operId the operId to set
     */
    public void setOperId(Long operId) {
        this.operId = operId;
    }
    
    /**
     * @return the trustor
     */
    public String getTrustor() {
        return trustor;
    }
    
    /**
     * @param trustor the trustor to set
     */
    public void setTrustor(String trustor) {
        this.trustor = trustor;
    }
    
    /**
     * @return the trustorName
     */
    public String getTrustorName() {
        return trustorName;
    }
    
    /**
     * @param trustorName the trustorName to set
     */
    public void setTrustorName(String trustorName) {
        this.trustorName = trustorName;
    }
    
    /**
     * @return the senduuid
     */
    public Long getSenduuid() {
        return senduuid;
    }
    
    /**
     * @param senduuid the senduuid to set
     */
    public void setSenduuid(Long senduuid) {
        this.senduuid = senduuid;
    }
    
}
