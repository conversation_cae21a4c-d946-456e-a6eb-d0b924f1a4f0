/*
 * Copyright 2013-2019 Smartdot Technologies Co., Ltd. All rights reserved.
 * SMARTDOT PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 */
package com.hd.rcugrc.bpm.facade.form;

import java.io.Serializable;
import java.util.Map;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <p>
 * 流程页面控件字段属性以及属性值
 * 
 * <AUTHOR> href="mailto:<EMAIL>">songjj</a>
 * @version 5.4, 2019年7月2日
 */
@Schema(description = "表单属性信息以及属性值，主要用于页面展现")
public class ProcessFormFieldValue implements Serializable {

    private static final long serialVersionUID = -1074228642406247688L;

    private String name;
    
    private String columnName;

    private String caption;

    private String dataType;

    private String specType;

    private Object value;

    private String defaultValue;

    private String htmlType;

    private boolean mutable;

    private boolean idProperty;

    private boolean mobileVisible;

    private boolean required;

    private boolean readonly;

    private Map<String, Object> attr;

    private int index;

    private int controlCat;

    // 在线自定义表单前端属性
    private Map<String, Object> attrFD;

    /**
     * 获取字段在页面的顺序
     * 
     * @return 字段顺序
     */
    @Schema(title = "字段在页面上的顺序", description = "字段在页面上的顺序", example = "1")
    public int getIndex() {
        return index;
    }

    /**
     * 设置 字段顺序
     * 
     * @param index 字段顺序
     */
    public void setIndex(int index) {
        this.index = index;
    }

    /**
     * 返回属性名称
     * 
     * @return 属性名称
     */
    @Schema(title = "属性名称", description = "属性名称", example = "shuxingmingzi2")
    public String getName() {
        return name;
    }

    /**
     * 设置属性名称
     * 
     * @param name 属性名称
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * 返回字段名称
     * 
     * @return 字段名称
     */
    @Schema(title = "字段名称", description = "字段名称")
    public String getColumnName() {
        return columnName;
    }

    public void setColumnName(String columnName) {
        this.columnName = columnName;
    }

    /**
     * 获取属性中文名称
     * 
     * @return 属性中文名称
     */
    @Schema(title = "属性中文名称", description = "属性中文名称", example = "标题")
    public String getCaption() {
        return caption;
    }

    /**
     * 设置属性中文名称
     * 
     * @param caption 属性中文名称
     */
    public void setCaption(String caption) {
        this.caption = caption;
    }

    /**
     * 返回所使用控件存储的数据类型
     * 
     * <pre>
     * int(整型),long(长整型),short(短整型),
     * float(浮点),double(双浮点),string(字符),.
     * selectlist(选择列表),title(标题),docCategory(文件类型),
     * signname(人员签署),signdep(机构签署),date(日期),
     * hdjqueryattach(附件),selectperson(选人),opinion(意见),
     * doc(NTKO正文),docNumber(文号),collection(子表),
     * existcollection(已有子表)
     * </pre>
     * 
     * @return 所使用的控件类型
     */
    @Schema(title = "控件数据存储类型，类型主要包含int(整型),long(长整型),short(短整型),\r\n"
            + " float(浮点),double(双浮点),string(字符),\r\nstring（字符串）、long(数值)、doc（正文）、\r\n"
            + "selectlist(选择列表),title(标题),docCategory(文件类型),\r\nsignname(人员签署),signdep(机构签署),date(日期),\r\n"
            + "hdjqueryattach(附件),selectperson(选人),opinion(意见),\r\n"
            + "doc(NTKO正文),docNumber(文号),collection(子表)existcollection(已有子表),等等",
            description = "控件数据存储类型，类型主要包含int(整型),long(长整型),short(短整型),\r\n"
                    + " float(浮点),double(双浮点),string(字符),\r\nstring（字符串）、long(数值)、doc（正文）、\r\n"
                    + "selectlist(选择列表),title(标题),docCategory(文件类型),\r\nsignname(人员签署),signdep(机构签署),date(日期),\r\n"
                    + "hdjqueryattach(附件),selectperson(选人),opinion(意见),\r\n"
                    + "doc(NTKO正文),docNumber(文号),collection(子表)existcollection(已有子表),等等",
            example = "title")
    public String getDataType() {
        return dataType;
    }

    /**
     * 设置所使用控件存储的数据类型
     * 
     * @param dataType 属所使用控件存储的数据类型
     */
    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    /**
     * 返回所使用的控件类型
     * 
     * <pre>
     * 标识主要包含hdTextField（文本输入框）、hdTextAreaField(多行文本框)、hdInputHelpField（普通文本带列表帮助框）
     * hdRichTextField（富文本）、hdTinymceField(新富文本控件)、hdNumberField(数字输入框)
     * hdSelectOrgField (页面所属机构签署框)、hdLabelInputField(标签输入框控件)、hdOpinionField（意见签署框）
     * hdOpinionAttachment（意见签署框带附件）、hdUserSignField（签署人）、hdDeptSignField（签署部门）
     * hdPersonPickerArea(机构人员选择框(多行))、hdPersonPickerField( 机构人员选择框(单行))
     * hdSendToRead(流程送阅选人控件)、docNumber（文号）、docCategory（文件类型）
     * docCategoryTitle（文件类型标题）、hdTextFieldHref(文本连接)、hdComboBoxField（下拉框）
     * hdDocField（正文）、hdjqueryattach（附件）、hdChildTableRowEditColResizeField(动态添加行子表)
     * hdDateField（日期输入框）、hdDateField（日期输入框）hdChildTable(子表)等等
     * 
     * </pre>
     * 
     * @return 所使用的控件类型
     */
    @Schema(title = "属性使用的控件类型，"
            + "类型主要包含hdTextField（文本输入框）、hdTextAreaField(多行文本框)、hdInputHelpField（普通文本带列表帮助框）\r\n"
            + "hdRichTextField（富文本）、hdTinymceField(新富文本控件)、hdNumberField(数字输入框)\r\n"
            + "hdSelectOrgField (页面所属机构签署框)、hdLabelInputField(标签输入框控件)、hdOpinionField（意见签署框）\r\n"
            + "hdOpinionAttachment（意见签署框带附件）、hdUserSignField（签署人）、hdDeptSignField（签署部门）\r\n"
            + "hdPersonPickerArea(机构人员选择框(多行))、hdPersonPickerField( 机构人员选择框(单行))\r\n"
            + "hdSendToRead(流程送阅选人控件)、docNumber（文号）、docCategory（文件类型）\r\n"
            + "docCategoryTitle（文件类型标题）、hdTextFieldHref(文本连接)、hdComboBoxField（下拉框）\r\n"
            + "hdDocField（正文）、hdjqueryattach（附件）、hdChildTableRowEditColResizeField(动态添加行子表)\r\n"
            + "hdDateField（日期输入框）、hdDateField（日期输入框）hdChildTable(子表)等等\r\n",
            description = "属性使用的控件类型，"
            + "类型主要包含hdTextField（文本输入框）、hdTextAreaField(多行文本框)、hdInputHelpField（普通文本带列表帮助框）\r\n"
            + "hdRichTextField（富文本）、hdTinymceField(新富文本控件)、hdNumberField(数字输入框)\r\n"
            + "hdSelectOrgField (页面所属机构签署框)、hdLabelInputField(标签输入框控件)、hdOpinionField（意见签署框）\r\n"
            + "hdOpinionAttachment（意见签署框带附件）、hdUserSignField（签署人）、hdDeptSignField（签署部门）\r\n"
            + "hdPersonPickerArea(机构人员选择框(多行))、hdPersonPickerField( 机构人员选择框(单行))\r\n"
            + "hdSendToRead(流程送阅选人控件)、docNumber（文号）、docCategory（文件类型）\r\n"
            + "docCategoryTitle（文件类型标题）、hdTextFieldHref(文本连接)、hdComboBoxField（下拉框）\r\n"
            + "hdDocField（正文）、hdjqueryattach（附件）、hdChildTableRowEditColResizeField(动态添加行子表)\r\n"
            + "hdDateField（日期输入框）、hdDateField（日期输入框）hdChildTable(子表)等等\r\n", example = "title")
    public String getSpecType() {
        return specType;
    }

    /**
     * 设置所使用的控件类型
     * 
     * @param specType 所使用的控件类型
     */
    public void setSpecType(String specType) {
        this.specType = specType;
    }

    /**
     * 返回属性对应存储值
     * 
     * @return 属性对应存储值
     */
    @Schema(title = "属性对应存储值", description = "属性对应存储值", example = "接口测试分裂汇聚2019-06-17 16:21:28:216")
    public Object getValue() {
        return value;
    }

    /**
     * 设置属性对应存储值
     * 
     * @param value 属性对应存储值
     */
    public void setValue(Object value) {
        this.value = value;
    }

    /**
     * 获取字段对应默认值
     * 
     * @return 字段对应默认值
     */
    @Schema(title = "字段对应默认值", description = "字段对应默认值", example = "test")
    public String getDefaultValue() {
        return defaultValue;
    }

    /**
     * 设置字段对应默认值
     * 
     * @param defaultValue 字段对应默认值
     */
    public void setDefaultValue(String defaultValue) {
        this.defaultValue = defaultValue;
    }

    /**
     * 是否可编辑
     * 
     * @return true 可编辑 false 不可编辑
     */
    @Schema(title = "是否可编辑,true 可编辑 false 不可编辑", description = "是否可编辑,true 可编辑 false 不可编辑", example = "true")
    public boolean isMutable() {
        return mutable;
    }

    /**
     * 设置是否可编辑
     * 
     * @param mutable 是否可编辑
     */
    public void setMutable(boolean mutable) {
        this.mutable = mutable;
    }

    /**
     * 是否为ID属性
     * 
     * @return true 为ID属性， false 非ID属性
     */
    @Schema(title = "是否为ID属性,true 为ID属性， false 非ID属性", description = "是否为ID属性,true 为ID属性， false 非ID属性", example = "true")
    public boolean isIdProperty() {
        return idProperty;
    }

    /**
     * 设置是否为ID属性
     * 
     * @param idProperty true 为ID属性 false 非ID属性
     */
    public void setIdProperty(boolean idProperty) {
        this.idProperty = idProperty;
    }

    /**
     * 获取扩展属性
     * 
     * @return 扩展属性
     */
    @Schema(title = "扩展属性", description = "扩展属性", example = "{}")
    public Map<String, Object> getAttr() {
        return attr;
    }

    /**
     * 设置扩展属性
     * 
     * @param attr 扩展属性
     */
    public void setAttr(Map<String, Object> attr) {
        this.attr = attr;
    }

    /**
     * 获取是否在手机上显示该字段
     * 
     */
    @Schema(title = "是否在移动端上显示该字段,true 为显示， false 不显示", description = "是否在移动端上显示该字段,true 为显示， false 不显示", example = "true")
    public boolean isMobileVisible() {
        return mobileVisible;
    }

    /**
     * 设置是否在移动端显示该属性
     * 
     * @param mobileVisible ,true 为显示， false 不显示
     */
    public void setMobileVisible(boolean mobileVisible) {
        this.mobileVisible = mobileVisible;
    }

    /**
     * 获取是否为必填项
     * 
     * @return true 必填，false 非必填
     */
    @Schema(title = "是否为必填项,true 必填，false 非必填", description = "是否为必填项,true 必填，false 非必填", example = "false")
    public boolean isRequired() {
        return required;
    }

    /**
     * 设置为必填项
     * 
     * @param required true 必填，false 非必填
     */
    public void setRequired(boolean required) {
        this.required = required;
    }

    /**
     * 获取是否为只读
     * 
     * @return true 只读，false 编辑
     */
    @Schema(title = "是否为只读,true 只读，false 编辑", description = "是否为只读,true 只读，false 编辑", example = "false")
    public boolean isReadonly() {
        return readonly;
    }

    public void setReadonly(boolean readonly) {
        this.readonly = readonly;
    }

    /**
     * 获取控件在页面展示的类型
     * 
     * @return 控件在页面展示的类型，如 文本域：textarea、 文本输入框：input、选择框：checkbox等等
     */
    @Schema(title = "控件在页面展示的类型，如 textarea、 输入框：input、选择框：checkbox", description = "控件在页面展示的类型，如 textarea、 输入框：input、选择框：checkbox", example = "textarea")
    public String getHtmlType() {
        return htmlType;
    }

    /**
     * 设置控件在页面展示的类型
     * 
     * @param htmlType 控件在页面展示的类型
     */
    public void setHtmlType(String htmlType) {
        this.htmlType = htmlType;
    }

    /**
     * 获取控件分类， 1：平台控件,2:产品控件 ，3:项目控件）
     * 
     * @return the controlCat
     */
    @Schema(title = "控件分类， 1：平台控件,2:产品控件 ，3:项目控件）", description = "控件分类， 1：平台控件,2:产品控件 ，3:项目控件）", example = "1")
    public int getControlCat() {
        return this.controlCat;
    }

    /**
     * @param controlCat 控件分类， 1：平台控件,2:产品控件 ，3:项目控件）
     */
    public void setControlCat(int controlCat) {
        this.controlCat = controlCat;
    }

    /**
     * @return the attrFD onl表单属性
     */
    @Schema(title = "onl表单属性", description = "onl表单属性", example = "{}")
    public Map<String, Object> getAttrFD() {
        return attrFD;
    }

    public void setAttrFD(Map<String, Object> attrFD) {
        this.attrFD = attrFD;
    }

}
