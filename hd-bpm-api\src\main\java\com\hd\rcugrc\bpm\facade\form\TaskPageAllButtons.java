/*
 * Copyright 2013-2019 Smartdot Technologies Co., Ltd. All rights reserved.
 * SMARTDOT PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 */
package com.hd.rcugrc.bpm.facade.form;

import java.io.Serializable;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <p>
 * 任务操作按钮相关信息，主要用于Restful
 * 
 * <AUTHOR> href="mailto:<EMAIL>">pandy</a>
 * @version 5.5, 2020年7月15日
 */
@Schema(title = "任务页面全部按钮", description = "包含：推送按钮，保存按钮、业务按钮（转办、沟通、加减签、知会、退回、关注），\r\n")
public class TaskPageAllButtons implements Serializable {

    /**
     * 
     */
    private static final long serialVersionUID = -4095029336379595361L;

    /**
     * 
     */
    public TaskPageAllButtons() {
        super();
    }

    /**
     * 
     * @param businessTaskButtons
     * @param saveTaskButtons
     * @param pushTaskButtons
     */
    public TaskPageAllButtons(TaskButton[] businessTaskButtons, TaskButton[] saveTaskButtons,
            TaskButton[] pushTaskButtons) {
        super();
        this.businessTaskButtons = businessTaskButtons;
        this.saveTaskButtons = saveTaskButtons;
        this.pushTaskButtons = pushTaskButtons;
    }

    private TaskButton[] businessTaskButtons;

    private TaskButton[] saveTaskButtons;

    private TaskButton[] pushTaskButtons;

    /**
     * 业务按钮（转办、沟通、加减签、知会、退回、关注）
     * 
     * @return the businessTaskButtons
     */
    public TaskButton[] getBusinessTaskButtons() {
        return businessTaskButtons;
    }

    /**
     * 业务按钮（转办、沟通、加减签、知会、退回、关注）
     * 
     * @param businessTaskButtons the businessTaskButtons to set
     */
    public void setBusinessTaskButtons(TaskButton[] businessTaskButtons) {
        this.businessTaskButtons = businessTaskButtons;
    }

    /**
     * 保存按钮
     * 
     * @return the saveTaskButtons
     */
    public TaskButton[] getSaveTaskButtons() {
        return saveTaskButtons;
    }

    /**
     * 保存按钮
     * 
     * @param saveTaskButtons the saveTaskButtons to set
     */
    public void setSaveTaskButtons(TaskButton[] saveTaskButtons) {
        this.saveTaskButtons = saveTaskButtons;
    }

    /**
     * 推送按钮
     * 
     * @return the pushTaskButtons
     */
    public TaskButton[] getPushTaskButtons() {
        return pushTaskButtons;
    }

    /**
     * 推送按钮
     * 
     * @param pushTaskButtons the pushTaskButtons to set
     */
    public void setPushTaskButtons(TaskButton[] pushTaskButtons) {
        this.pushTaskButtons = pushTaskButtons;
    }
}
