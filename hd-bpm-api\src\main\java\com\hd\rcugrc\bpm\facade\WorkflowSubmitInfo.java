/**
 * Copyright 2013-2021 Smartdot Technologies Co., Ltd. All rights reserved.
 * SMARTDOT PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 */
package com.hd.rcugrc.bpm.facade;

import java.io.Serializable;

import org.joda.time.DateTime;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <p>流程提交信息
 *
 * <AUTHOR> href="mailto:<EMAIL>">bimw</a>
 * @version 1.0, 2021-7-19 
 */
@Schema(description = "流程提交信息")
public class WorkflowSubmitInfo implements Serializable {
    private static final long serialVersionUID = 3645146673124169253L;
    private long id;
    private long instId;
    private String creatorAccount;
    private DateTime createDate;
    private long dataSize;
    private int status;
    private boolean error;
    private String remark;
    
    /**
     *返回id
     *
     * @return
     */
    @Schema(description = "流程提交信息id")
    public long getId() {
        return id;
    }
    
    /**
     *设置id
     *
     * @param id
     */
    public void setId(long id) {
        this.id = id;
    }
    
    /**
     *返回流程实例id
     *
     * @return
     */
    @Schema(description = "流程实例id")
    public long getInstId() {
        return instId;
    }
    
    /**
     *设置流程实例id
     *
     * @param instId
     */
    public void setInstId(long instId) {
        this.instId = instId;
    }
    
    /**
     *返回提交人账号
     *
     * @return
     */
    @Schema(description = "提交人账号")
    public String getCreatorAccount() {
        return creatorAccount;
    }
    
    /**
     *设置提交人账号
     *
     * @param creatorAccount
     */
    public void setCreatorAccount(String creatorAccount) {
        this.creatorAccount = creatorAccount;
    }
    
    /**
     *返回提交时间
     *
     * @return
     */
    @Schema(description = "提交时间")
    public DateTime getCreateDate() {
        return createDate;
    }
    
    /**
     *设置提交时间
     *
     * @param createDate
     */
    public void setCreateDate(DateTime createDate) {
        this.createDate = createDate;
    }
    

    /**
     *返回提交数据大小
     *
     * @return
     */
    @Schema(description = "提交数据大小")
    public long getDataSize() {
        return dataSize;
    }

    /**
     *设置提交数据大小
     *
     * @param dataSize
     */
    public void setDataSize(long dataSize) {
        this.dataSize = dataSize;
    }
    
    /**
     *返回异步提交状态
     *
     * @return
     */
    @Schema(description = "异步提交状态(提交操作后 = 0 服务端已接收 = 1 服务端开始执行 = 2 服务端执行完毕 = 3)")
    public int getStatus() {
        return status;
    }
    
    /**
     *设置异步提交状态
     *
     * @param satus
     */
    public void setStatus(int status) {
        this.status = status;
    }
    
    /**
     *返回提交是否失败
     *
     * @return
     */
    @Schema(description = "提交是否失败")
    public boolean isError() {
        return error;
    }
    
    /**
     *设置提交是否失败
     *
     * @param error
     */
    public void setError(boolean error) {
        this.error = error;
    }
    
    /**
     *返回备注
     *
     * @return
     */
    @Schema(description = "备注，提交执行成功后可获取到活动环节信息，格式为二维数组，每一行为一个当前处理步骤的定义信息，"
            + " 定义信息的第一列是当前处理步骤的所隶属的流转实例的id，第二列是当前处理步骤的id", example = "{\"activeSteps\":[[1,2]]}")
    public String getRemark() {
        return remark;
    }
    
    /**
     *设置备注
     *
     * @param remark
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }
    
}
