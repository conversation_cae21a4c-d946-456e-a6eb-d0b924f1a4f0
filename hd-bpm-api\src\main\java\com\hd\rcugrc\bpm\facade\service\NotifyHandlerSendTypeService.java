/** 
*北京慧点科技有限公司XCOA产品。
*注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的。 
*@Date: 2022年9月5日
*@Copyright: Copyright 2022 © Smartdot Technologies Co., Ltd.
*/
package com.hd.rcugrc.bpm.facade.service;

import java.util.Map;

import com.hd.rcugrc.bpm.facade.NotifyHandlerSendTypeBean;

/**
 * <AUTHOR>
 * @since  1.0, 2022年9月5日
 */
public interface NotifyHandlerSendTypeService {
    
    /**
     * 获取发送方式开关
     * 
     * @return
     * <AUTHOR>
     * @since 1.0, 2022年9月5日
     */
    public Map<String, Map<String, Object>> getSendTypeMap();
    
    /**
     * 获取通知处理人的发送方式开关(勾选之前)
     * 
     * @return
     * <AUTHOR>
     * @since 1.0, 2022年9月5日
     */
    public NotifyHandlerSendTypeBean getNotifyHandlerSendTypeBean();
    
    /**
     * 校验当前发送方式是否是打开的(勾选之后)
     * @param sendType, 支持逗号间隔如sendType="email,sms"
     * @return
     * <AUTHOR>
     * @since  1.0, 2022年9月6日
     */
    public boolean checkNeedSend(String sendType);
}
