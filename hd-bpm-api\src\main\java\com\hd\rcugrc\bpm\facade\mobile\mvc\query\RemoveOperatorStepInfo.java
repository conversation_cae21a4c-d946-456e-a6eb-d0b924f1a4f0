/*
 * Copyright 2013-2019 Smartdot Technologies Co., Ltd. All rights reserved.
 * SMARTDOT PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 */
package com.hd.rcugrc.bpm.facade.mobile.mvc.query;

import java.io.Serializable;

import com.hd.rcugrc.bpm.OperatorBean;

import io.swagger.v3.oas.annotations.media.Schema;


/**
 * <p>
 * 流程实例活动环节减签参数，主要用于Restful接口
 *
 * <AUTHOR> href="mailto:<EMAIL>">songjj</a>
 * @version 5.3, 2019年4月10日
 */
@Schema(title = "RemoveOperatorStepInfo", description = "减签时，指定删除的子流程，或者是多人环节")
public class RemoveOperatorStepInfo implements Serializable {
    
    /**
     *
     */
    private static final long serialVersionUID = 2810998173894785954L;
    
    private long subflowInstId;
    
    private long stepOperatorId;
    
    private OperatorBean[] operators;
    
    /**
     * @return the subflowInstId
     */
    @Schema(title = "子流程流程实例id，与stepOperatorId二选一，因为环节要么是子流程环节，要么是多人环节",
            description = "子流程流程实例id，与stepOperatorId二选一，因为环节要么是子流程环节，要么是多人环节", example = "100")
    public long getSubflowInstId() {
        return subflowInstId;
    }
    
    /**
     * @param subflowInstId the subflowInstId to set
     */
    public void setSubflowInstId(long subflowInstId) {
        this.subflowInstId = subflowInstId;
    }
    
    /**
     * @return the stepOperatorId
     */
    @Schema(title = "stepOperatorId环节执行人id，与子流程流程实例id二选一，因为环节要么是子流程环节，要么是多人环节",
            description = "stepOperatorId环节执行人id，与子流程流程实例id二选一，因为环节要么是子流程环节，要么是多人环节", example = "100")
    public long getStepOperatorId() {
        return stepOperatorId;
    }
    
    /**
     * @param stepOperatorId the stepOperatorId to set
     */
    public void setStepOperatorId(long stepOperatorId) {
        this.stepOperatorId = stepOperatorId;
    }
    
    /**
     * @return the operators
     */
    @Schema(title = "需要删除的执行者", description = "需要删除的执行者", required = true)
    public OperatorBean[] getOperators() {
        return operators;
    }
    
    /**
     * @param operators the operators to set
     */
    public void setOperators(OperatorBean[] operators) {
        this.operators = operators;
    }
}
