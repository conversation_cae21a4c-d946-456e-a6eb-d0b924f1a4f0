/*
 * Copyright 2013-2021 Smartdot Technologies Co., Ltd. All rights reserved.
 * SMARTDOT PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 */
package com.hd.rcugrc.bpm.facade.exception;

/**
 * <p>
 * 
 * <AUTHOR> href="mailto:<EMAIL>">pandy</a>
 * @version 1.0, 2021年3月10日
 */
public class ProcessFormException extends FacadeException {
    /**
     * 
     */
    private static final long serialVersionUID = 1292810292637120450L;

    public ProcessFormException() {
        super();
    }

    public ProcessFormException(int code, String message) {
        super(message);
        setCode(code);
    }

    public ProcessFormException(int code, String message, Throwable cause) {
        super(message, cause);
        setCode(code);
    }

    public ProcessFormException(String message, Throwable cause) {
        super(message, cause);
    }

    public ProcessFormException(String message) {
        super(message);
    }

    public ProcessFormException(Throwable cause) {
        super(cause);
    }
}
