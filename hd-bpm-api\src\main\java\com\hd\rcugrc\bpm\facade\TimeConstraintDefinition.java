/*
 * Copyright 2013-2021 Smartdot Technologies Co., Ltd. All rights reserved.
 * SMARTDOT PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 */
package com.hd.rcugrc.bpm.facade;

import java.io.Serializable;

import com.hd.rcugrc.bpm.OperatorBean;
import com.hd.rcugrc.bpm.TimeDefintion;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <p>时间控制定义信息
 * 
 * <AUTHOR> href="mailto:<EMAIL>">yanglx</a>
 * @version 1.0, 2021年3月5日
 */
@Schema(title = "时间控制定义参数", description = "时间控制定义参数")
public class TimeConstraintDefinition implements Serializable {

    private static final long serialVersionUID = 1320984038480090232L;
    private TimeDefintion alarmTimeDefinition;
    private TimeDefintion dueTimeDefintion;
    private String messengerBeanIds;
    private OperatorBean[] notificationReceivers;
    
    private TimeDefintion[] alarmNotificationDefinition;
    private TimeDefintion[] dueNotificationDefinition;
    
    @Schema(description = "预警时间定义", example = "{\r\n"
            + "      \"dateValue\": \"1\",\r\n" 
            + "      \"dateUnit\": \"d\"\r\n"
            + "    }")
    public TimeDefintion getAlarmTimeDefinition() {
        return alarmTimeDefinition;
    }
    
    public void setAlarmTimeDefinition(TimeDefintion alarmTimeDefinition) {
        this.alarmTimeDefinition = alarmTimeDefinition;
    }
    
    @Schema(description = "超时时间定义", example = "{\r\n"
            + "      \"dateValue\": \"3\",\r\n" 
            + "      \"dateUnit\": \"d\"\r\n"
            + "    }")
    public TimeDefintion getDueTimeDefintion() {
        return dueTimeDefintion;
    }
    
    public void setDueTimeDefintion(TimeDefintion dueTimeDefintion) {
        this.dueTimeDefintion = dueTimeDefintion;
    }
    
    @Schema(description = "提醒方式", example = "hdWorkflowTodolistMessenger,hdCustomWorkflowTodolistMessenger")
    public String getMessengerBeanIds() {
        return messengerBeanIds;
    }
    
    public void setMessengerBeanIds(String messengerBeanIds) {
        this.messengerBeanIds = messengerBeanIds;
    }
    
    @Schema(description = "提醒接收人", example = "[\r\n"
            + "      {\r\n" 
            + "        \"type\": \"User\",\r\n"
            + "        \"code\": \"hd\",\r\n" 
            + "        \"name\": \"hd\"\r\n" 
            + "      }\r\n" 
            + "    ]")
    public OperatorBean[] getNotificationReceivers() {
        return notificationReceivers;
    }
    
    public void setNotificationReceivers(OperatorBean[] notificationReceivers) {
        this.notificationReceivers = notificationReceivers;
    }
    
    @Schema(description = "多次提醒预警定义", example = "{\r\n"
            + "        \"dateValue\": \"1\",\r\n"
            + "        \"dateUnit\": \"d\"\r\n" 
            + "      }")
    public TimeDefintion[] getAlarmNotificationDefinition() {
        return alarmNotificationDefinition;
    }
    
    public void setAlarmNotificationDefinition(TimeDefintion[] alarmNotificationDefinition) {
        this.alarmNotificationDefinition = alarmNotificationDefinition;
    }
    
    @Schema(description = "多次提醒超时定义", example = "{\r\n"
            + "        \"dateValue\": \"1\",\r\n"
            + "        \"dateUnit\": \"d\"\r\n" 
            + "      }")
    public TimeDefintion[] getDueNotificationDefinition() {
        return dueNotificationDefinition;
    }
    
    public void setDueNotificationDefinition(TimeDefintion[] dueNotificationDefinition) {
        this.dueNotificationDefinition = dueNotificationDefinition;
    }
}

