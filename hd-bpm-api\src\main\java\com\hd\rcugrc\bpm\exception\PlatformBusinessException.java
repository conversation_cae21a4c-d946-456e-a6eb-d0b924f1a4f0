/*
 * 北京慧点科技有限公司XCOA产品。
 * 注意：本内容仅限于内部传阅，禁止外泄以及用于其他的商业目的。
 * @Date: 2021年04月23日
 * @Copyright: Copyright 2020 © Smartdot Technologies Co., Ltd.
 */
package com.hd.rcugrc.bpm.exception;

import com.hd.rcugrc.bpm.tool.I18NUtils;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.core.NestedRuntimeException;
import org.springframework.http.HttpStatus;

/**
 * 业务异常，此异常表示业务异常，返回前端http码为500
 * code ： 异常编码，-1时表示使用后端返回的msg，！= -1 时表示前端定制提示内容
 * msg ： 错误提示，当code = -1时前端会使用此内容
 * errors ： 用于后端排除问题，可放堆栈 信息，前端不会使用此字段
 * context ： 当前端需要定制提示时，用于返回给前端一些关键信息，如流程id等
 * 
 * code默认-1表示需要前端无需定制提示内容，直接展示message中的内容，可使用前四个构造函数errors表示给后端排查问题使用的，可存堆栈异常等信息
 * 当需要前端定制提示内容时，需要使用后两个构造函数，并使code ！= -1， 前端将不读取message的内容，提示内容前端定制
 * <AUTHOR> href="mailto:<EMAIL>">lichao</a>
 * @version 1.0, 2022年06月17日
 */
public class PlatformBusinessException extends NestedRuntimeException {
    
    private static final long serialVersionUID = -6343814603125678675L;
    private int code = -1;
    private String errors;
    private Object[] args;
    private HttpStatus httpStatus = HttpStatus.INTERNAL_SERVER_ERROR;
    private String context;
    private boolean i18n = false;
    private boolean printStackTrace = true;
    
    public PlatformBusinessException(String msg) {
        super(msg);
    }
    
    public PlatformBusinessException(String msg, Object[] args) {
        super(msg);
        this.args = args;
    }
    
    public PlatformBusinessException(String msg, Throwable cause) {
        super(msg, cause);
    }
    
    public PlatformBusinessException(String msg, Object[] args, Throwable cause) {
        super(msg, cause);
        this.args = args;
    }
    
    public PlatformBusinessException(String msg, HttpStatus httpStatus, Throwable cause) {
        super(msg, cause);
        this.httpStatus = httpStatus;
    }
    
    public PlatformBusinessException(String msg, String errors) {
        super(msg);
        this.errors = errors;
    }
    
    public PlatformBusinessException(String msg, String errors, Object[] args) {
        super(msg);
        this.errors = errors;
        this.args = args;
    }
    
    public PlatformBusinessException(String msg, HttpStatus httpStatus, String errors) {
        super(msg);
        this.errors = errors;
        this.httpStatus = httpStatus;
    }
    
    public PlatformBusinessException(String msg, String errors, Throwable cause) {
        super(msg, cause);
        this.errors = errors;
    }
    
    public PlatformBusinessException(String msg, String errors, Object[] args, Throwable cause) {
        super(msg, cause);
        this.errors = errors;
        this.args = args;
    }
    
    public PlatformBusinessException(String msg, String errors, HttpStatus httpStatus, Throwable cause) {
        super(msg, cause);
        this.errors = errors;
        this.httpStatus = httpStatus;
    }
    
    public PlatformBusinessException(int code, String msg, String errors, String context) {
        super(msg);
        this.code = code;
        this.errors = errors;
        this.context = context;
    }
    
    public PlatformBusinessException(int code, String msg, String errors, String context, HttpStatus httpStatus) {
        super(msg);
        this.code = code;
        this.errors = errors;
        this.context = context;
        this.httpStatus = httpStatus;
    }
    
    public PlatformBusinessException(int code, String msg, String errors, String context, Throwable cause) {
        super(msg, cause);
        this.code = code;
        this.errors = errors;
        this.context = context;
    }
    
    public PlatformBusinessException(int code, String msg, String errors, String context, HttpStatus httpStatus, Throwable cause) {
        super(msg, cause);
        this.code = code;
        this.errors = errors;
        this.context = context;
        this.httpStatus = httpStatus;
    }
    
    public PlatformBusinessException(boolean i18n, String msg) {
        super(i18n ? I18NUtils.getMessage(msg, LocaleContextHolder.getLocale()) : msg);
        this.i18n = i18n;
    }
    
    public PlatformBusinessException(boolean i18n, String msg, Object[] args) {
        super(i18n ? I18NUtils.getMessage(msg, args, LocaleContextHolder.getLocale()) : msg);
        this.args = args;
        this.i18n = i18n;
    }
    
    public PlatformBusinessException(boolean i18n, String msg, Throwable cause) {
        super(i18n ? I18NUtils.getMessage(msg, LocaleContextHolder.getLocale()) : msg, cause);
        this.i18n = i18n;
    }
    
    public PlatformBusinessException(boolean i18n, String msg, Object[] args, Throwable cause) {
        super(i18n ? I18NUtils.getMessage(msg, args, LocaleContextHolder.getLocale()) : msg, cause);
        this.args = args;
        this.i18n = i18n;
    }
    
    public PlatformBusinessException(boolean i18n, String msg, HttpStatus httpStatus, Throwable cause) {
        super(i18n ? I18NUtils.getMessage(msg, LocaleContextHolder.getLocale()) : msg, cause);
        this.httpStatus = httpStatus;
        this.i18n = i18n;
    }
    
    public PlatformBusinessException(boolean i18n, String msg, String errors) {
        super(i18n ? I18NUtils.getMessage(msg, LocaleContextHolder.getLocale()) : msg);
        this.errors = errors;
        this.i18n = i18n;
    }
    
    public PlatformBusinessException(boolean i18n, String msg, String errors, Object[] args) {
        super(i18n ? I18NUtils.getMessage(msg, args, LocaleContextHolder.getLocale()) : msg);
        this.errors = errors;
        this.args = args;
        this.i18n = i18n;
    }
    
    public PlatformBusinessException(boolean i18n, String msg, HttpStatus httpStatus, String errors) {
        super(i18n ? I18NUtils.getMessage(msg, LocaleContextHolder.getLocale()) : msg);
        this.errors = errors;
        this.httpStatus = httpStatus;
        this.i18n = i18n;
    }
    
    public PlatformBusinessException(boolean i18n, String msg, String errors, Throwable cause) {
        super(i18n ? I18NUtils.getMessage(msg, LocaleContextHolder.getLocale()) : msg, cause);
        this.errors = errors;
        this.i18n = i18n;
    }
    
    public PlatformBusinessException(boolean i18n, String msg, String errors, Object[] args, Throwable cause) {
        super(i18n ? I18NUtils.getMessage(msg, args, LocaleContextHolder.getLocale()) : msg, cause);
        this.errors = errors;
        this.args = args;
        this.i18n = i18n;
    }
    
    public PlatformBusinessException(boolean i18n, String msg, String errors, HttpStatus httpStatus, Throwable cause) {
        super(i18n ? I18NUtils.getMessage(msg, LocaleContextHolder.getLocale()) : msg, cause);
        this.errors = errors;
        this.httpStatus = httpStatus;
        this.i18n = i18n;
    }
    
    public PlatformBusinessException(boolean i18n, int code, String msg, String errors, String context) {
        super(i18n ? I18NUtils.getMessage(msg, LocaleContextHolder.getLocale()) : msg);
        this.code = code;
        this.errors = errors;
        this.context = context;
        this.i18n = i18n;
    }
    
    public PlatformBusinessException(boolean i18n, int code, String msg, String errors, String context, HttpStatus httpStatus) {
        super(i18n ? I18NUtils.getMessage(msg, LocaleContextHolder.getLocale()) : msg);
        this.code = code;
        this.errors = errors;
        this.context = context;
        this.httpStatus = httpStatus;
        this.i18n = i18n;
    }
    
    public PlatformBusinessException(boolean i18n, int code, String msg, String errors, String context, Throwable cause) {
        super(i18n ? I18NUtils.getMessage(msg, LocaleContextHolder.getLocale()) : msg, cause);
        this.code = code;
        this.errors = errors;
        this.context = context;
        this.i18n = i18n;
    }
    
    public PlatformBusinessException(boolean i18n, int code, String msg, String errors, String context, HttpStatus httpStatus,
                                     Throwable cause) {
        super(i18n ? I18NUtils.getMessage(msg, LocaleContextHolder.getLocale()) : msg, cause);
        this.code = code;
        this.errors = errors;
        this.context = context;
        this.httpStatus = httpStatus;
        this.i18n = i18n;
    }
    
    public PlatformBusinessException(boolean i18n, boolean printStackTrace, String msg) {
        super(i18n ? I18NUtils.getMessage(msg, LocaleContextHolder.getLocale()) : msg);
        this.i18n = i18n;
        this.printStackTrace = printStackTrace;
    }
    
    public PlatformBusinessException(boolean i18n, boolean printStackTrace, String msg, Object[] args) {
        super(i18n ? I18NUtils.getMessage(msg, args, LocaleContextHolder.getLocale()) : msg);
        this.args = args;
        this.i18n = i18n;
        this.printStackTrace = printStackTrace;
    }
    
    public PlatformBusinessException(boolean i18n, boolean printStackTrace, String msg, Throwable cause) {
        super(i18n ? I18NUtils.getMessage(msg, LocaleContextHolder.getLocale()) : msg, cause);
        this.i18n = i18n;
        this.printStackTrace = printStackTrace;
    }
    
    public PlatformBusinessException(boolean i18n, boolean printStackTrace, String msg, Object[] args, Throwable cause) {
        super(i18n ? I18NUtils.getMessage(msg, args, LocaleContextHolder.getLocale()) : msg, cause);
        this.args = args;
        this.i18n = i18n;
        this.printStackTrace = printStackTrace;
    }
    
    public PlatformBusinessException(boolean i18n, boolean printStackTrace, String msg, HttpStatus httpStatus, Throwable cause) {
        super(i18n ? I18NUtils.getMessage(msg, LocaleContextHolder.getLocale()) : msg, cause);
        this.httpStatus = httpStatus;
        this.i18n = i18n;
        this.printStackTrace = printStackTrace;
    }
    
    public PlatformBusinessException(boolean i18n, boolean printStackTrace, String msg, String errors) {
        super(i18n ? I18NUtils.getMessage(msg, LocaleContextHolder.getLocale()) : msg);
        this.errors = errors;
        this.i18n = i18n;
        this.printStackTrace = printStackTrace;
    }
    
    public PlatformBusinessException(boolean i18n, boolean printStackTrace, String msg, String errors, Object[] args) {
        super(i18n ? I18NUtils.getMessage(msg, args, LocaleContextHolder.getLocale()) : msg);
        this.errors = errors;
        this.args = args;
        this.i18n = i18n;
        this.printStackTrace = printStackTrace;
    }
    
    public PlatformBusinessException(boolean i18n, boolean printStackTrace, String msg, HttpStatus httpStatus, String errors) {
        super(i18n ? I18NUtils.getMessage(msg, LocaleContextHolder.getLocale()) : msg);
        this.errors = errors;
        this.httpStatus = httpStatus;
        this.i18n = i18n;
        this.printStackTrace = printStackTrace;
    }
    
    public PlatformBusinessException(boolean i18n, boolean printStackTrace, String msg, String errors, Throwable cause) {
        super(i18n ? I18NUtils.getMessage(msg, LocaleContextHolder.getLocale()) : msg, cause);
        this.errors = errors;
        this.i18n = i18n;
        this.printStackTrace = printStackTrace;
    }
    
    public PlatformBusinessException(boolean i18n, boolean printStackTrace, String msg, String errors, Object[] args, Throwable cause) {
        super(i18n ? I18NUtils.getMessage(msg, args, LocaleContextHolder.getLocale()) : msg, cause);
        this.errors = errors;
        this.args = args;
        this.i18n = i18n;
        this.printStackTrace = printStackTrace;
    }
    
    public PlatformBusinessException(boolean i18n, boolean printStackTrace, String msg, String errors, HttpStatus httpStatus,
                                     Throwable cause) {
        super(i18n ? I18NUtils.getMessage(msg, LocaleContextHolder.getLocale()) : msg, cause);
        this.errors = errors;
        this.httpStatus = httpStatus;
        this.i18n = i18n;
        this.printStackTrace = printStackTrace;
    }
    
    public PlatformBusinessException(boolean i18n, boolean printStackTrace, int code, String msg, String errors, String context) {
        super(i18n ? I18NUtils.getMessage(msg, LocaleContextHolder.getLocale()) : msg);
        this.code = code;
        this.errors = errors;
        this.context = context;
        this.i18n = i18n;
        this.printStackTrace = printStackTrace;
    }
    
    public PlatformBusinessException(boolean i18n, boolean printStackTrace, int code, String msg, String errors, String context,
                                     HttpStatus httpStatus) {
        super(i18n ? I18NUtils.getMessage(msg, LocaleContextHolder.getLocale()) : msg);
        this.code = code;
        this.errors = errors;
        this.context = context;
        this.httpStatus = httpStatus;
        this.i18n = i18n;
        this.printStackTrace = printStackTrace;
    }
    
    public PlatformBusinessException(boolean i18n, boolean printStackTrace, int code, String msg, String errors, String context,
                                     Throwable cause) {
        super(i18n ? I18NUtils.getMessage(msg, LocaleContextHolder.getLocale()) : msg, cause);
        this.code = code;
        this.errors = errors;
        this.context = context;
        this.i18n = i18n;
        this.printStackTrace = printStackTrace;
    }
    
    public PlatformBusinessException(boolean i18n, boolean printStackTrace, int code, String msg, String errors, String context,
                                     HttpStatus httpStatus, Throwable cause) {
        super(i18n ? I18NUtils.getMessage(msg, LocaleContextHolder.getLocale()) : msg, cause);
        this.code = code;
        this.errors = errors;
        this.context = context;
        this.httpStatus = httpStatus;
        this.i18n = i18n;
        this.printStackTrace = printStackTrace;
    }
    
    public PlatformBusinessException(boolean i18n, boolean printStackTrace, int code, String msg, Object[] args, String errors, String context,
                                     HttpStatus httpStatus, Throwable cause) {
        super(i18n ? I18NUtils.getMessage(msg, args, LocaleContextHolder.getLocale()) : msg, cause);
        this.code = code;
        this.errors = errors;
        this.context = context;
        this.httpStatus = httpStatus;
        this.i18n = i18n;
        this.printStackTrace = printStackTrace;
        this.args = args;
    }
    
    public int getCode() {
        return code;
    }
    
    public void setCode(int code) {
        this.code = code;
    }
    
    public String getErrors() {
        return errors;
    }
    
    public void setErrors(String errors) {
        this.errors = errors;
    }
    
    public String getContext() {
        return context;
    }
    
    public void setContext(String context) {
        this.context = context;
    }
    
    public HttpStatus getHttpStatus() {
        return httpStatus;
    }
    
    public void setHttpStatus(HttpStatus httpStatus) {
        this.httpStatus = httpStatus;
    }
    
    public Object[] getArgs() {
        return args;
    }
    
    public void setArgs(Object[] args) {
        this.args = args;
    }
    
    public boolean isI18n() {
        return i18n;
    }
    
    public void setI18n(boolean i18n) {
        this.i18n = i18n;
    }
    
    public boolean isPrintStackTrace() {
        return printStackTrace;
    }
    
    public void setPrintStackTrace(boolean printStackTrace) {
        this.printStackTrace = printStackTrace;
    }
    
//    @Override
//    public String getMessage() {
//        String message = super.getMessage();
//        if (i18n && I18NUtils.getApplicationContext() != null) {
//            message = I18NUtils.getMessageSourceAccessor().getMessage(message, this.args == null ? new Object[0] :  this.args,
//                    LocaleContextHolder.getLocale());
//        }
//        return message;
//    }
}
