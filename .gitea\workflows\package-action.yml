name: 代码打包
run-name: ${{ gitea.actor }} 代码打包
on:
  push:
    branches:
      - test


jobs:
  package:
    runs-on: ubuntu-latest
    container: dockerhub.gbu.smartdot.com/gbu_custom/gitlib-runner-java-node
    steps:
      - name: 事件信息
        run: |
          echo "${{ gitea.event_name }} 事件触发自动执行任务。"
          echo "分支名称：${{ gitea.ref }}"
          echo "仓库名称：${{ gitea.repository }}."
      - name: 检出代码
        uses: actions/checkout@v4
      - name: 打包
        run: |
          chmod +x gradlew
          ./gradlew buildProject
          cp -r wep/build/webapp docker/app
          docker login dockerhub.gbu.smartdot.com -u admin -p Harbor12345
          docker pull dockerhub.gbu.smartdot.com/gbu_custom/openjdk:8-jdk-alpine-cn
          cd docker && docker build --build-arg packageTime="$(date +'%Y-%m-%d %H:%M:%S')" -t dockerhub.gbu.smartdot.com/gbu/projects/${{gitea.repository_owner}}/xcoa-boot:${{gitea.ref_name}}-${{gitea.sha}} .
          docker push dockerhub.gbu.smartdot.com/gbu/projects/${{gitea.repository_owner}}/xcoa-boot:${{gitea.ref_name}}-${{gitea.sha}}
          echo "镜像推送完成 dockerhub.gbu.smartdot.com/gbu/projects/${{gitea.repository_owner}}/xcoa-boot:${{gitea.ref_name}}-${{gitea.sha}}"
      - name: 更新
        run: 'curl -X PUT -H "content-type: application/json" -H "Cookie: KuboardUsername=admin; KuboardAccessKey=${{vars.K8S_ACCESS_TOKEN}}" -d "{\"kind\":\"deployments\",\"namespace\":\"default\",\"name\":\"${{gitea.repository_owner}}-boot\",\"images\":{\"dockerhub.gbu.smartdot.com/gbu/projects/${{gitea.repository_owner}}/xcoa-boot\":\"dockerhub.gbu.smartdot.com/gbu/projects/${{gitea.repository_owner}}/xcoa-boot:${{gitea.ref_name}}-${{gitea.sha}}\"}}" "${{vars.K8S_URL}}"'
