/*
 * Copyright 2013-2020 Smartdot Technologies Co., Ltd. All rights reserved.
 * SMARTDOT PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 */
package com.hd.rcugrc.bpm.facade.form.service;

import com.hd.rcugrc.bpm.ActiveStep;
import com.hd.rcugrc.bpm.FlowDefinition;
import com.hd.rcugrc.bpm.facade.RollbackStep;
import com.hd.rcugrc.bpm.facade.form.TaskButton;

import java.util.Map;

/**
 * <p>
 * 
 * <AUTHOR> href="mailto:<EMAIL>">pandy</a>
 * @version 1.0, 2020年7月15日
 */
public interface RejectTaskButtonService {

    /**
     * 
     * @param instId
     * @param flow
     * @param activeStep
     * @return
     */
    public TaskButton[] getRejectTaskButtons(long instId, FlowDefinition flow, ActiveStep activeStep);

    /**
     *
     * @param instId
     * @param flow
     * @param activeStep
     * @return
     */
    public TaskButton[] getRejectTaskButtons(long instId, FlowDefinition flow, ActiveStep activeStep, Map<String, Object> context);

    /**
     * 
     * @param instId
     * @param activeStepId
     * @return
     */
    public RollbackStep[] getRejectSteps(long instId, long activeStepId);

    /**
     * @param instId
     * @param activeStepId
     * @return
     */
    public RollbackStep[] getRejectSteps(long instId, long activeStepId, Map<String, Object> context);

    /**
     * 
     * @param instId
     * @param activeStepId
     * @param rollbackable
     * @param ignoreInterfere
     * @return
     */
    public RollbackStep[] getRejectSteps(long instId, long activeStepId, boolean rollbackable, boolean ignoreInterfere);
}

