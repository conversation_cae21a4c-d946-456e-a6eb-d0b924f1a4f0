/*
 * Copyright 2013-2019 Smartdot Technologies Co., Ltd. All rights reserved.
 * SMARTDOT PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 */
package com.hd.rcugrc.bpm.facade.form.service;

import java.util.List;
import java.util.Map;

import com.hd.rcugrc.bpm.facade.form.ProcessFormFieldValue;
import com.hd.rcugrc.bpm.facade.form.util.ProcessFormFieldUtil;
import com.hd.rcugrc.form.dao.model.impl.ModelFieldInfoBean;
import com.hd.rcugrc.page.Control;

/**
 * <p>
 * 流程页面控件属性读取，主要读取流程页面控件对应绑定的字段、属性值
 * 
 * <AUTHOR> href="mailto:<EMAIL>">songjj</a>
 * @version 5.4, 2019年7月2日
 */
public interface ProcessFormFieldReadService {

    /**
     * 获取控件字段属性已经属性值
     * 
     * @param formField      表单字段
     * @param control        控件信息
     * @param bean           实体Bean
     * @param mode           模式
     * 
     *                       <pre>
     * 编辑 {@link ProcessFormFieldUtil#FORM_MODEL_EDIT} 返回表单字段以及值外，还返回字段附加信息 如下拉框信息
     * 只读 {@link ProcessFormFieldUtil#FORM_MODEL_VIEW} 返回表单字段以及值 并且为只读模式
     * 送阅 {@link ProcessFormFieldUtil#FORM_MODEL_VIEWTOREAD} 返回表单字段以及值 并且为只读模式,部分送阅字段可编辑
     * 沟通 {@link ProcessFormFieldUtil#FORM_MODEL_VIEWCOMM}  返回表单字段以及值 并且为只读模式,部分沟通字段可编辑
     * 申请 {@link ProcessFormFieldUtil#FORM_MODEL_VIEWAPP} 返回表单字段以及值 并且为只读模式,部分申请字段可编辑
     *                       </pre>
     * 
     * @param allowFields    允许操作字段ID
     * @param requiredFields 必填操作字段ID
     * @param context        上下文
     * @return 表单字段属性值
     */
    public ProcessFormFieldValue getProcessFormFieldValue(ModelFieldInfoBean formField, Control control, Object bean,
            List<String> allowFields, List<String> requiredFields, String mode, Map<String, Object> context);

}
