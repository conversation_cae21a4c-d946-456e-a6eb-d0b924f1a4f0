FROM dockerhub.gbu.smartdot.com/gbu_custom/openjdk:8-jdk-alpine-cn
LABEL maintainer=gbu
ARG packageTime
ENV packageTime=$packageTime
# 内存参数
ENV JAVA_MEM_OPTS=-Xmx2048m
# 激活的profile
ENV ACTIVE_PROFILE=redis_default,workflow_save_default,security_web,redis_codec_jdk,k8s
# 其他附加的java参数
ENV JAVA_EXTRA_OPTS=
# 附加的classpath
ENV CLASSPATH=
#主程序
ENV MAIN_CLASS=org.springframework.boot.loader.WarLauncher
#所依赖的主机及服务
ENV DEPEND_SERVERS=sd-redis:6379,sd-db:5236
ENV DEPEND_SERVER_TIMEOUT=30
#日志输出路径
ENV LOG_PATH=/var/log/grcv5/
ENV LOG_FILE=/var/log/grcv5/hd.log
#日志属性
ENV LOG_OPTS=
  
WORKDIR /opt/app
COPY wait-for-it.sh /usr/local/bin/
COPY docker-entrypoint.sh /usr/local/bin/
COPY app ./
RUN chmod +x /usr/local/bin/docker-entrypoint.sh
RUN chmod +x /usr/local/bin/wait-for-it.sh
ENTRYPOINT ["docker-entrypoint.sh"]

EXPOSE 8888