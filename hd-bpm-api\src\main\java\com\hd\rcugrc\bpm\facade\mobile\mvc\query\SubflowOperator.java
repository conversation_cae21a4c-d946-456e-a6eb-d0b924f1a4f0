/*
 * Copyright 2013-2019 Smartdot Technologies Co., Ltd. All rights reserved.
 * SMARTDOT PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 */
package com.hd.rcugrc.bpm.facade.mobile.mvc.query;

import java.io.Serializable;

import com.hd.rcugrc.bpm.OperatorBean;

import io.swagger.v3.oas.annotations.media.Schema;


/**
 * <p>
 * 多处理人并行子流程环节待减签操作者信息 ，主要用于restful 接口减签参数
 *
 * <AUTHOR> href="mailto:<EMAIL>">songjj</a>
 * @version 5.3, 2019年5月10日
 */
@Schema(title = "subflowOperator", description = "多处理人并行子流程环节待减签操作者信息,主要用于多处理人子流程减签使用，"
        + "主要包含子流程实例ID以及该子流程对应的操作者")
public class SubflowOperator implements Serializable {
    
    private static final long serialVersionUID = 7292394745046223869L;
    
    private long subflowInstId;
    
    private OperatorBean operator;
    
    /**
     * 返回子流程实例ID
     *
     * @return 子流程实例ID
     */
    @Schema(required = true, description = "子流程实例Id", example = "2")
    public long getSubflowInstId() {
        return subflowInstId;
    }
    
    /**
     * 设置子流程实例ID
     *
     * @param subflowInstId 子流程实例ID
     */
    public void setSubflowInstId(long subflowInstId) {
        this.subflowInstId = subflowInstId;
    }
    
    /**
     * 返回待减签操作者
     *
     * @return 待减签操作者
     */
    @Schema(required = true, description = "子流程实例Id对应操作者")
    public OperatorBean getOperator() {
        return operator;
    }
    
    /**
     * 设置待减签操作者
     *
     * @param operator 待减签操作者
     */
    public void setOperator(OperatorBean operator) {
        this.operator = operator;
    }
}
