/*
 * Copyright 2013-2019 Smartdot Technologies Co., Ltd. All rights reserved.
 * SMARTDOT PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 */
package com.hd.rcugrc.bpm.facade.mobile.mvc.query;

import java.io.Serializable;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <p>
 * 用户信息
 *
 * <AUTHOR> href="mailto:<EMAIL>">songjj</a>
 * @version 5.4, 2019年7月5日
 */
@Schema(title = "UserTaskCountInfo", description = "用户待办数量、关注数量、待回复沟通数量等")
public class UserTaskCountInfo implements Serializable {
    
    private static final long serialVersionUID = 4495997919683305633L;
    private long id;
    private String account;
    private String name;
    private int todoNumbers = 0;
    private int toReadNumbers = 0;
    private int toCommunicateNumbers = 0;
    private int toAttentionNumbers = 0;
    // 办结数量
    private int completedNumbers = 0;
    private int processedNumbers = 0;
    private int reminderNumbers = 0; //返回催办数量
    private int sysMessageNumbers = 0; //返回系统提醒数量
    private int readNumbers = 0; //返回系统提醒数量
    
    /**
     * @return 待回复沟通数量
     */
    @Schema(title = "待回复沟通数量", description = "待回复沟通数量", example = "10")
    public int getToCommunicateNumbers() {
        return toCommunicateNumbers;
    }
    
    /**
     * @param toCommunicateNumbers 待回复沟通数量
     */
    public void setToCommunicateNumbers(int toCommunicateNumbers) {
        this.toCommunicateNumbers = toCommunicateNumbers;
    }
    
    /**
     * @return 关注数量
     */
    @Schema(title = "关注数量", description = "关注数量", example = "10")
    public int getToAttentionNumbers() {
        return toAttentionNumbers;
    }
    
    /**
     * @param toAttentionNumbers 关注数量
     */
    public void setToAttentionNumbers(int toAttentionNumbers) {
        this.toAttentionNumbers = toAttentionNumbers;
    }
    
    /**
     * @return 待办数量
     */
    @Schema(title = "待办数量", description = "待办数量", example = "10")
    public int getTodoNumbers() {
        return todoNumbers;
    }
    
    /**
     * 设置待办数量
     *
     * @param todoNumbers 待办数量
     */
    public void setTodoNumbers(int todoNumbers) {
        this.todoNumbers = todoNumbers;
    }
    
    /**
     * @return 待阅数量
     */
    @Schema(title = "待阅数量", description = "待阅数量", example = "10")
    public int getToReadNumbers() {
        return toReadNumbers;
    }
    
    /**
     * 设置待阅数量
     *
     * @param toReadNumbers 待阅数量
     */
    public void setToReadNumbers(int toReadNumbers) {
        this.toReadNumbers = toReadNumbers;
    }
    
    
    /**
     * @return 用户ID
     */
    @Schema(title = "用户ID", description = "用户ID", example = "10")
    public long getId() {
        return id;
    }
    
    /**
     * 设置用户ID
     *
     * @param id 用户ID
     */
    public void setId(long id) {
        this.id = id;
    }
    
    /**
     * @return 账户
     */
    @Schema(title = "账户", description = "账户", example = "tester")
    public String getAccount() {
        return account;
    }
    
    /**
     * @param account 设置 账户
     */
    public void setAccount(String account) {
        this.account = account;
    }
    
    
    /**
     * @return 用户姓名
     */
    @Schema(title = "用户姓名", description = "用户姓名", example = "测试用户")
    public String getName() {
        return name;
    }
    
    /**
     * 设置用户姓名
     *
     * @param name 用户姓名
     */
    public void setName(String name) {
        this.name = name;
    }
    
    @Schema(title = "办结数量", description = "办结数量", example = "1")
    public int getCompletedNumbers() {
        return completedNumbers;
    }
    
    public void setCompletedNumbers(int completedNumbers) {
        this.completedNumbers = completedNumbers;
    }
    
    @Schema(title = "已办数量", description = "已办数量", example = "1")
    public int getProcessedNumbers() {
        return processedNumbers;
    }
    
    public void setProcessedNumbers(int processedNumbers) {
        this.processedNumbers = processedNumbers;
    }
    
    @Schema(title = "催办数量", description = "催办数量", example = "1")
    public int getReminderNumbers() {
        return reminderNumbers;
    }
    
    public void setReminderNumbers(int reminderNumbers) {
        this.reminderNumbers = reminderNumbers;
    }
    
    @Schema(title = "系统提醒数量", description = "系统提醒数量", example = "1")
    public int getSysMessageNumbers() {
        return sysMessageNumbers;
    }
    
    public void setSysMessageNumbers(int sysMessageNumbers) {
        this.sysMessageNumbers = sysMessageNumbers;
    }
    
    @Schema(title = "已阅数量", description = "已阅数量", example = "1")
    public int getReadNumbers() {
        return readNumbers;
    }
    
    public void setReadNumbers(int readNumbers) {
        this.readNumbers = readNumbers;
    }
}
