/*
 * Copyright 2013-2022 Smartdot Technologies Co., Ltd. All rights reserved.
 * SMARTDOT PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */
package com.hd.rcugrc.bpm.facade;

import java.io.Serializable;

import org.joda.time.DateTime;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <p>工作日历信息
 *
 * <AUTHOR> href="mailto:<EMAIL>">bimw</a>
 * @version 1.0, 2022-04-13
 */
@Schema(title = "workCalendarInfo", description = "工作日历信息")
public class WorkCalendarInfo implements Serializable {

    private static final long serialVersionUID = 7974905527864152885L;
    private Long id;
    private String calendarCode;
    private String calendarName; //
    private String includeYears; //日历包含的年份; 逗号分割; 注意: 和items的内容无关
    private String orgCode; //所属机构编码
    private String orgName; //所属机构名称

    private String creatorCode; //创建人编码
    private String creatorName; //创建人名称
    private DateTime creationTime; // 创建时间

    private String modifierCode; //编辑人编码
    private String modifierName; //编辑人名称
    private DateTime modifiedTime; // 最后修改时间

    private String description; //描述
    private Integer enable; // 是否启用
    private Integer deleted; // 是否删除

    @Schema(description = "id值", example = "1")
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @Schema(description = "日历编码", example = "default")
    public String getCalendarCode() {
        return calendarCode;
    }

    public void setCalendarCode(String calendarCode) {
        this.calendarCode = calendarCode;
    }

    @Schema(description = "日历名称", example = "默认日历")
    public String getCalendarName() {
        return calendarName;
    }

    public void setCalendarName(String calendarName) {
        this.calendarName = calendarName;
    }

    @Schema(description = "日历包含的年份; 逗号分割", example = "2022")
    public String getIncludeYears() {
        return includeYears;
    }

    public void setIncludeYears(String includeYears) {
        this.includeYears = includeYears;
    }

    @Schema(description = "所属机构编码", example = "smartdot")
    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    @Schema(description = "所属机构名称", example = "慧点科技")
    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    @Schema(description = "创建人账号", example = "hd")
    public String getCreatorCode() {
        return creatorCode;
    }

    public void setCreatorCode(String creatorCode) {
        this.creatorCode = creatorCode;
    }

    @Schema(description = "创建人名称", example = "hd")
    public String getCreatorName() {
        return creatorName;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }

    @Schema(description = "创建时间")
    public DateTime getCreationTime() {
        return creationTime;
    }

    public void setCreationTime(DateTime creationTime) {
        this.creationTime = creationTime;
    }

    @Schema(description = "修改人账号", example = "zhangsan")
    public String getModifierCode() {
        return modifierCode;
    }

    public void setModifierCode(String modifierCode) {
        this.modifierCode = modifierCode;
    }

    @Schema(description = "修改人名称", example = "张三")
    public String getModifierName() {
        return modifierName;
    }

    public void setModifierName(String modifierName) {
        this.modifierName = modifierName;
    }

    @Schema(description = "最后修改时间")
    public DateTime getModifiedTime() {
        return modifiedTime;
    }

    public void setModifiedTime(DateTime modifiedTime) {
        this.modifiedTime = modifiedTime;
    }

    @Schema(description = "描述", example = "描述信息")
    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @Schema(description = "是否启用", example = "1")
    public Integer getEnable() {
        return enable;
    }

    public void setEnable(Integer enable) {
        this.enable = enable;
    }

    @Schema(description = "是否删除", example = "0")
    public Integer getDeleted() {
        return deleted;
    }

    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }
}
