package com.hd.rcugrc.bpm.facade.mvc.query;

import java.io.Serializable;

import io.swagger.v3.oas.annotations.media.Schema;


/**
 * 表单域的值
 */
@Schema(title = "表单域的值", description = "主用用于接收表单内参数以及参数值")
public class FormFieldValue implements Serializable {
    private static final long serialVersionUID = 9219739064406719780L;
    private String name;
    private String value;
    
    /**
     * 返回表单域的名字
     *
     * @return 表单域的名字
     */
    @Schema(title = "表单域的名字", description = "表单域的名字", example = "name")
    public String getName() {
        return name;
    }
    
    /**
     * 设置表单域的名字
     *
     * @param name 表单域的名字
     */
    public void setName(String name) {
        this.name = name;
    }
    
    /**
     * 返回表单域的值
     *
     * @return 表单域的值
     */
    @Schema(title = "表单域的值", description = "表单域的值", example = "testname")
    public String getValue() {
        return value;
    }
    
    /**
     * 设置表单域的值
     *
     * @param value 表单域的值
     */
    public void setValue(String value) {
        this.value = value;
    }
    
    
}
