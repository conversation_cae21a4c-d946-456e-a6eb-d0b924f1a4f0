/*
 * Copyright 2013-2019 Smartdot Technologies Co., Ltd. All rights reserved.
 * SMARTDOT PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 */
package com.hd.rcugrc.bpm.facade.form;

import java.io.Serializable;
import java.util.Map;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <p>
 * 流程表单字段以及字段值信息
 * 
 * <AUTHOR> href="mailto:<EMAIL>">songjj</a>
 * @version 5.4, 2019年7月2日
 */
@Schema(title = "流程页面表单字段以及字段值信息", description = "流程页面表单字段以及字段值信息")
public class ProcessFormData implements Serializable {

    private static final long serialVersionUID = -3050247403664552974L;

    private String pageId;

    private String formId;

    private String beanId;

    private String mode;

    private ProcessFormFieldValue[] processFormPropertyValues;

    // 在线自定义表单扩展属性
    private Map<String, Object> attr;
    // 在线自定义表单前端属性
    private Map<String, Object> attrFD;

    /**
     * @return 页面ID
     */
    @Schema(title = "页面ID", description = "页面ID", example = "testPage")
    public String getPageId() {
        return pageId;
    }

    /**
     * 设置页面Id
     * 
     * @param pageId 页面Id
     */
    public void setPageId(String pageId) {
        this.pageId = pageId;
    }

    /**
     * @return the formId 表单Id
     */
    @Schema(title = "表单Id", description = "表单Id", example = "model:testForm")
    public String getFormId() {
        return formId;
    }

    /**
     * 设置 表单Id
     * 
     * @param formId 表单Id
     */
    public void setFormId(String formId) {
        this.formId = formId;
    }

    /**
     * 设置模式
     * 
     * @return 模式
     */
    @Schema(title = "模式", description = "模式", example = "EDIT")
    public String getMode() {
        return mode;
    }

    /**
     * @param mode 模式
     * 
     *             <pre>
     *  EDIT 编辑
     *  VIEW 查看
     *             </pre>
     */
    public void setMode(String mode) {
        this.mode = mode;
    }

    /**
     * @return 页面上表单控件列表
     */
    @Schema(title = "页面上表单控件列表", description = "页面上表单控件列表")
    public ProcessFormFieldValue[] getProcessFormPropertyValues() {
        return processFormPropertyValues;
    }

    /**
     * 设置页面上表单控件列表
     * 
     * @param processFormPropertyValues 页面上表单控件列表
     */

    public void setProcessFormPropertyValues(ProcessFormFieldValue[] processFormPropertyValues) {
        this.processFormPropertyValues = processFormPropertyValues;
    }

    /**
     * @return the beanId
     */
    public String getBeanId() {
        return beanId;
    }

    /**
     * @param beanId the beanId to set
     */
    public void setBeanId(String beanId) {
        this.beanId = beanId;
    }

    @Schema(title = "onl表单扩展属性", description = "onl表单扩展属性", example = "{}")
    public Map<String, Object> getAttr() {
        return attr;
    }

    public void setAttr(Map<String, Object> attr) {
        this.attr = attr;
    }

    /**
     * @return the attrFD onl表单前端属性
     */
    @Schema(title = "onl表单前端属性", description = "onl表单前端属性", example = "{}")
    public Map<String, Object> getAttrFD() {
        return attrFD;
    }

    public void setAttrFD(Map<String, Object> attrFD) {
        this.attrFD = attrFD;
    }

}
