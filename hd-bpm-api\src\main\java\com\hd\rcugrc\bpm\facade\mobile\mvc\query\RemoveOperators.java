/*
 * Copyright 2013-2020 Smartdot Technologies Co., Ltd. All rights reserved.
 * SMARTDOT PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 */
package com.hd.rcugrc.bpm.facade.mobile.mvc.query;

import java.io.Serializable;
import java.util.Map;

import com.hd.rcugrc.bpm.facade.StepOperator;

import com.hd.rcugrc.bpm.facade.mvc.query.FormFieldValue;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <p> 流程实例活动环节减签参数，主要用于Restful接口
 *
 * <AUTHOR> href="mailto:<EMAIL>">yanglx</a>
 * @version 1.0, 2020年10月15日
 */
@Schema(title = "removeOperators", description = "流程实例活动环节减签参数,流程实例活动环节减签,"
        + "以下example表示： 当前用户为hd，将该流程实例中并行处理人列表中删除用户tjUser")
public class RemoveOperators implements Serializable {
    
    private static final long serialVersionUID = 123024938438535L;
    
    private long instId;
    
    private long activeStepId;
    
    private StepOperator[] stepOperators;
    
    private Map<String, String> props;
    
    private String beanIdProperty;
    
    private String beanTitleProperty;
    
    private String beanIdType;
    
    private Map<String, String> bean;
    
    private FormFieldValue[] inputs;
    
    /**
     * 返回流程实例id
     *
     * @return 流程实例id
     */
    @Schema(title = "当前流程实例ID", required = true, description = "当前流程实例ID", example = "100")
    public long getInstId() {
        return instId;
    }
    
    /**
     * 设置流程实例ID
     *
     * @param instId 流程实例ID
     */
    public void setInstId(long instId) {
        this.instId = instId;
    }
    
    /**
     * @return the activeStepId
     */
    @Schema(title = "当前环节ID，如果不指定，则为当前环节数据的第一个", description = "当前环节ID，如果不指定，则为当前环节数据的第一个", example = "100")
    public long getActiveStepId() {
        return activeStepId;
    }
    
    /**
     * @param activeStepId the activeStepId to set
     */
    public void setActiveStepId(long activeStepId) {
        this.activeStepId = activeStepId;
    }
    
    @Schema(title = "流程实例待减签人员列表", description = "流程实例待减签人员列表", required = true, nullable = false,
            example = "[\r\n"
                    + "        {\r\n"
                    + "            \"stepOperatorId\": 488,\r\n"
                    + "            \"operators\": [\r\n"
                    + "                {\r\n"
                    + "                    \"attrs\": {},\r\n"
                    + "                    \"code\": \"test2\",\r\n"
                    + "                    \"name\": \"test2\",\r\n"
                    + "                    \"type\": \"User\"\r\n"
                    + "                }\r\n"
                    + "            ]\r\n"
                    + "        },\r\n"
                    + "        {\r\n"
                    + "            \"stepOperatorId\": 489,\r\n"
                    + "            \"operators\": [\r\n"
                    + "                {\r\n"
                    + "                    \"attrs\": {},\r\n"
                    + "                    \"code\": \"test1\",\r\n"
                    + "                    \"name\": \"test1\",\r\n"
                    + "                    \"type\": \"User\"\r\n"
                    + "                }\r\n"
                    + "            ]\r\n"
                    + "        }\r\n"
                    + "    ]")
    public StepOperator[] getStepOperators() {
        return stepOperators;
    }
    
    /**
     * 设置减签人员列表
     *
     * @param stepOperators
     */
    public void setStepOperators(StepOperator[] stepOperators) {
        this.stepOperators = stepOperators;
    }
    
    @Schema(title = "扩展属性，可以通过扩展属性来标记数据来源， 可以为空",
            description = "扩展属性，可以通过扩展属性来标记数据来源， 可以为空", nullable = true,
            example = "{\"source\":\"mobile\"}")
    public Map<String, String> getProps() {
        return props;
    }
    
    /**
     * 设置存储表单数据实体bean的扩展属性
     *
     * @param props
     */
    public void setProps(Map<String, String> props) {
        this.props = props;
    }
    
    /**
     * 返回实体bean主键ID属性名称
     *
     * @return 实体bean主键ID属性名称
     * <p>
     * 根据该属性获取对应实体Bean中的主键ID值使用，如果bean为空，要从inputs中获取
     * </p>
     */
    @Schema(title = "实体bean主键ID属性名称，根据该属性获取对应实体Bean中的主键ID值使用，如果bean为空，要从inputs中获取",
            description = "实体bean主键ID属性名称，根据该属性获取对应实体Bean中的主键ID值使用，如果bean为空，要从inputs中获取", example = "id")
    public String getBeanIdProperty() {
        return beanIdProperty;
    }
    
    /**
     * 设置实体bean主键ID属性名称
     *
     * @param beanIdProperty 实体bean主键ID属性名称
     */
    public void setBeanIdProperty(String beanIdProperty) {
        this.beanIdProperty = beanIdProperty;
    }
    
    /**
     * 返回实体bean中作为流程标题属性名称
     *
     * @return 实体bean中作为流程标题属性名称
     */
    @Schema(title = " 实体bean中作为流程标题属性名称，根据该属性获取对应实体Bean中的Title值使用，如果bean为空，要从inputs中获取",
            description = "实体bean中作为流程标题属性名称，根据该属性获取对应实体Bean中的Title值使用，如果bean为空，要从inputs中获取",
            example = "title")
    public String getBeanTitleProperty() {
        return beanTitleProperty;
    }
    
    /**
     * 设置实体bean中作为流程标题属性名称
     *
     * @param beanTitleProperty 实体bean中作为流程标题属性名称
     */
    public void setBeanTitleProperty(String beanTitleProperty) {
        this.beanTitleProperty = beanTitleProperty;
    }
    
    /**
     * 返回实体bean主键ID值类型
     *
     * @return 实体bean主键ID值类型
     * <p>
     * 目前仅支持long,string
     * </p>
     */
    @Schema(title = "实体bean主键ID值类型，目前仅支持long,string",
            description = "实体bean主键ID值类型，目前仅支持long,string", example = "long")
    public String getBeanIdType() {
        return beanIdType;
    }
    
    /**
     * 设置实体bean主键ID值类型
     *
     * @param beanIdType 实体bean主键ID值类型
     */
    public void setBeanIdType(String beanIdType) {
        this.beanIdType = beanIdType;
    }
    
    /**
     * 返回实体bean
     *
     * @return 存储表单数据实体bean
     */
    @Schema(title = "存储表单数据实体bean，至少主键ID以及标题对应属性有值", description = "存储表单数据实体bean，至少主键ID以及标题对应属性有值",
            example = "{\"id\":100,\r\n\"name\":\"日常办公用品报销\",\r\n" + "\"title\":\"2019-04 日常办公用品报销\"\r\n" + "}")
    public Map<String, String> getBean() {
        return bean;
    }
    
    /**
     * 设置实体bean
     *
     * @param bean 设置存储表单数据实体bean
     */
    public void setBean(Map<String, String> bean) {
        this.bean = bean;
    }
    
    /**
     * 返回表单中所有参数
     *
     * @return 表单中所有参数
     * <p>此参数主要是页面表单中所有参数，可以为空，平台只对此参数转发</p>
     */
    @Schema(description = "表单中所有参数,此参数主要是页面表单中所有参数，可以为空，平台只对此参数转发，注：意见信息需要存放到属性opinions，正文附件需要存储到zwDoc"
            + "属性中，附件信息需要存放到atahGroups属性中", required = false,
            example = "[{\"name\":\"bxName\",\"value\":\"办公用品报销\"},"
                    + "{\"name\":\"sxmz2\",\"value\":\"{\\\"signUserName\\\":\\\"小赵\\\","
                    + "\\\"signDeptName\\\":\\\"慧点科技\\\",\\\"signContent\\\":\\\"测试意见，办公用品报销,同意此次报销\\\","
                    + "\\\"signDate\\\":1593322952361}\"},"
                    + "{\"name\":\"pageLink\",\"value\":\"http://*************:18080/thirdDemoWeb/page/bxForm"
                    + ".jsp?id=100&instId=@{inst.id}&activeStepId=@{activeStep.id}\"}]")
    public FormFieldValue[] getInputs() {
        return inputs;
    }
    
    /**
     * @param inputs 设置表单中参数
     */
    public void setInputs(FormFieldValue[] inputs) {
        this.inputs = inputs;
    }
}
