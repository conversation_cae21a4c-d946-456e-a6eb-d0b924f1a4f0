/*
 * Copyright 2013-2020 Smartdot Technologies Co., Ltd. All rights reserved.
 * SMARTDOT PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 */
package com.hd.rcugrc.bpm.facade.form.service;

import org.joda.time.DateTime;

/**
 * <p> 办理时长设置接口
 * 
 * <AUTHOR> href="mailto:<EMAIL>">yanglx</a>
 * @version 1.0, 2020年10月12日
 */
public interface ProcessTimeDataHandlerService {
    
    /***
     * 设置办理时长
     * @param workCalendarCode 工作日历编码
     * @param startDate 开始时间
     * @param endDate 结束时间
     */
    public String appendProcessTime(String workCalendarCode, DateTime startDateMillis, 
            DateTime endDateMillis) throws Exception;
}

