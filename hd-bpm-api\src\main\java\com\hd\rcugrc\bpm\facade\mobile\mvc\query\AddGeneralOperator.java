/*
 * Copyright 2013-2019 Smartdot Technologies Co., Ltd. All rights reserved.
 * SMARTDOT PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 */
package com.hd.rcugrc.bpm.facade.mobile.mvc.query;

import java.io.Serializable;
import java.util.Map;

import com.hd.rcugrc.bpm.facade.mvc.query.FormFieldValue;

import io.swagger.v3.oas.annotations.media.Schema;


/**
 * <p>
 * 流程实例活动环节加签参数，主要用于Restful接口
 *
 * <AUTHOR> href="mailto:<EMAIL>">songjj</a>
 * @version 5.3, 2019年4月10日
 */
@Schema(title = "addOperator", description = "流程实例活动环节加签参数,流程实例活动环节加签,"
        + "  formId 表单ID  包含协议前缀 如推荐：非平台表单  tj:testForm；平台表单  model:testForm \r\n" + " 如果是非平台表单，则还需要包含\r\n"
        + " bean 实体bean对象，该bean方式设置方法 有两种  \r\n" + "   方式一：调用此方法时直接通过SaveFlowInstance设置  \r\n"
        + "   方式二：使用接口{@link com.hd.rcugrc.bpm.facade.service.FormHandlerService}实现类中方法 "
        + "handlerFormBeforeFlowStateChange中进行赋值\r\n"
        + " <b>如果bean 为空则将 formParams中的inputs 参数作为bean</b>\r\n" + "  beanTitleProperty 实体bean对象中作为标题字段属性 \r\n"
        + "  beanIdProperty 实体bean对象中作为主键ID字段属性 \r\n"
        + "以下example表示：表单formId为tj:tjbx， 当前用户为hd，将该流程实例中并行处理人列表中增加用户tjUser")
public class AddGeneralOperator implements Serializable {
    
    /**
     *
     */
    private static final long serialVersionUID = -1659179817282061368L;
    
    private long instId;
    
    private FlowInstanceCaller caller;
    
    private String formId;
    
    private FormFieldValue[] inputs;
    
    private String beanIdProperty;
    
    private String beanTitleProperty;
    
    private String beanIdType;
    
    private Map<String, String> bean;
    
    private String flowCallbackBeanName;
    
    private AddOperatorInfo[] addOperatorInfos;
    
    private Map<String, String> props;
    
    /**
     * 返回实体bean主键ID值类型
     *
     * @return 实体bean主键ID值类型
     * <p>
     * 目前仅支持long,string
     * </p>
     */
    @Schema(title = "实体bean主键ID值类型，目前仅支持long,string", description = "实体bean主键ID值类型，目前仅支持long,string", example = "long")
    public String getBeanIdType() {
        return beanIdType;
    }
    
    /**
     * 设置实体bean主键ID值类型
     *
     * @param beanIdType 实体bean主键ID值类型
     */
    public void setBeanIdType(String beanIdType) {
        this.beanIdType = beanIdType;
    }
    
    /**
     * 返回实体bean主键ID属性名称
     *
     * @return 实体bean主键ID属性名称
     * <p>
     * 根据该属性获取对应实体Bean中的主键ID值使用，如果bean为空，要从inputs中获取
     * </p>
     */
    @Schema(title = "实体bean主键ID属性名称，根据该属性获取对应实体Bean中的主键ID值使用，如果bean为空，要从inputs中获取",
            description = "实体bean主键ID属性名称，根据该属性获取对应实体Bean中的主键ID值使用，如果bean为空，要从inputs中获取", example = "id")
    public String getBeanIdProperty() {
        return beanIdProperty;
    }
    
    /**
     * 设置实体bean主键ID属性名称
     *
     * @param beanIdProperty 实体bean主键ID属性名称
     */
    public void setBeanIdProperty(String beanIdProperty) {
        this.beanIdProperty = beanIdProperty;
    }
    
    /**
     * 返回实体bean中作为流程标题属性名称
     *
     * @return 实体bean中作为流程标题属性名称
     */
    @Schema(title = " 实体bean中作为流程标题属性名称，根据该属性获取对应实体Bean中的Title值使用，如果bean为空，要从inputs中获取",
            description = " 实体bean中作为流程标题属性名称，根据该属性获取对应实体Bean中的Title值使用，如果bean为空，要从inputs中获取",
            example = "title")
    public String getBeanTitleProperty() {
        return beanTitleProperty;
    }
    
    /**
     * 设置实体bean中作为流程标题属性名称
     *
     * @param beanTitleProperty 实体bean中作为流程标题属性名称
     */
    public void setBeanTitleProperty(String beanTitleProperty) {
        this.beanTitleProperty = beanTitleProperty;
    }
    
    /**
     * 返回实体bean
     *
     * @return 存储表单数据实体bean
     */
    @Schema(title = "存储表单数据实体bean，至少主键ID以及标题对应属性有值",
            description = "存储表单数据实体bean，至少主键ID以及标题对应属性有值",
            example = "{\"id\":100,\r\n\"name\":\"日常办公用品报销\",\r\n" + "\"title\":\"2019-04 日常办公用品报销\"\r\n" + "}")
    public Map<String, String> getBean() {
        return bean;
    }
    
    /**
     * 设置实体bean
     *
     * @param bean 设置存储表单数据实体bean
     */
    public void setBean(Map<String, String> bean) {
        this.bean = bean;
    }
    
    /**
     * 返回流程实例id
     *
     * @return 流程实例id
     */
    
    @Schema(title = "当前流程实例ID", description = "当前流程实例ID", required = true, example = "100")
    public long getInstId() {
        return instId;
    }
    
    /**
     * 设置流程实例ID
     *
     * @param instId 流程实例ID
     */
    public void setInstId(long instId) {
        this.instId = instId;
    }
    
    /**
     * 返回流程实例创建者
     *
     * @return 流程实例创建者
     */
    @Schema(title = "流程实例创建者,一般都是当前登录人， 如果是第三方系统建议和GRCv5进行同步系统用户",
            description = "流程实例创建者,一般都是当前登录人， 如果是第三方系统建议和GRCv5进行同步系统用户",
            required = true, example = "      \"caller\": {\r\n"
            + "           \"userName\": \"慧点人员\",\r\n" + "           \"userAccount\": \"hd\"\r\n"
            + "                   }")
    public FlowInstanceCaller getCaller() {
        return caller;
    }
    
    /**
     * 设置流程实例创建者
     *
     * @param caller 流程实例创建者
     */
    public void setCaller(FlowInstanceCaller caller) {
        this.caller = caller;
    }
    
    /**
     * 返回 表单id
     *
     * @return 表单id
     */
    @Schema(required = false, description = "主要用于表单与流程关联使用，用于生成流程待办列表使用 表单ID 分为两部分 ,"
            + "这两部分以‘:’分割，‘：’前面的表示协议，‘:’后面的是具体使用的表单ID"
            + "如果是GRCv5平台，则表单规则为model:xxx ,如：model:testForm,\r\n" + "如果非GRCv5平台，则表单规则可以为tj:xxx 或者 **:xxx ,\r\n"
            + "tj:xxx在平台有一套相应的处理规则，如： tj:tjBxForm,\r\n"
            + "而 **:xxx需要项目自行按照tj相关类改造,**.xxx 样例为sf:contractForm \r\n", example = "tj:tjbx")
    public String getFormId() {
        return formId;
    }
    
    /**
     * 设置表单ID
     *
     * @param formId 表单ID
     *               <p>
     *               表单ID 分为两部分 ,这两部分以‘:’分割，‘：’前面部分表示协议，‘:’后面部分是具体使用的表单ID
     *               </p>
     */
    public void setFormId(String formId) {
        this.formId = formId;
    }
    
    /**
     * 返回表单中所有参数
     *
     * @return 表单中所有参数
     */
    @Schema(title = "表单中所有参数，此参数主要是页面表单中所有参数，可以为空，pageLink参数用于待办展示第三方页面使用， 系统会自动将@{inst.id}替换成当前流程实例ID，@{activeStep"
            + ".id}替换成活动环节ID，另平台不对参数修改，仅转发使用",
            description = "表单中所有参数，此参数主要是页面表单中所有参数，可以为空，pageLink参数用于待办展示第三方页面使用， 系统会自动将@{inst.id}替换成当前流程实例ID，@{activeStep"
                    + ".id}替换成活动环节ID，另平台不对参数修改，仅转发使用", required = false, example = "[{\"name\":\"bxName\","
            + "\"value\":\"办公用品报销\"},"
            + "{\"name\":\"sxmz2\",\"value\":\"{\\\"signUserName\\\":\\\"小赵\\\",\\\"signDeptName\\\":\\\"慧点科技\\\","
            + "\\\"signContent\\\":\\\"测试意见，办公用品报销,同意此次报销\\\",\\\"signDate\\\":1593322952361}\"},"
            + "{\"name\":\"pageLink\",\"value\":\"http://*************:18080/thirdDemoWeb/page/bxForm"
            + ".jsp?id=100&instId=@{inst.id}&activeStepId=@{activeStep.id}\"}]")
    public FormFieldValue[] getInputs() {
        return inputs;
    }
    
    /**
     * 设置表单中参数
     *
     * @param inputs 表单中参数
     */
    public void setInputs(FormFieldValue[] inputs) {
        this.inputs = inputs;
    }
    
    /**
     * 返回流程回调类名称，如果是给子流程加签则不需要设置
     *
     * @return 流程回调类名称
     * <p>
     * 需要在流程引擎中相关xml中注册，这里是实际注册beanId,该bean需要实现接口com.hd.rcugrc.bpm.FlowCallback
     * </p>
     */
    @Schema(title = "流程回调类名称，需要在流程引擎中相关xml中注册，这里是实际注册beanId，该bean需要实现接口com.hd.rcugrc.bpm"
            + ".FlowCallback，可以为空，该样例是在主流程中创建子流程中需回调处理子流程和表单关系",
            description = "流程回调类名称，需要在流程引擎中相关xml中注册，这里是实际注册beanId，该bean需要实现接口com.hd.rcugrc.bpm"
                    + ".FlowCallback，可以为空，该样例是在主流程中创建子流程中需回调处理子流程和表单关系",
            required = false, example = "subflowDataBinderCallback")
    public String getFlowCallbackBeanName() {
        return flowCallbackBeanName;
    }
    
    /**
     * 设置流程回调类名称
     *
     * @param flowCallbackBeanName 流程回调类名称，需要在流程引擎中相关xml中注册，这里是实际注册beanId,该bean需要实现接口com.hd.rcugrc.bpm.FlowCallback
     */
    public void setFlowCallbackBeanName(String flowCallbackBeanName) {
        this.flowCallbackBeanName = flowCallbackBeanName;
    }
    
    /**
     * 加签环节的信息
     * 需要加签的环节
     * 需要加的处理人
     *
     * @return the addOperatorInfos
     */
    public AddOperatorInfo[] getAddOperatorInfos() {
        return addOperatorInfos;
    }
    
    /**
     * @param addOperatorInfos the addOperatorInfos to set
     */
    public void setAddOperatorInfos(AddOperatorInfo[] addOperatorInfos) {
        this.addOperatorInfos = addOperatorInfos;
    }
    
    /**
     * 获取存储表单数据实体bean的扩展属性
     *
     * @return
     */
    @Schema(title = "扩展属性，可以通过扩展属性来标记数据来源， 可以为空", description = "扩展属性，可以通过扩展属性来标记数据来源， 可以为空", required = false,
            example = "{\"source\":\"mobile\"}")
    public Map<String, String> getProps() {
        return props;
    }
    
    /**
     * 设置存储表单数据实体bean的扩展属性
     *
     * @param props
     */
    public void setProps(Map<String, String> props) {
        this.props = props;
    }
}
