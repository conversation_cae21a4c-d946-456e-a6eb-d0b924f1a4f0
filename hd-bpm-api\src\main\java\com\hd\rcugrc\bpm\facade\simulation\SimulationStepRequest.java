/**
 * Copyright 2013-2020 Smartdot Technologies Co., Ltd. All rights reserved.
 * SMARTDOT PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 */
package com.hd.rcugrc.bpm.facade.simulation;

import java.io.Serializable;

import com.hd.bpm.engine.runtime.User;
import com.hd.bpm.engine.simulation.ConditionData;
import com.hd.bpm.engine.simulation.StepUser;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 单步仿真请求定义
 * 
 * <AUTHOR> href="mailto:<EMAIL>">victor mu</a>
 * @version 1.0, 2021年8月16日
 */
@Schema(title = "simulationStepRequest", description = "单步仿真请求定义")
public class SimulationStepRequest implements Serializable {
    private static final long serialVersionUID = -1234568696122186655L;

    @Schema(title = "流程id", required = true, nullable = false,
            description = "流程id")
    private String flowId;
    
    @Schema(title = "节点编号", required = true, nullable = false,
            description = "流程定义中的节点id")
    private String stepCode;
    
    @Schema(title = "实例id", required = true, nullable = false,
            description = "实例id -无实例id时传 -1", example = "-1")
    private long instId;
    
    @Schema(title = "当前节点id", required = true, nullable = false,
            description = "当前节点id -无当前节点id时传 -1", example = "-1")    
    private long activeStepId;
    
    @Schema(title = "下一步操作id", required = false, nullable = true,
            description = "下一步操作id 可空")    
    private String nextActionId;
        
    @Schema(title = "子流程首节点下一步操作id", required = false, nullable = true,
            description = "子流程首节点下一步操作id 当前节点为子流程且子流程首节点后续为多个操作需要传入选定操作，其余情况可空")    
    private String nextSubActionId;
    
    @Schema(title = "处理人", required = true, nullable = false,
            description = "处理人")    
    private StepUser[] user; 
    
    @Schema(title = "处理人集合", required = true, nullable = false,
            description = "处理人集合-范围判定用")    
    private User[] userScope;
    
    @Schema(title = "判定条件", required = false, nullable = true,
            description = "判定条件 可空")    
    private ConditionData[] conditions;
    
    public String getFlowId() {
        return flowId;
    }
    public void setFlowId(String flowId) {
        this.flowId = flowId;
    }
    public String getStepCode() {
        return stepCode;
    }
    public void setStepCode(String stepCode) {
        this.stepCode = stepCode;
    }
    public long getInstId() {
        return instId;
    }
    public void setInstId(long instId) {
        this.instId = instId;
    }
    public long getActiveStepId() {
        return activeStepId;
    }
    public void setActiveStepId(long activeStepId) {
        this.activeStepId = activeStepId;
    }
    public String getNextActionId() {
        return nextActionId;
    }
    public void setNextActionId(String nextActionId) {
        this.nextActionId = nextActionId;
    }
    public String getNextSubActionId() {
        return nextSubActionId;
    }
    public void setNextSubActionId(String nextSubActionId) {
        this.nextSubActionId = nextSubActionId;
    }
    public StepUser[] getUser() {
        return user;
    }
    public void setUser(StepUser[] user) {
        this.user = user;
    }
    public User[] getUserScope() {
        return userScope;
    }
    public void setUserScope(User[] userScope) {
        this.userScope = userScope;
    }
    public ConditionData[] getConditions() {
        return conditions;
    }
    public void setConditions(ConditionData[] conditions) {
        this.conditions = conditions;
    }

}
