/*
 * Copyright 2013-2021 Smartdot Technologies Co., Ltd. All rights reserved.
 * SMARTDOT PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 */
package com.hd.rcugrc.bpm.facade.form.service;

import java.util.List;
import java.util.Map;

import com.hd.rcugrc.bpm.ActionDefinition;
import com.hd.rcugrc.bpm.ActiveStep;
import com.hd.rcugrc.bpm.FlowDefinition;
import com.hd.rcugrc.bpm.FlowInstance;
import com.hd.rcugrc.bpm.StepDefinition;
import com.hd.rcugrc.bpm.User;

/**
 * <p>
 * 
 * <AUTHOR> href="mailto:<EMAIL>">pandy</a>
 * @version 1.0, 2021年6月18日
 */
public interface PackageNextStepButtonsExtendService {

    /**
     * 
     * @param flowDefinition
     * @param stepDefinition
     * @param inst
     * @param activeStep
     * @param actions
     * @param caller
     * @param actionIdPrefix
     * @param actionlist
     * @param context
     * @return
     */
    public boolean haveOtherOperator(FlowDefinition flowDefinition, StepDefinition stepDefinition, FlowInstance inst,
            ActiveStep activeStep, ActionDefinition actions, User caller, Object actionIdPrefix,
            List<String[]> actionlist, Map<String, Object> context);
}

