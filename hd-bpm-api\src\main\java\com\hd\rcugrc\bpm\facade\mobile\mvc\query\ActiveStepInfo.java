/*
 * Copyright 2013-2019 Smartdot Technologies Co., Ltd. All rights reserved.
 * SMARTDOT PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 *
 */
package com.hd.rcugrc.bpm.facade.mobile.mvc.query;

import java.io.Serializable;

import io.swagger.v3.oas.annotations.media.Schema;


/**
 * <p>
 * 当前流程活动相关信息，主要用于Restful接口
 *
 * <AUTHOR> href="mailto:<EMAIL>">songjj</a>
 * @version 5.3, 2019年4月10日
 */
@Schema(title = "activeStepInfo", description = "当前流程活动相关信息")
public class ActiveStepInfo implements Serializable {
    /**
     *
     */
    private static final long serialVersionUID = -3309501282930194390L;
    
    private long instId;
    
    private long activeStepId;
    
    /**
     * @return 流程实例ID
     */
    @Schema(title = "流程实例ID", description = "流程实例ID", example = "1")
    public long getInstId() {
        return instId;
    }
    
    /**
     * @param instId 设置流程实例ID
     */
    public void setInstId(long instId) {
        this.instId = instId;
    }
    
    /**
     * 获取流程实例活动环节ID
     *
     * @return 流程实例活动环节ID
     */
    @Schema(title = "流程实例活动环节ID", description = "流程实例活动环节ID")
    public long getActiveStepId() {
        return activeStepId;
    }
    
    /**
     * 设置流程实例活动环节ID
     *
     * @param activeStepId 流程实例活动环节ID
     */
    public void setActiveStepId(long activeStepId) {
        this.activeStepId = activeStepId;
    }
    
}

